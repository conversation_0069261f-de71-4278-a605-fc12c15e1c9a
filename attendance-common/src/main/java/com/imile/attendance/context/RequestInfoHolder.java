package com.imile.attendance.context;

import com.alibaba.fastjson.JSON;
import com.imile.rpc.utils.InvisibleParamsUtils;
import com.imile.ucenter.api.authenticate.UcenterUtils;
import com.imile.util.user.UserEvnHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 请求信息ThreadLocal工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class RequestInfoHolder {

    private RequestInfoHolder() {
    }

    private static final String TOKEN = "token";
    private static final String DEVICE_ID = "deviceId";
    private static final String REMOTE_IP = "remoteIp";
    private static final String RESOURCE_LOCAL = "resourceLocal";

    private static ThreadLocal<UserContext> userContext = new InheritableThreadLocal<>();

    private static ThreadLocal<String> token = new InheritableThreadLocal<>();

    private static ThreadLocal<String> deviceId = new InheritableThreadLocal<>();

    private static ThreadLocal<String> remoteIp = new InheritableThreadLocal<>();

    private static ThreadLocal<String> resourceLocal = new InheritableThreadLocal<>();



    private static UserContext getLoginInfoFromRpc() {
        String userInfo = InvisibleParamsUtils.get("userInfo");
        if (StringUtils.isNotEmpty(userInfo)) {
            return JSON.parseObject(userInfo, UserContext.class);
        }
        return null;
    }

    public static Optional<UserContext> getLoginInfoNullable() {
        // 优先去已经存在的,否则去远程调用的
        if (userContext.get() != null) {
            return Optional.of(userContext.get());
        }
        UserContext loginInfoFromRpc = getLoginInfoFromRpc();
        if (loginInfoFromRpc != null) {
            userContext.set(loginInfoFromRpc);
            return Optional.of(userContext.get());
        }
        return Optional.empty();
    }

    public static UserContext getLoginInfo() {
        return getLoginInfoNullable().orElse(null);
    }



    public static void setLoginInfo(UserContext user) {
        if (getLoginInfoNullable().isPresent()) {
            log.warn("用户上下文信息已经存在,setLoginInfo将覆盖原有用户上下文信息!");
        }
        userContext.set(user);
    }

    public static void setOrgId(Long orgId) {
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (userContext == null) {
            userContext = new UserContext();
        }
        userContext.setOrgId(orgId);
        RequestInfoHolder.setLoginInfo(userContext);
    }

    public static String getUserCode() {
        return getLoginInfoNullable().map(UserContext::getUserCode).orElse(null);
    }

    public static Long getUserId() {
        return getLoginInfoNullable().map(UserContext::getId).orElse(null);
    }

    public static String getUserName() {
        return getLoginInfoNullable().map(UserContext::getUserName).orElse(null);
    }

    public static String getUserNameEn() {
        return getLoginInfoNullable().map(UserContext::getUserNameEn).orElse(null);
    }

    public static String getUserNameFull() {
        String userNameEn = getUserNameEn();
        String userName = getUserName();
        return StringUtils.isNotEmpty(userNameEn) ?
                (StringUtils.isNotEmpty(userName) ? userNameEn + "(" + userName + ")" : userNameEn) : userName;
    }

    public static Long getOrgId() {
        return getLoginInfoNullable().map(UserContext::getOrgId).orElse(null);
    }


    public static void setPath(String p) {
        UserEvnHolder.setPath(p);
    }

    public static String getPath() {
        return UserEvnHolder.getPath();
    }

    public static Optional<String> getPathNullable() {
        return Optional.ofNullable(UserEvnHolder.getPath());
    }

    public static void setLocale(String localeStr) {
        UserEvnHolder.setLocale(localeStr);
    }

    public static void setLocale(Locale locale) {
        UserEvnHolder.setLocale(locale);
    }

    public static Locale getLocale() {
        return UserEvnHolder.getLocal();
    }

    public static Optional<Locale> getLocaleNullable() {
        return Optional.ofNullable(UserEvnHolder.getLocal());
    }

    public static String getLocaleTxt() {
        return UserEvnHolder.getLocalStr();
    }

    public static Optional<String> getLocaleTxtNullable() {
        return Optional.of(UserEvnHolder.getLocalStr());
    }

    public static void setTimeZone(String timeZone) {
        UserEvnHolder.setTimeZone(timeZone);
    }

    public static void setTimeZone(TimeZone timeZone) {
        UserEvnHolder.setTimeZone(timeZone);
    }

    public static TimeZone getTimeZone() {
        return UserEvnHolder.getTimeZone();
    }

    public static Optional<TimeZone> getTimeZoneNullable() {
        return Optional.ofNullable(UserEvnHolder.getTimeZone());
    }

    public static String getTimeZoneTxt() {
        return UserEvnHolder.getTimeZoneStr();
    }

    public static Optional<String> getTimeZoneTxtNullable() {
        return Optional.of(UserEvnHolder.getTimeZoneStr());
    }

    public static void setResourceCode(String resourceCode) {
        resourceLocal.set(resourceCode);
    }

    public static String getResourceCode() {
        return resourceLocal.get();
    }

    public static int getTimeZoneOffset() {
        // 这里默认返回了8时区
        return getTimeZoneNullable().map(TimeZone::getRawOffset).map(offset -> offset / 3600000).orElse(8);
    }

    public static String getDeviceId() {
        if (deviceId.get() != null) {
            return deviceId.get();
        }
        String deviceIdStr = InvisibleParamsUtils.get(DEVICE_ID);
        if (StringUtils.isNotEmpty(deviceIdStr)) {
            deviceId.set(deviceIdStr);
            return deviceId.get();
        }
        return null;
    }

    public static void setDeviceId(String localDeviceId) {
        if (getDeviceId() != null) {
            log.warn("上下文设备id已经存在,setDeviceId将覆盖原有上下文设备id!");
        }
        deviceId.set(localDeviceId);
    }

    public static String getRemoteIp() {
        if (remoteIp.get() != null) {
            return remoteIp.get();
        }
        String remoteIpStr = InvisibleParamsUtils.get(REMOTE_IP);
        if (StringUtils.isNotEmpty(remoteIpStr)) {
            remoteIp.set(remoteIpStr);
            return remoteIp.get();
        }
        return null;
    }

    public static void setRemoteIp(String ip) {
        if (getRemoteIp() != null) {
            log.warn("上下文远程ip信息已经存在,setRemoteIp将覆盖原有上下文远程ip信息!");
        }
        remoteIp.set(ip);
    }

    public static void remove() {
        userContext.remove();
        token.remove();
        UserEvnHolder.remove();
        deviceId.remove();
        remoteIp.remove();
        resourceLocal.remove();
    }


    public static Map<String, String> meta() {
        Map<String, String> meta = new ConcurrentHashMap<>();
        meta.put("userContext", RequestInfoHolder.getLoginInfoNullable().map(JSON::toJSONString).orElse(""));
        meta.put(TOKEN, StringUtils.defaultString(UcenterUtils.getUToken()));
        meta.put(DEVICE_ID, StringUtils.defaultString(RequestInfoHolder.getDeviceId()));
        meta.put(REMOTE_IP, StringUtils.defaultString(RequestInfoHolder.getRemoteIp()));
        meta.put(RESOURCE_LOCAL, StringUtils.defaultString(RequestInfoHolder.getResourceCode()));
        return meta;
    }


    public static void fromMeta(Map<String, String> meta) {
        if (meta == null) {
            return;
        }

        String userContextStr = meta.get("userContext");
        if (StringUtils.isNotEmpty(userContextStr)) {
            RequestInfoHolder.setLoginInfo(JSON.parseObject(userContextStr, UserContext.class));
        }
        if (StringUtils.isNotEmpty(meta.get(TOKEN))) {
            UcenterUtils.setUToken(meta.get(TOKEN));
        }
        if (StringUtils.isNotEmpty(meta.get(DEVICE_ID))) {
            RequestInfoHolder.setDeviceId(meta.get(DEVICE_ID));
        }
        if (StringUtils.isNotEmpty(meta.get(REMOTE_IP))) {
            RequestInfoHolder.setRemoteIp(meta.get(REMOTE_IP));
        }
        if (StringUtils.isNotEmpty(meta.get(RESOURCE_LOCAL))) {
            RequestInfoHolder.setResourceCode(meta.get(RESOURCE_LOCAL));
        }
    }

    public static boolean isChinese() {
        return "zh_CN".equals(getLocaleTxt());
    }

    public static String langType() {
        return getLocaleTxt() == null ? "" : getLocaleTxt();
    }

}
