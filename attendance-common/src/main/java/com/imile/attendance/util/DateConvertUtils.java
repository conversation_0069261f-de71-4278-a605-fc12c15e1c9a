package com.imile.attendance.util;

import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 时间转换工具类
 */
public class DateConvertUtils {

    public static final String FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String DEFAULT_SEPARATOR = "~";
    public static final String FORMAT_DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    public static final String FORMAT_HH_MM_SS = "HH:mm:ss";
    /**
     * !!注意这个8时区
     */
    private static final ZoneId SYS_ZONE_ID = ZoneId.of("GMT+8");

    public static Date toDate(LocalDateTime time) {
        return time == null ? null : Date.from(time.atZone(SYS_ZONE_ID).toInstant());
    }

    public static Date toDate(LocalDate localDate) {
        return localDate == null ? null : Date.from(localDate.atStartOfDay(SYS_ZONE_ID).toInstant());
    }


    public static LocalDateTime toLocalDateTime(Date time) {
        return time == null ? null : LocalDateTime.ofInstant(time.toInstant(), SYS_ZONE_ID);
    }

    /**
     * 生成时间周期,
     * 示例
     * 输入2021-01-01 00:00:00,2021-01-01 23:59:59  -> 2021-01-01
     * 输入2021-01-01 00:00:00,2021-01-02 23:59:59  -> 2021-01-01~2021-01-02
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static String generateDateCycle(Date startTime, Date endTime) {
        return generateDateCycle(startTime, endTime, FORMAT_YYYY_MM_DD);
    }

    /**
     * 生成时间周期
     *
     * @param startTime
     * @param endTime
     * @param format
     * @return
     */
    public static String generateDateCycle(Date startTime, Date endTime, String format) {
        String formatTmp = StringUtils.isBlank(format) ? FORMAT_YYYY_MM_DD : format;
        String startTimeStr = DateUtil.format(startTime, formatTmp);
        String endTimeStr = DateUtil.format(endTime, formatTmp);
        // 如果开始时间与结束时间在同一天，则返回开始时间即可
        if (Objects.equals(startTimeStr, endTimeStr) || StringUtils.isBlank(endTimeStr)) {
            return startTimeStr;
        }
        if (StringUtils.isBlank(startTimeStr)) {
            return endTimeStr;
        }
        return startTimeStr + DEFAULT_SEPARATOR + endTimeStr;
    }


    /**
     * 获取当天最大时间
     *
     * @param date yyyy-MM-dd
     * @return
     */
    public static Date getMaxTimeByDate(String date) throws ParseException {
        String regex = "((19|20)[0-9]{2})-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])";
        boolean isOk = Pattern.compile(regex).matcher(date).matches();
        if (!isOk) {
            return null;
        }
        String dateTime = date + " 23:59:59";
        SimpleDateFormat df = new SimpleDateFormat(FORMAT_DATE_TIME);
        return df.parse(dateTime);
    }

    /**
     * 获取当天最小时间
     *
     * @param date yyyy-MM-dd
     * @return
     */
    public static Date getMinTimeByDate(String date) throws ParseException {
        String regex = "((19|20)[0-9]{2})-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])";
        boolean isOk = Pattern.compile(regex).matcher(date).matches();
        if (!isOk) {
            return null;
        }
        String dateTime = date + " 00:00:00";
        SimpleDateFormat df = new SimpleDateFormat(FORMAT_DATE_TIME);
        return df.parse(dateTime);
    }


    public static Date getMaxTime(Date date) throws ParseException {
        String dateTimeStr = DateUtil.format(date, FORMAT_YYYY_MM_DD);
        String dateTime = dateTimeStr + " 23:59:59";
        SimpleDateFormat df = new SimpleDateFormat(FORMAT_DATE_TIME);
        return df.parse(dateTime);
    }

    public static Date getMinTime(Date date) throws ParseException {
        String dateTimeStr = DateUtil.format(date, FORMAT_YYYY_MM_DD);
        String dateTime = dateTimeStr + " 00:00:00";
        SimpleDateFormat df = new SimpleDateFormat(FORMAT_DATE_TIME);
        return df.parse(dateTime);
    }

    public static String getFormatHhMmSs(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        SimpleDateFormat timeFormat = new SimpleDateFormat(FORMAT_HH_MM_SS);
        return timeFormat.format(date);
    }
}
