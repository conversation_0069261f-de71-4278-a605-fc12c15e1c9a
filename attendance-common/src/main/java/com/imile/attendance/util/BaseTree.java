package com.imile.attendance.util;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BaseTree<T,N> implements Serializable {
    private static final long serialVersionUID = -3991033099403453171L;
    /**
     * 节点ID
     */
    private T id;
    /**
     * 标题
     */
    private String nodeName;
    /**
     * 组织编码
     */
    @Deprecated
    private String organizationCode;
    /**
     * 父节点ID
     */
    private T parentId;
    /**
     * 是否子节点
     */
    private boolean hasChild;
    /**
     * 树结构中访问路径
     */
    private List<T> path;
    /**
     * 子节点
     */
    private List<N> children;
}
