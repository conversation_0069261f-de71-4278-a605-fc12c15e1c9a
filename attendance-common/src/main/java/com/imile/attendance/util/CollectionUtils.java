package com.imile.attendance.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
public class CollectionUtils {

    public static <R, T> T convertSingle(R originData, Class<T> clazz) {
        if (originData == null) {
            return null;
        }

        // 方案一：fastJson转换
        // 方案二：遍历，使用BeanUtil
        return JSONArray.parseObject(JSONObject.toJSONString(originData), clazz);
    }

    public static <R, T> List<T> convert(List<R> originDataList, Class<T> clazz) {
        if (originDataList == null || originDataList.isEmpty()) {
            return new ArrayList<>();
        }
        // 方案一：fastJson转换
        // 方案二：遍历，使用BeanUtil
        return JSONArray.parseArray(JSONObject.toJSONString(originDataList), clazz);
    }
}
