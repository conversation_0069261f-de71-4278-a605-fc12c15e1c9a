package com.imile.attendance.util;

import com.imile.attendance.base.BaseDO;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.ucenter.api.authenticate.UcenterUtils;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import io.micrometer.core.instrument.util.StringUtils;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description
 */
public class BaseDOUtil {

    public static final String SYSTEM_OPERATE = "system";

    /**
     * 填充DO公共值
     *
     * @param baseDO
     */
    public static void fillDOUpdate(BaseDO baseDO) {
        baseDO.setLastUpdDate(new Date());
        baseDO.setLastUpdUserCode(RequestInfoHolder.getUserCode());
        baseDO.setLastUpdUserName(RequestInfoHolder.getUserName());
    }

    /**
     * 填充DO公共值，系统填充
     */
    public static void fillDOUpdateBySystem(BaseDO baseDO) {
        baseDO.setLastUpdDate(new Date());
        baseDO.setLastUpdUserCode(SYSTEM_OPERATE);
        baseDO.setLastUpdUserName(SYSTEM_OPERATE);
    }

    public static void fillDOUpdateByUserOrSystem(BaseDO baseDO) {
        baseDO.setLastUpdDate(new Date());
        String userCode = RequestInfoHolder.getUserCode();
        if (StringUtils.isNotEmpty(userCode)) {
            baseDO.setLastUpdUserCode(userCode);
            baseDO.setLastUpdUserName(RequestInfoHolder.getUserName());
            return;
        }
        baseDO.setLastUpdUserCode(SYSTEM_OPERATE);
        baseDO.setLastUpdUserName(SYSTEM_OPERATE);
    }

    /**
     * 填充DO公共值
     *
     * @param baseDO
     */
    public static void fillDOInsert(BaseDO baseDO) {
        baseDO.setCreateDate(new Date());
        baseDO.setCreateUserCode(RequestInfoHolder.getUserCode());
        baseDO.setCreateUserName(RequestInfoHolder.getUserName());
        baseDO.setLastUpdDate(baseDO.getCreateDate());
        baseDO.setLastUpdUserCode(RequestInfoHolder.getUserCode());
        baseDO.setLastUpdUserName(RequestInfoHolder.getUserName());
        baseDO.setIsDelete(IsDeleteEnum.NO.getCode());
        baseDO.setRecordVersion(1L);
    }

    public static void fillDOInsertBySystem(BaseDO baseDO) {
        baseDO.setCreateDate(new Date());
        baseDO.setCreateUserCode(SYSTEM_OPERATE);
        baseDO.setCreateUserName(SYSTEM_OPERATE);
        baseDO.setLastUpdDate(baseDO.getCreateDate());
        baseDO.setLastUpdUserCode(SYSTEM_OPERATE);
        baseDO.setLastUpdUserName(SYSTEM_OPERATE);
        baseDO.setIsDelete(IsDeleteEnum.NO.getCode());
        baseDO.setRecordVersion(1L);
    }

    public static void fillDOInsertByUsrOrSystem(BaseDO baseDO) {
        baseDO.setCreateDate(new Date());
        String userCode = RequestInfoHolder.getUserCode();
        if (StringUtils.isNotEmpty(userCode)) {
            baseDO.setCreateUserCode(userCode);
            baseDO.setCreateUserName(RequestInfoHolder.getUserName());
            baseDO.setLastUpdDate(baseDO.getCreateDate());
            baseDO.setLastUpdUserCode(userCode);
            baseDO.setLastUpdUserName(RequestInfoHolder.getUserName());
            baseDO.setIsDelete(IsDeleteEnum.NO.getCode());
            baseDO.setRecordVersion(1L);
            return;
        }
        baseDO.setCreateUserCode(SYSTEM_OPERATE);
        baseDO.setCreateUserName(SYSTEM_OPERATE);
        baseDO.setLastUpdDate(baseDO.getCreateDate());
        baseDO.setLastUpdUserCode(SYSTEM_OPERATE);
        baseDO.setLastUpdUserName(SYSTEM_OPERATE);
        baseDO.setIsDelete(IsDeleteEnum.NO.getCode());
        baseDO.setRecordVersion(1L);
    }

    public static void fillDOUpdateFromUCenter(BaseDO baseDO) {
        baseDO.setLastUpdDate(new Date());
        UserInfoDTO userInfo = UcenterUtils.getUserInfo();
        if (userInfo != null) {
            baseDO.setLastUpdUserCode(userInfo.getUserCode());
            baseDO.setLastUpdUserName(userInfo.getUserName());
        } else {
            // 兜底，避免userInfo为null的情况
            baseDO.setLastUpdUserCode(RequestInfoHolder.getUserCode());
            baseDO.setLastUpdUserName(RequestInfoHolder.getUserName());
        }


    }

    /**
     * 填充DO公共值
     *
     * @param baseDO
     */
    public static void fillDOInsertFromUCenter(BaseDO baseDO) {
        UserInfoDTO userInfo = UcenterUtils.getUserInfo();
        if (userInfo != null) {
            baseDO.setCreateUserCode(userInfo.getUserCode());
            baseDO.setCreateUserName(userInfo.getUserName());
            baseDO.setLastUpdUserCode(userInfo.getUserCode());
            baseDO.setLastUpdUserName(userInfo.getUserName());
        } else {
            // 兜底，避免userInfo为null的情况
            baseDO.setCreateUserCode(RequestInfoHolder.getUserCode());
            baseDO.setCreateUserName(RequestInfoHolder.getUserName());
            baseDO.setLastUpdUserCode(RequestInfoHolder.getUserCode());
            baseDO.setLastUpdUserName(RequestInfoHolder.getUserName());
        }
        baseDO.setCreateDate(new Date());
        baseDO.setLastUpdDate(baseDO.getCreateDate());
        baseDO.setIsDelete(IsDeleteEnum.NO.getCode());
        baseDO.setRecordVersion(1L);
    }
}
