package com.imile.attendance.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.ZonedDateTimeSerializer;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2024/12/3 
 * @Description jackson工具类
 */
@Slf4j
public class JsonUtils {

    public static final ObjectMapper OBJECT_MAPPER;

    static {
        OBJECT_MAPPER = Jackson2ObjectMapperBuilder.json()
                .createXmlMapper(false)
                .dateFormat(new SimpleDateFormat(DateUtils.DATETIME_FORMAT))
                .serializationInclusion(JsonInclude.Include.NON_NULL)
                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .featuresToDisable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
                .featuresToDisable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .modules(new ParameterNamesModule(), new Jdk8Module(), getJavaTimeModule())
                .build();
    }


    @NotNull
    private static JavaTimeModule getJavaTimeModule() {
        DateTimeFormatter dtFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter desDtFormatter = new DateTimeFormatterBuilder()
                .appendPattern("yyyy-MM-dd[[[' ']['T']HH][:mm][:ss[.SSS]]")
                .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
                .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
                .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
                .parseDefaulting(ChronoField.NANO_OF_SECOND, 0)
                .toFormatter();
        DateTimeFormatter tFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dtFormatter));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(tFormatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(desDtFormatter));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(tFormatter));
        javaTimeModule.addSerializer(ZonedDateTime.class,
                new ZonedDateTimeSerializer(DateTimeFormatter.ISO_ZONED_DATE_TIME));
        return javaTimeModule;
    }


    @SneakyThrows
    public static <T> T read(String text, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        return OBJECT_MAPPER.readValue(text, typeReference);

    }

    @SneakyThrows
    public static <T> T read(String text, Class<T> clazz) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        return OBJECT_MAPPER.readValue(text, clazz);
    }

    @SneakyThrows
    public static String write(Object obj) {
        if (obj == null) {
            return null;
        }
        return OBJECT_MAPPER.writeValueAsString(obj);

    }


    /**
     * Notice: invoke object.toString()
     */
    @SneakyThrows
    public static String writeToString(Object obj) {
        if (obj == null) {
            return "";
        }
        return obj.toString();

    }

    @SneakyThrows
    public static byte[] writeBytes(Object value) {
        if (value == null) {
            return new byte[0];
        }
        return OBJECT_MAPPER.writeValueAsBytes(value);
    }

    @SneakyThrows
    public static <T> T convert(Object obj, TypeReference<T> typeReference) {
        return OBJECT_MAPPER.convertValue(obj, typeReference);
    }

    @SneakyThrows
    public static <T> T convert(Object obj, Class<T> type) {
        return OBJECT_MAPPER.convertValue(obj, type);
    }


    public static class CustomModule extends SimpleModule {
        public CustomModule() {
            addDeserializer(List.class, new FlexibleListDeserializer());
        }
    }

    public static class FlexibleListDeserializer extends JsonDeserializer<List<?>> {
        @Override
        public List<?> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String text = p.getText();

            // 尝试获取类型信息
            JavaType type = ctxt.getContextualType();

            // 如果无法获取类型信息，尝试推断类型
            if (type == null) {
                // 尝试解析为整数
                try {
                    return parseIntegerList(text);
                } catch (NumberFormatException e) {
                    // 如果解析整数失败，则作为字符串处理
                    return parseStringList(text);
                }
            }

            // 如果能获取到类型信息，按类型处理
            Class<?> elementType = type.getContentType().getRawClass();
            if (String.class.equals(elementType)) {
                return parseStringList(text);
            } else if (Integer.class.equals(elementType)) {
                return parseIntegerList(text);
            } else {
                // 如果类型不匹配，尝试智能推断
                try {
                    return parseIntegerList(text);
                } catch (NumberFormatException e) {
                    return parseStringList(text);
                }
            }
        }

        private List<String> parseStringList(String text) {
            return Arrays.stream(text.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
        }

        private List<Integer> parseIntegerList(String text) {
            return Arrays.stream(text.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
    }
}
