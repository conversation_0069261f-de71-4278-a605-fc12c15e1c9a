package com.imile.attendance.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/4/18
 */
@Slf4j
public class BusinessFieldUtils {

    public static final String CHINESE_PATTERN = "[\u4e00-\u9fa5]";

    private BusinessFieldUtils() {
        // ignored
    }

    public static String getUnifiedUserName(String userName, String userNameEn) {
        if (StringUtils.isBlank(userName)) {
            return "";
        }
        // 若userName为全中文且userNameEn不为空 则将俩字段的值拼接在一起再返回
        if (isAllInChinese(userName) && StringUtils.isNotBlank(userNameEn)) {
            return StringUtils.capitalize(userNameEn.trim()) + userName.trim();
        }
        // 否则直接返回userName的值
        return userName.trim();
    }

    /**
     * 判断关键字是否全中文
     *
     * @param keyword 关键字
     * @return Boolean
     */
    public static Boolean isAllInChinese(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return Boolean.TRUE;
        }
        String keywordTrim = keyword.trim();
        Pattern pattern = Pattern.compile(CHINESE_PATTERN);
        Matcher mather = pattern.matcher(keywordTrim);
        int length = 0;
        while (mather.find()) {
            length++;
        }
        return length == keywordTrim.length() ? Boolean.TRUE : Boolean.FALSE;
    }

}
