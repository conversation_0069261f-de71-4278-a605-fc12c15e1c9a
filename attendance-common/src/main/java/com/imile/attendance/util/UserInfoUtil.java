package com.imile.attendance.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.imile.attendance.constants.EntAuthConstant;
import com.imile.common.constant.HeaderConstant;
import com.imile.ucenter.api.authenticate.UcenterUtils;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import com.imile.util.json.JsonBuilder;
import com.imile.util.user.UserEvnHolder;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 〈一句话功能简述〉<br>
 * 〈用户登录工具类〉
 *
 * <AUTHOR>
 * @Company 杭州艾麦科技有限公司
 * @className： UserInfoUtil.java
 * @create 2020/8/14
 * @since 1.0.0
 */
public class UserInfoUtil {
    private UserInfoUtil(){}

    /**
     * 查询用户
     *
     * @return
     */
    public static UserInfoDTO getUserInfo() {
        return UcenterUtils.getUserInfo();
    }

    /**
     * 查询用户token
     *
     * @return
     */
    public static String getUToken() {
        return UcenterUtils.getUToken();
    }

    /**
     * 获取结算企业id
     *
     * @return
     */
    public static Long getOrgId() {
        UserInfoDTO userInfoDTO = getUserInfo();
        Long orgId = null;
        if (!ObjectUtils.isEmpty(userInfoDTO)) {
            orgId = userInfoDTO.getSettleOrgId();
        }
        return orgId;
    }

    /**
     * 获取运营操作企业id
     *
     * @return
     */
    public static Long getOptOrgId() {
        UserInfoDTO userInfoDTO = getUserInfo();
        Long orgId = null;
        if (!ObjectUtils.isEmpty(userInfoDTO)) {
            orgId = userInfoDTO.getOrgId();
        }
        return orgId;
    }

    /**
     * 获取lang
     *
     * @return
     */
    public static String getLang() {
        return UserEvnHolder.getLang();
    }

    /**
     * 获取lang
     *
     * @return
     */
    public static String getTimeZone() {
        return UserEvnHolder.getTimeZoneStr();
    }

    /**
     * 获取moduleId
     *
     * @return
     */
    public static Long getModuleId(HttpServletRequest request) {
        Map<String, String> userInfo = getFrontSecMap(request);
        if (!CollectionUtils.isEmpty(userInfo)) {
            String id = userInfo.get(EntAuthConstant.MODULEID);
            if (StringUtils.isBlank(id)) {
                return null;
            }
            return Long.parseLong(id);
        }
        return null;
    }

    /**
     * 获取头信息
     *
     * @param request
     */
    public static Map<String, String> getFrontSecMap(HttpServletRequest request) {
        String authString = request.getHeader(HeaderConstant.FrontSec);
        if (StringUtils.isBlank(authString)) {
            return null;
        }
        try {
            return Optional.ofNullable(JsonBuilder.buildNormalBinder()
                    .fromJsonForCollection(authString, new TypeReference<Map<String, String>>() {
                    })).get();
        } catch (Exception e) {
            e.printStackTrace();
            return new HashMap<>();
        }
    }


}
