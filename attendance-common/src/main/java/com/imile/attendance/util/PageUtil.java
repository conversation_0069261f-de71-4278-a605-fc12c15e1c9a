package com.imile.attendance.util;

import com.github.pagehelper.Page;
import com.imile.common.page.Pagination;
import com.imile.common.page.PaginationResult;
import com.imile.common.query.BaseQuery;
import org.springframework.beans.BeanUtils;

import javax.annotation.Nonnull;
import java.util.List;

public class PageUtil {

    /**
     * 处理分页返回数据
     */
    public static <T> PaginationResult<T> get(List<T> queryData,
                                              Page<T> page,
                                              Pagination pagination) {
        PaginationResult<T> result = new PaginationResult<>();
        if (page != null) {
            long totalCount = pagination.getTotalResult() == 0 ?
                    page.getTotal() : pagination.getTotalResult();
            pagination.setTotalResult((int) totalCount);
            pagination.setPageEnabled(true);
            pagination.reset();
        }
        result.setResults(queryData);
        result.setPagination(pagination);
        return result;
    }

    /**
     * 返回结果为空的时候封装空对象
     *
     * @param currentPage 页码
     * @param showCount   页码
     * @return 结果
     */
    @Nonnull
    public static <T> PaginationResult<T> empty(Integer currentPage, Integer showCount) {
        PaginationResult<T> result = new PaginationResult<>();
        Pagination pagination = new Pagination();
        pagination.setCurrentPage(currentPage);
        pagination.setShowCount(showCount);
        pagination.setTotalResult(0);
        pagination.setPageEnabled(false);
        pagination.reset();
        result.setResults(null);
        result.setPagination(pagination);
        return result;
    }

    /**
     * 返回分页参数
     */
    public static <T> PaginationResult<T> getPageResult(List<T> list, BaseQuery pageQuery, Integer total, Integer totalPage) {
        Pagination pagination = new Pagination();
        BeanUtils.copyProperties(pageQuery, pagination);
        pagination.setTotalResult(pageQuery.getShowCount() < 0 && total < 0 ? list.size() : total);
        pagination.setTotalPage(totalPage);
        PaginationResult<T> result = new PaginationResult<>();
        result.setResults(list);
        result.setPagination(pagination);
        return result;
    }
}

