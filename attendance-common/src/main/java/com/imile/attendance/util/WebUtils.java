package com.imile.attendance.util;

import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Locale;
import java.util.Objects;

/**
 * Web工具类，提供HTTP请求、响应和会话相关的实用方法。
 * 该工具类主要用于在Spring Web环境中获取当前请求上下文中的HttpServletRequest、
 * HttpServletResponse和HttpSession对象，以及处理区域设置和获取客户端IP地址等功能。
 *
 * 注意：这些方法依赖于Spring的RequestContextHolder，因此只能在Web请求线程中使用。
 * 在非Web线程中调用某些方法可能会抛出IllegalStateException异常。
 */
public class WebUtils {
    /**
     * 获取当前 {@link HttpSession} 对象
     *
     * @param create 如果当前会话不存在，是否创建新会话
     * @return 当前HTTP会话对象
     * @throws IllegalStateException 如果在非Web线程中调用此方法
     */
    public static HttpSession httpSession(boolean create) {
        return request().getSession(create);
    }

    /**
     * 获取当前HTTP请求对象
     *
     * @return 当前HTTP请求对象
     * @throws IllegalStateException 如果在非Web线程中调用此方法
     */
    public static HttpServletRequest request() {
        HttpServletRequest result = optionalRequest();
        if (result == null) {
            throw new IllegalStateException("在非 web 线程中获取 request");
        }
        return result;
    }


    /**
     * 尝试获取当前HTTP请求对象，如果不在Web线程中则返回null
     *
     * @return 当前HTTP请求对象，如果不在Web线程中则返回null
     */
    public static HttpServletRequest optionalRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        return requestAttributes.getRequest();
    }

    /**
     * 获取当前HTTP响应对象
     *
     * @return 当前HTTP响应对象
     * @throws IllegalStateException 如果在非Web线程中调用此方法
     */
    public static HttpServletResponse response() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            throw new IllegalStateException("在非 web 线程中获取 response");
        }
        return requestAttributes.getResponse();
    }

    /**
     * 设置当前请求的区域设置（Locale）
     *
     * @param locale 要设置的区域设置
     * @throws IllegalStateException 如果在非Web线程中调用此方法
     * @throws IllegalArgumentException 如果未找到LocaleResolver
     */
    public static void setLocale(Locale locale) {
        HttpServletRequest request = request();
        HttpServletResponse response = response();
        LocaleResolver localeResolver = RequestContextUtils.getLocaleResolver(request);
        Assert.notNull(localeResolver, "localeResolver为空");
        localeResolver.setLocale(request, response, locale);
    }

    /**
     * 获取客户端的真实IP地址
     * <p>
     * 该方法会尝试从各种HTTP头信息中获取客户端IP地址，包括：
     * - X-Forwarded-For (Squid服务代理)
     * - X-Real-IP (nginx服务代理)
     * - Proxy-Client-IP (apache服务代理)
     * - WL-Proxy-Client-IP (weblogic服务代理)
     * - HTTP_CLIENT_IP (其他代理服务器)
     * <p>
     * 如果以上头信息都无法获取有效IP，则使用request.getRemoteAddr()方法获取。
     * 对于多层代理的情况，会提取第一个非unknown的IP地址作为客户端真实IP。
     *
     * @return 客户端的IP地址，如果不在Web线程中或无法获取则返回null
     */
    public static String getIpAddress() {
        String ip = null;
        HttpServletRequest request = optionalRequest();
        if (Objects.isNull(request)) {
            return ip;
        }

        // X-Forwarded-For：Squid 服务代理
        String ipAddresses = request.getHeader("X-Forwarded-For");
        if (ipAddresses == null || ipAddresses.isEmpty() || "unknown".equalsIgnoreCase(ipAddresses)) {
            // X-Real-IP：nginx服务代理
            ipAddresses = request.getHeader("X-Real-IP");
        }
        if (ipAddresses == null || ipAddresses.isEmpty() || "unknown".equalsIgnoreCase(ipAddresses)) {
            // Proxy-Client-IP：apache 服务代理
            ipAddresses = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddresses == null || ipAddresses.isEmpty() || "unknown".equalsIgnoreCase(ipAddresses)) {
            // WL-Proxy-Client-IP：weblogic 服务代理
            ipAddresses = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddresses == null || ipAddresses.isEmpty() || "unknown".equalsIgnoreCase(ipAddresses)) {
            // HTTP_CLIENT_IP：有些代理服务器
            ipAddresses = request.getHeader("HTTP_CLIENT_IP");
        }
        // 有些网络通过多层代理，那么获取到的ip就会有多个，一般都是通过逗号（,）分割开来，并且第一个ip为客户端的真实IP
        if (ipAddresses != null && !ipAddresses.isEmpty()) {
            ip = ipAddresses.split(",")[0];
        }
        // 还是不能获取到，最后再通过request.getRemoteAddr();获取
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ipAddresses)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

}
