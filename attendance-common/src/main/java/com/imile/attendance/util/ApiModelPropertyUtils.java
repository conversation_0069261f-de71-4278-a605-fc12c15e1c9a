package com.imile.attendance.util;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */

import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * API模型属性工具类，用于获取类字段上的ApiModelProperty注解的value值
 */
public class ApiModelPropertyUtils {

    // 使用ConcurrentHashMap作为缓存容器，提高并发环境下的性能
    private static final Map<String, String> PROPERTY_VALUE_CACHE = new ConcurrentHashMap<>();

    /**
     * 获取指定类中某个字段的ApiModelProperty注解的value值
     *
     * @param clazz 类
     * @param fieldName 字段名
     * @return 注解value值，如果没有找到返回字段名
     */
    public static String getPropertyValue(Class<?> clazz, String fieldName) {
        if (clazz == null || !StringUtils.hasText(fieldName)) {
            return fieldName;
        }
        // 构造缓存key
        String cacheKey = clazz.getName() + "#" + fieldName;

        // 先从缓存中查找
        String cachedValue = PROPERTY_VALUE_CACHE.get(cacheKey);
        if (cachedValue != null) {
            return cachedValue;
        }
        try {
            // 获取类的声明字段
            Field field = clazz.getDeclaredField(fieldName);
            // 获取字段上的ApiModelProperty注解
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);

            String value = fieldName;
            if (annotation != null) {
                value = annotation.value();
                // 如果注解的value为空，则使用原字段名
                if (!StringUtils.hasText(value)) {
                    value = fieldName;
                }
            }

            // 将结果放入缓存
            PROPERTY_VALUE_CACHE.put(cacheKey, value);
            return value;
        } catch (NoSuchFieldException e) {
            // 如果在当前类中找不到该字段，尝试在父类中查找
            if (clazz.getSuperclass() != null) {
                return getPropertyValue(clazz.getSuperclass(), fieldName);
            }
            // 如果找不到字段，返回原字段名
            PROPERTY_VALUE_CACHE.put(cacheKey, fieldName);
            return fieldName;
        }
    }

    /**
     * 获取对象实例字段的ApiModelProperty注解value值
     *
     * @param obj 对象实例
     * @param fieldName 字段名
     * @return 注解value值
     */
    public static String getPropertyValue(Object obj, String fieldName) {
        if (obj == null) {
            return fieldName;
        }
        return getPropertyValue(obj.getClass(), fieldName);
    }

    /**
     * 清除缓存
     */
    public static void clearCache() {
        PROPERTY_VALUE_CACHE.clear();
    }
}
