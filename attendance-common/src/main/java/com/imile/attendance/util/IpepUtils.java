package com.imile.attendance.util;

import com.imile.common.excel.ExcelImport;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工具类
 * <AUTHOR>
 * @param <T>
 */
public class IpepUtils<T extends ExcelImport> {
    public static <T extends ExcelImport> void putFail(T excelImport, String message) {
        excelImport.setSuccess(false);
        excelImport.setErrorMessage(message);
    }

    public static <T extends ExcelImport> void putFail(List<T> list, String message) {
        if (list != null) {
            list.forEach(item -> {
                if (item.getSuccess()) {
                    putFail(item, message);
                }
            });
        }
    }

    public static <T extends ExcelImport> List<T> filterFail(Map<Integer, T> sourceMap, List<T> failList) {
        return failList.stream().map(item -> {
            T source = sourceMap.get(item.getRowNum());
            source.setErrorMessage(item.getErrorMessage());
            source.setSuccess(item.getSuccess());
            source.setRowNum(item.getRowNum());
            return source;
        }).collect(Collectors.toList());
    }
}
