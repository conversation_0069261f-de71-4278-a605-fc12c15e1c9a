package com.imile.attendance.util;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
public class RedisUtils {

    public static final String NAMESPACE = System.getProperty("my.redis.module", "attendance");
    public static final String KEY_SEPARATOR = ":";

    /**
     * 转换 template，使用自带类型信息的模板
     *
     * @param commonTemplate 通用模板
     * @param <T>            类型
     * @return 转换后的模板
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static <T> RedisTemplate<String, T> convertTemplate(RedisTemplate<String, Object> commonTemplate) {
        return (RedisTemplate) commonTemplate;
    }


    public static String buildKey(String keySuffix) {
        return NAMESPACE + KEY_SEPARATOR + keySuffix;
    }

    public static String buildKey(String moduleName, String businessName, String dataName, Object... params) {
        StringBuilder keyBuilder = new StringBuilder(NAMESPACE);
        keyBuilder.append(KEY_SEPARATOR);
        keyBuilder.append(moduleName);
        keyBuilder.append(KEY_SEPARATOR);
        keyBuilder.append(businessName);
        keyBuilder.append(KEY_SEPARATOR);
        keyBuilder.append(dataName);
        if (params != null && params.length > 0) {
            keyBuilder.append(KEY_SEPARATOR);
            for (Object param : params) {
                keyBuilder.append(param);
                keyBuilder.append(KEY_SEPARATOR);
            }
            keyBuilder.deleteCharAt(keyBuilder.length() - 1);
        }
        return keyBuilder.toString();
    }

    public static String buildClusterKey(String moduleName, String businessName, String dataName, String hashParams, Object... params) {
        StringBuilder keyBuilder = new StringBuilder(NAMESPACE);
        keyBuilder.append(KEY_SEPARATOR);
        keyBuilder.append(moduleName);
        keyBuilder.append(KEY_SEPARATOR);
        keyBuilder.append(businessName);
        keyBuilder.append(KEY_SEPARATOR);
        keyBuilder.append(dataName);
        keyBuilder.append(KEY_SEPARATOR);
        keyBuilder.append("{");
        keyBuilder.append(hashParams);
        keyBuilder.append("}");
        if (params != null && params.length > 0) {
            keyBuilder.append(KEY_SEPARATOR);
            for (Object param : params) {
                keyBuilder.append(param);
                keyBuilder.append(KEY_SEPARATOR);
            }
            keyBuilder.deleteCharAt(keyBuilder.length() - 1);
        }
        return keyBuilder.toString();
    }

    public static List<String> buildMultiKey(String moduleName, String businessName, String dataName, List<?> objectList) {
        if (CollectionUtils.isEmpty(objectList)) {
            return Collections.emptyList();
        }
        String prefix = NAMESPACE +
                KEY_SEPARATOR +
                moduleName +
                KEY_SEPARATOR +
                businessName +
                KEY_SEPARATOR +
                dataName;
        List<String> keySet = new ArrayList<>(objectList.size());
        for (Object object : objectList) {
            keySet.add(prefix + KEY_SEPARATOR + object);
        }
        return keySet;
    }

    public static List<String> buildMultiClusterKey(String moduleName, String businessName, String dataName,
                                                    String hashParam, List<?> objectList) {
        if (CollectionUtils.isEmpty(objectList)) {
            return Collections.emptyList();
        }
        String prefix = NAMESPACE +
                KEY_SEPARATOR +
                moduleName +
                KEY_SEPARATOR +
                businessName +
                KEY_SEPARATOR +
                dataName +
                KEY_SEPARATOR +
                "{" +
                hashParam
                + "}";
        List<String> keySet = new ArrayList<>(objectList.size());
        for (Object object : objectList) {
            keySet.add(prefix + KEY_SEPARATOR + object);
        }
        return keySet;
    }
}
