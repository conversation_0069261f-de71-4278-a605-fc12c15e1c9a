package com.imile.attendance.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@Data
public class OptionVO implements Serializable {

    private static final long serialVersionUID = -417747921413215849L;

    /**
     * 枚举的type值：一般为Integer类型
     */
    private Integer type;

    /**
     * 枚举的描述：后端根据语言环境返回
     */
    private String desc;

}
