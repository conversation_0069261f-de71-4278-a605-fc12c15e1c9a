package com.imile.attendance.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 标准国家编码和内部国家编码转换
 */
@Getter
public enum CountryCodeEnum {

    //https://en.wikipedia.org/wiki/List_of_ISO_3166_country_codes
    //https://developers.google.com/maps/documentation/geocoding/overview#ComponentFiltering


    CHN("CHN", "CN", "China", "中国", "中国"),
    KSA("KSA", "SA", "Kingdom of Saudi Arabia", "沙特阿拉伯", "沙特"),
    UAE("UAE", "AE", "United Arab Emirates", "阿拉伯联合酋长国", "阿联酋"),
    AUS("AUS", "AU", "Australia", "澳大利亚(澳洲)", "澳大利亚"),
    BHR("BHR", "BH", "Bahrain", "巴林", "巴林"),
    BTN("BTN", "BT", "Bhutan", "不丹", "不丹"),
    BRN("BRN", "BN", "Brunei Darussalam", "汶莱", "汶莱"),
    KHM("KHM", "KH", "Cambodia", "柬埔寨", "柬埔寨"),
    FJI("FJI", "FJ", "Fiji", "斐济", "斐济"),
    GUM("GUM", "GU", "Guam", "关岛", "关岛"),
    IND("IN", "IN", "India", "印度", "印度"),
    IDN("IDN", "ID", "Indonesia", "印尼", "印尼"),
    IRN("IRN", "IR", "Iran", "伊朗", "伊朗"),
    ISR("ISR", "IL", "Israel", "以色列", "以色列"),
    JPN("JPN", "JP", "Japan", "日本", "日本"),
    JOR("JOR", "JO", "Jordan", "约旦", "约旦"),
    KWT("KWT", "KW", "Kuwait", "科威特", "科威特"),
    KOR("KOR", "KR", "Korea", "韩国", "韩国"),
    LAO("LAO", "LA", "Lao", "寮国", "寮国"),
    MAS("MAS", "MY", "Malaysia", "马来西亚", "马来西亚"),
    NRU("NRU", "NR", "Nauru", "诺鲁", "诺鲁"),
    NZL("NZL", "NZ", "New Zealand", "纽西兰", "纽西兰"),
    OMN("OMN", "OM", "Oman", "阿曼", "阿曼"),
    PAK("PAK", "PK", "Pakistan", "巴基斯坦", "巴基斯坦"),
    PNG("PNG", "PG", "Papua New Guinea", "巴布亚纽几内亚", "巴布亚纽几内亚"),
    PHL("PHL", "PH", "Philippines", "菲律宾", "菲律宾"),
    QAT("QAT", "QA", "Qatar", "卡塔尔", "卡塔尔"),
    SGP("SGP", "SG", "Singapore", "新加坡", "新加坡"),
    SAU("SAU", "SA", "Saudi Arabia", "沙乌地阿拉伯", "沙乌地阿拉伯"),
    SLB("SLB", "SB", "Solomon Islands", "索罗门群岛", "索罗门群岛"),
    LKA("LKA", "LK", "Sri Lanka", "斯里兰卡", "斯里兰卡"),
    SYR("SYR", "SY", "Syrian Arab Republic", "叙利亚", "叙利亚"),
    THA("THA", "TH", "Thailand", "泰国", "泰国"),
    TUR("TUR", "TR", "Turkey", "土耳其", "土耳其"),
    ARE("ARE", "AE", "United Arab Emirates", "阿拉伯联合大公国", "阿拉伯联合大公国"),
    VNM("VNM", "VN", "Viet Nam", "越南", "越南"),
    YEM("YEM", "YE", "Yemen", "叶门", "叶门"),
    ARM("ARM", "AM", "Armenia", "亚美尼亚", "亚美尼亚"),
    AUT("AUT", "AT", "Austria", "奥地利", "奥地利"),
    AZE("AZE", "AZ", "Azerbaijan", "亚塞拜然", "亚塞拜然"),
    BLR("BLR", "BY", "Belarus", "白俄罗斯", "白俄罗斯"),
    BEL("BEL", "BE", "Belgium", "比利时", "比利时"),
    BGR("BGR", "BG", "Bulgaria", "保加利亚", "保加利亚"),
    CYP("CYP", "CY", "Cyprus", "塞普勒斯", "塞普勒斯"),
    CZE("CZE", "CZ", "Czech", "捷克", "捷克"),
    DNK("DNK", "DK", "Denmark", "丹麦", "丹麦"),
    EST("EST", "EE", "Estonia", "爱沙尼亚", "爱沙尼亚"),
    FIN("FIN", "FI", "Finland", "芬兰", "芬兰"),
    FRA("FRA", "FR", "France", "法国", "法国"),
    GEO("GEO", "GE", "Georgia", "乔治亚", "乔治亚"),
    DEU("DEU", "DE", "Germany", "德国", "德国"),
    GBR("GBR", "GB", "United Kingdom", "英国", "英国"),
    GRC("GRC", "GR", "Greece", "希腊", "希腊"),
    HUN("HUN", "HU", "Hungary", "匈牙利", "匈牙利"),
    IRL("IRL", "IE", "Ireland", "爱尔兰", "爱尔兰"),
    ITA("ITA", "IT", "Italy", "意大利", "意大利"),
    KAZ("KAZ", "KZ", "Kazakhstan", "哈萨克", "哈萨克"),
    KGZ("KGZ", "KG", "Kyrgyzstan", "吉尔吉斯", "吉尔吉斯"),
    LVA("LVA", "LV", "Latvia", "拉脱维亚", "拉脱维亚"),
    LTU("LTU", "LT", "Lithuania", "立陶宛", "立陶宛"),
    LUX("LUX", "LU", "Luxembourg", "卢森堡", "卢森堡"),
    MLT("MLT", "MT", "Malta", "马耳他", "马耳他"),
    MDA("MDA", "MD", "Moldova", "摩尔多瓦", "摩尔多瓦"),
    NLD("NLD", "NL", "Netherlands", "荷兰", "荷兰"),
    NOR("NOR", "NO", "Norway", "挪威", "挪威"),
    POL("POL", "PL", "Poland", "波兰", "波兰"),
    PRT("PRT", "PT", "Portugal", "葡萄牙", "葡萄牙"),
    RUS("RUS", "RU", "Russian Federation", "俄罗斯", "俄罗斯"),
    SVK("SVK", "SK", "Slovakia", "斯洛伐克", "斯洛伐克"),
    SVN("SVN", "SI", "Slovenia", "斯洛维尼亚", "斯洛维尼亚"),
    ESP("ESP", "ES", "Spain", "西班牙", "西班牙"),
    CHE("CHE", "CH", "Switzerland", "瑞士", "瑞士"),
    SWE("SWE", "SE", "Sweden", "瑞典", "瑞典"),
    TJK("TJK", "TJ", "Tajikistan", "塔吉克", "塔吉克"),
    TKM("TKM", "TM", "Turkmenistan", "土库曼", "土库曼"),
    UKR("UKR", "UA", "Ukraine", "乌克兰", "乌克兰"),
    UZB("UZB", "UZ", "Uzbekistan", "乌兹别克", "乌兹别克"),
    CAN("CAN", "CA", "Canada", "加拿大", "加拿大"),
    USA("USA", "US", "United States", "美国", "美国"),
    ARG("ARG", "AR", "Argentina", "阿根廷", "阿根廷"),
    BRB("BRB", "BB", "Barbados", "巴贝多", "巴贝多"),
    BOL("BOL", "BO", "Bolivia", "玻利维亚", "玻利维亚"),
    BRA("BRA", "BR", "Brazil", "巴西", "巴西"),
    CHL("CHL", "CL", "Chile", "智利", "智利"),
    COL("COL", "CO", "Colombia", "哥伦比亚", "哥伦比亚"),
    CRI("CRI", "CR", "Costa Rica", "哥斯达黎加", "哥斯达黎加"),
    ECU("ECU", "EC", "Ecuador", "厄瓜多尔", "厄瓜多尔"),
    SLV("SLV", "SV", "El Salvador", "萨尔瓦多", "萨尔瓦多"),
    GTM("GTM", "GT", "Guatemala", "瓜地马拉", "瓜地马拉"),
    HND("HND", "HN", "Honduras", "洪都拉斯", "洪都拉斯"),
    JAM("JAM", "JM", "Jamaica", "牙买加", "牙买加"),
    MEX("MEX", "MX", "Mexico", "墨西哥", "墨西哥"),
    PAN("PAN", "PA", "Panama", "巴拿马", "巴拿马"),
    PRY("PRY", "PY", "Paraguay", "巴拉圭", "巴拉圭"),
    PER("PER", "PE", "Peru", "秘鲁", "秘鲁"),
    PRI("PRI", "PR", "Puerto Rico", "波多黎各", "波多黎各"),
    URY("URY", "UY", "Uruguay", "乌拉圭", "乌拉圭"),
    VEN("VEN", "VE", "Venezuela", "委内瑞拉", "委内瑞拉"),
    BEN("BEN", "BJ", "Benin", "贝南", "贝南"),
    CIV("CIV", "CI", "Ivory Coast (Cote D’Ivoire)", "象牙海岸", "象牙海岸"),
    DJI("DJI", "DJ", "Djibouti", "吉布地", "吉布地"),
    EGY("EGY", "EG", "Egypt", "埃及", "埃及"),
    ETH("ETH", "ET", "Ethiopia", "衣索比亚", "衣索比亚"),
    GHA("GHA", "GH", "Ghana", "迦纳", "迦纳"),
    KEN("KEN", "KE", "Kenya", "肯亚", "肯亚"),
    LSO("LSO", "LS", "Lesotho", "赖索托", "赖索托"),
    MDG("MDG", "MG", "Madagascar", "马达加斯加", "马达加斯加"),
    MWI("MWI", "MW", "Malawi", "马拉威", "马拉威"),
    MLI("MLI", "ML", "Mali", "马利", "马利"),
    MUS("MUS", "MU", "Mauritius", "模里西斯", "模里西斯"),
    MOZ("MOZ", "MZ", "Mozambique", "莫三比克", "莫三比克"),
    NER("NER", "NE", "Niger", "尼日", "尼日"),
    NGA("NGA", "NG", "Nigeria", "奈及利亚", "奈及利亚"),
    SEN("SEN", "SN", "Senegal", "塞内加尔", "塞内加尔"),
    SLE("SLE", "SL", "Sierra Leone", "狮子山", "狮子山"),
    RSA("RSA", "ZA", "South Africa", "南非", "南非"),
    SDN("SDN", "SD", "Sudan", "苏丹", "苏丹"),
    TZA("TZA", "TZ", "Tanzania", "坦尚尼亚", "坦尚尼亚"),
    TGO("TGO", "TG", "Togo", "多哥", "多哥"),
    LBN("LBN", "LB", "Lebanon", "黎巴嫩", "黎巴嫩"),
    IRQ("IRQ", "IQ", "Iraq", "伊拉克", "伊拉克"),
    EU("EU", "EU", "Europe", "欧洲", "欧洲"),
    MAR("MAR", "MA", "Morocco", "摩洛哥", "摩洛哥"),
    DOM("DOM", "DO", "Dominican Republic", "多米尼加", "多米尼加"),
    NIC("NIC", "NI", "Nicaragua", "尼加拉瓜", "尼加拉瓜"),
    HQ("HQ", "HQ", "Headquarters", "集团总部", "集团总部");


    private String code;

    private String standardCode;

    private String descEn;

    private String descCn;

    private String shortName;

    CountryCodeEnum(String code, String standardCode, String descEn, String descCn, String shortName) {
        this.code = code;
        this.standardCode = standardCode;
        this.descEn = descEn;
        this.descCn = descCn;
        this.shortName = shortName;
    }

    public static CountryCodeEnum getInstance(String code) {
        for (CountryCodeEnum value : CountryCodeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 把内部国家编码转为标准国家编码
     *
     * @return
     */
    public static String convert2StandardCountryCode(String countryCode) {
        CountryCodeEnum instance = getInstance(countryCode);
        if (instance == null) {
            return countryCode;
        }
        return instance.getStandardCode();
    }

    /**
     * 标准国家编码转为内部国家编码
     *
     * @param standardCode
     * @return
     */
    public static String convert2InternalCountryCode(String standardCode) {
        for (CountryCodeEnum countryCodeEnum : CountryCodeEnum.values()) {
            if (countryCodeEnum.getStandardCode().equalsIgnoreCase(standardCode)) {
                return countryCodeEnum.getCode();
            }
        }
        return "";
    }

    public static String getCountryName(String code, boolean isChinese) {
        CountryCodeEnum countryEnum = CountryCodeEnum.getInstance(code);
        return Objects.isNull(countryEnum) ? code : isChinese ? countryEnum.getDescCn() : countryEnum.getDescEn();

    }

    public static List<String> getCodeList() {
        return Arrays.stream(CountryCodeEnum.values())
                .map(CountryCodeEnum::getCode)
                .collect(Collectors.toList());
    }

    /**
     * 排班页面特殊国家：仅这些国家管理劳务派遣人员
     */
    public static final List<String> SPECIAL_COUNTRY_FOR_PAGE_SHIFT = Arrays.asList(
            KSA.getCode(), UAE.getCode(), MEX.getCode(), OMN.getCode(),
            QAT.getCode(), KWT.getCode(), BRA.getCode()
    );
}
