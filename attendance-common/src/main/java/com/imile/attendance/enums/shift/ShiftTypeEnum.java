package com.imile.attendance.enums.shift;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18
 * @Description 排班类型
 */
@Getter
public enum ShiftTypeEnum {

    //自定义排班>循环排班>自动排班

    /**
     * 自动排班
     */
    AUTO_SHIFT("AUTO_SHIFT", "自动排班", "Auto Scheduling"),

    /**
     * 循环排班
     */
    CYCLE_SHIFT("CYCLE_SHIFT", "循环排班", "Cycle Scheduling"),

    /**
     * 自定义排班
     */
    CUSTOM_SHIFT("CUSTOM_SHIFT", "自定义排班", "Custom Scheduling");

    private final String code;
    private final String desc;
    private final String descEn;

    private static final Map<String, ShiftTypeEnum> cacheMap = new ConcurrentHashMap<>();

    static {
        for (ShiftTypeEnum categoryEnum : values()) {
            cacheMap.put(categoryEnum.getCode(), categoryEnum);
        }
    }

    ShiftTypeEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static ShiftTypeEnum getByCode(String code) {
        return cacheMap.get(code);
    }

    /**
     * 获取该排班类型下的所有排班来源
     */
    public List<ShiftSourceEnum> getSubTypes() {
        switch (this) {
            case AUTO_SHIFT:
                return Arrays.asList(ShiftSourceEnum.SYSTEM_SHIFT);
            case CYCLE_SHIFT:
                return Arrays.asList(ShiftSourceEnum.CYCLE_SHIFT);
            case CUSTOM_SHIFT:
                return Arrays.asList(
                        ShiftSourceEnum.PAGE_SHIFT,
                        ShiftSourceEnum.BATCH_SHIFT,
                        ShiftSourceEnum.IMPORT_SHIFT
                );
            default:
                return Arrays.asList();
        }
    }

    public static List<ShiftTypeEnum> listCustomShiftLowPriority() {
        return Arrays.asList(ShiftTypeEnum.CUSTOM_SHIFT, ShiftTypeEnum.CYCLE_SHIFT, ShiftTypeEnum.AUTO_SHIFT);
    }

    public static List<ShiftTypeEnum> listCycleShiftLowPriority() {
        return Arrays.asList(ShiftTypeEnum.CYCLE_SHIFT, ShiftTypeEnum.AUTO_SHIFT);
    }

    public static List<ShiftTypeEnum> listAutoShiftLowPriority() {
        return Arrays.asList(ShiftTypeEnum.AUTO_SHIFT);
    }

    public static List<String> listNoCustomShift() {
        return Lists.newArrayList(ShiftTypeEnum.CYCLE_SHIFT.getCode(), ShiftTypeEnum.AUTO_SHIFT.getCode());
    }


}
