package com.imile.attendance.enums.shift;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description 排班数据来源
 */
@Getter
public enum ShiftSourceEnum {

    /**
     * 页面操作 - 自定义排班
     */
    PAGE_SHIFT("PAGE_SHIFT", "页面操作", "Page Operation", ShiftTypeEnum.CUSTOM_SHIFT),

    /**
     * 批量排班 - 自定义排班
     */
    BATCH_SHIFT("BATCH_SHIFT", "批量排班", "Batch Scheduling", ShiftTypeEnum.CUSTOM_SHIFT),

    /**
     * 系统排班 - 自动排班
     */
    SYSTEM_SHIFT("SYSTEM_SHIFT", "系统排班", "System Scheduling", ShiftTypeEnum.AUTO_SHIFT),

    /**
     * 系统排班续期 - 自动排班
     */
    SYSTEM_SHIFT_RENEWAL("SYSTEM_SHIFT_RENEWAL", "系统排班续期", "System Scheduling Renewal", ShiftTypeEnum.AUTO_SHIFT),

    /**
     * 日历变动自动排班 - 自动排班
     */
    SYSTEM_SHIFT_CALENDAR_TRIGGER("SYSTEM_SHIFT_CALENDAR_TRIGGER", "日历变动自动排班", "Calendar Change Auto Scheduling", ShiftTypeEnum.AUTO_SHIFT),

    /**
     * 循环排班 - 循环排班
     */
    CYCLE_SHIFT("CYCLE_SHIFT", "循环排班", "Cycle Scheduling", ShiftTypeEnum.CYCLE_SHIFT),

    /**
     * 循环排班续期 - 循环排班
     */
    CYCLE_SHIFT_RENEWAL("CYCLE_SHIFT_RENEWAL", "循环排班续期", "Cycle Scheduling Renewal", ShiftTypeEnum.CYCLE_SHIFT),

    /**
     * 排班导入 - 自定义排班
     */
    IMPORT_SHIFT("IMPORT_SHIFT", "排班导入", "Schedule Import", ShiftTypeEnum.CUSTOM_SHIFT);


    private final String code;
    private final String desc;
    private final String descEn;
    private final ShiftTypeEnum category;

    private static final Map<String, ShiftSourceEnum> cacheMap = new ConcurrentHashMap<>();

    static {
        for (ShiftSourceEnum typeEnum : values()) {
            cacheMap.put(typeEnum.getCode(), typeEnum);
        }
    }

    ShiftSourceEnum(String code, String desc, String descEn, ShiftTypeEnum category) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
        this.category = category;
    }

    public static ShiftSourceEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return cacheMap.get(code);
    }

    /**
     * 根据排班来源获取排班类型
     */
    public static ShiftTypeEnum getShiftCategory(String shiftTypeCode) {
        ShiftSourceEnum shiftSourceEnum = getByCode(shiftTypeCode);
        if (shiftSourceEnum == null) {
            return null;
        }
        return shiftSourceEnum.getCategory();
    }

    /**
     * 判断排班来源是否属于指定的排班类型
     */
    public boolean belongsTo(ShiftTypeEnum categoryEnum) {
        return this.category == categoryEnum;
    }
}
