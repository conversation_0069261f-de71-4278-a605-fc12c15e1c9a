package com.imile.attendance.enums;

import lombok.Getter;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Getter
public enum ApprovalNoPrefixEnum {

    LEAVE("KQQJ", "请假申请单"),
    OUT_OF_OFFICE("KQWQ", "外勤申请单"),
    REISSUE_CARD("KQBK", "补卡申请单"),
    ADD_DURATION("KQAD", "补时长申请单"),
    OVER_TIME("KQOT", "加班申请单"),
    LEAVE_REVOKE("QJCX","请假-撤销申请单"),
    OUT_OF_OFFICE_REVOKE("WQCX", "外勤-撤销申请单"),
    REISSUE_CARD_REVOKE("BKCX", "补卡-撤销申请单"),



    ;



    /**
     * 前缀
     */
    private final String prefix;

    /**
     * 模块说明
     */
    private final String remark;


    ApprovalNoPrefixEnum(String prefix, String remark) {
        this.prefix = prefix;
        this.remark = remark;
    }

    public static ApprovalNoPrefixEnum getInstance(String prefix) {
        for (ApprovalNoPrefixEnum value : ApprovalNoPrefixEnum.values()) {
            if (value.getPrefix().equalsIgnoreCase(prefix)) {
                return value;
            }
        }
        return null;
    }
}
