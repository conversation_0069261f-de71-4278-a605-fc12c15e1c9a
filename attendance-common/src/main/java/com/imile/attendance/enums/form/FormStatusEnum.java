package com.imile.attendance.enums.form;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/9 
 * @Description
 */
@Getter
public enum FormStatusEnum {

    STAGING("STAGING", "Staging", "暂存"),
    REJECT("REJECT", "reject", "已驳回"),
    IN_REVIEW("IN_REVIEW", "in review", "审核中"),
    PASS("PASS", "pass", "已通过"),
    CANCEL("CANCEL", "cancel", "已取消"),
    TERMINATED("TERMINATED", "terminated", "已终止"),
    WITHDRAWN("WITHDRAWN", "withdrawn", "已撤销"),

    ;


    private final String code;

    private final String descEn;

    private final String desc;

    FormStatusEnum(String code, String descEn, String desc) {
        this.code = code;
        this.descEn = descEn;
        this.desc = desc;
    }

    public static FormStatusEnum getInstance(String code) {
        for (FormStatusEnum value : FormStatusEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<String> getAttendanceCodeList() {
        return Lists.newArrayList(IN_REVIEW, PASS)
                .stream()
                .map(FormStatusEnum::getCode)
                .collect(Collectors.toList());
    }
}
