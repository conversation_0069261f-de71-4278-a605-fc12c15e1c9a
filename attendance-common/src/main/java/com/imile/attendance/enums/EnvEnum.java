package com.imile.attendance.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/9/21
 */
@Getter
public enum EnvEnum {
    /**
     * 系统环境枚举类
     */
    LOCAL("local", "本地环境"),
    DEV("dev", "开发环境"),
    TEST("test", "测试环境"),
    LPT("lpt", "预发环境"),
    UAT("uat", "灰度环境"),
    PROD("prod", "生产环境"),
    ;

    private final String code;

    private final String desc;

    EnvEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 是否为非正式环境
     *
     * @param env 环境编码
     * @return Boolean
     */
    public static Boolean isInformal(String env) {
        return !UAT.code.equalsIgnoreCase(env) && !PROD.code.equalsIgnoreCase(env);
    }
}
