package com.imile.attendance.enums.vacation;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024/4/30
 */
public enum AttendanceInvalidEnum {
    YES(1, "失效"),
    NO(0, "未失效");

    private Integer code;

    private String value;

    private static final Map<Integer, AttendanceInvalidEnum> cacheMap = new ConcurrentHashMap<Integer, AttendanceInvalidEnum>();

    static {
        for (AttendanceInvalidEnum codeEnum : values()) {
            cacheMap.put(codeEnum.getCode(), codeEnum);
        }
    }

    AttendanceInvalidEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static AttendanceInvalidEnum getIsInvalidEnum(Integer code) {
        if (code == null) {
            return null;
        }
        return cacheMap.get(code);
    }
}
