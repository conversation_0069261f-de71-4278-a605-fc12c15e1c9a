package com.imile.attendance.enums.vacation;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {@code @author:} allen
 * {@code @className:} LeaveConfigIsSalary
 * {@code @since:} 2024-04-15 16:54
 * {@code @description:}
 */
@Getter
public enum LeaveConfigIsSalary {
    DEFAULT(0, "", ""),
    LEAVE_WITH_FULL_PAY(1, "全薪假", "Leave with full pay"),
    UNPAID_LEAVE(2, "无薪假", "Unpaid leave"),
    LADDER_FAKE(3, "阶梯假", "Ladder fake"),
    ;

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, LeaveConfigIsSalary> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (LeaveConfigIsSalary codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    LeaveConfigIsSalary(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static LeaveConfigIsSalary getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }
}
