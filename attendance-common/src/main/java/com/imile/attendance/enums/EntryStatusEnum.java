package com.imile.attendance.enums;

import lombok.Getter;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Getter
public enum EntryStatusEnum {

    REPARE_INVITED("WAIT_INVITED", "等待邀请"),
    PREPARE_REGISTER("PREPARE_REGISTER", "待员工登记"),
    PREPARE_ENTRY_CONFIRM("PREPARE_CONFIRM", "待确认入职"),
    ENTRY("ENTRY", "已入职"),
    CANCEL_ENTRY("CANCEL_ENTRY", "已放弃入职"),

    WAIT_AUDIT("WAIT_AUDIT", "待审核"),
    NO_THROUGH("NO_THROUGH", "审核不通过"),

    IN_REVIEW("IN_REVIEW", "审批中"),
    REJECTED("REJECTED", "驳回"),
    APPROVED("APPROVED", "审批通过"),
            ;


    private String code;

    private String desc;

    EntryStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
