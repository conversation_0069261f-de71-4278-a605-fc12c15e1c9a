package com.imile.attendance.enums.form;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum FormTypeEnum {

    LEAVE("LEAVE", "Leave Application Form", "请假申请单"),
    OUT_OF_OFFICE("OUT_OF_OFFICE", "Out Of Office Application Form", "外勤申请单"),
    REISSUE_CARD("REISSUE_CARD", "Reissue Card Application Form", "补卡申请单"),
    ADD_DURATION("ADD_DURATION", "Add Duration Application Form", "补时长申请单"),
    OVER_TIME("OVER_TIME", "Over Time Application Form", "加班申请单"),
    WAREHOUSE_REISSUE_CARD("WAREHOUSE_REISSUE_CARD", "Warehouse Reissue Card Application Form", "仓内补卡申请单"),

    LEAVE_REVOKE("LEAVE_REVOKE", "Leave Revoke Application Form", "请假-撤销申请单"),
    OUT_OF_OFFICE_REVOKE("OUT_OF_OFFICE_REVOKE", "Out Of Office Revoke Application Form", "外勤-撤销申请单"),
    REISSUE_CARD_REVOKE("REISSUE_CARD_REVOKE", "Reissue Card Revoke Application Form", "补卡-撤销申请单"),


    ;
    private String code;

    private String descEn;

    private String desc;

    FormTypeEnum(String code, String descEn, String desc) {
        this.code = code;
        this.descEn = descEn;
        this.desc = desc;
    }

    /**
     * 补卡类型
     */
    public static final List<String> TYPE_OF_REISSUE_CARD_TYPE = Lists.newArrayList(REISSUE_CARD, WAREHOUSE_REISSUE_CARD)
            .stream()
            .map(FormTypeEnum::getCode)
            .collect(Collectors.toList());

    public static FormTypeEnum getInstance(String code) {
        for (FormTypeEnum value : FormTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<String> getAttendanceCodeList() {
        return Lists.newArrayList(LEAVE, OUT_OF_OFFICE, REISSUE_CARD).stream()
                .map(FormTypeEnum::getCode)
                .collect(Collectors.toList());
    }

    public static List<String> getLeaveAndOutOfOfficeCodeList() {
        return Lists.newArrayList(LEAVE, OUT_OF_OFFICE).stream()
                .map(FormTypeEnum::getCode)
                .collect(Collectors.toList());
    }
}
