package com.imile.attendance.enums;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 出勤类型 PRESENT 应出勤日，WEEKEND 休息日 ，HOLIDAY 节假日, LEAVE 请假
 */
public enum AttendanceDayTypeEnum {

    /**
     * 应出勤日
     */
    PRESENT,
    /**
     * 节假日
     */
    HOLIDAY,
    /**
     * 休息日
     */
    WEEKEND,
    /**
     * 请假
     */
    LEAVE;

    private static final Map<String, AttendanceDayTypeEnum> cacheMap = new ConcurrentHashMap<>();

    static {
        for (AttendanceDayTypeEnum codeEnum : values()) {
            cacheMap.put(codeEnum.name(), codeEnum);
        }
    }


    public static AttendanceDayTypeEnum parserEnum(String code) {
        if (code == null) {
            return null;
        }
        return cacheMap.get(code);
    }
}
