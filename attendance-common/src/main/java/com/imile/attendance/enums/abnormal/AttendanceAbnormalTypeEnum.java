package com.imile.attendance.enums.abnormal;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum AttendanceAbnormalTypeEnum {
    NORMAL("NORMAL", "正常", "Normal", "NORMAL", "正常", "NORMAL"),
    LATE("LATE", "迟到", "Late", "LATE"
            , "所在班次上班时间后到岗", "Arrive after the work start time of the shift schedule."),
    LEAVE_EARLY("LEAVE_EARLY", "早退", "Leave Early", "LE"
            , "所在班次下班时间前离岗", "Leave before the work end time of the shift schedule."),
    LACK("LACK", "缺卡", "Lack", "LACK", "缺卡", "LACK"),
    ABSENT("ABSENT", "缺勤", "Absent", "A"
            , "没有按时出席工作或没有事先请假而缺席工作，尤其是一天都没有打卡记录"
            , "Failure to attend work on time or without prior leave approval,especially if no punch record is made for the entire day."),
    OVERTIME("OVERTIME", "加班", "Overtime", "OT"
            , "经上级领导审批通过且超出正常工作时间进行工作", "Working beyond normal working hours with approval from a supervisor."),
    NO_SCHEDULING_PLAN("NO_SCHEDULING_PLAN", "没有排班计划", "No Scheduling Plan", "NSP"
            , "系统中显示没有当日排班计划", "No Scheduling Plan on the system, please contact HR."),
    SCAN_COUNT_ERROR("SCAN_COUNT_ERROR", "作业数据不足", "SCAN_COUNT_ERROR", "SCE"
            , "作业数据不足", "SCAN_COUNT_ERROR"),
    NO_SALARY_CONFIG("NO_SALARY_CONFIG", "未设置薪资方案/薪资方案有误", "NO_SALARY_CONFIG", "NSC"
            , "未设置薪资方案/薪资方案有误", "NO_SALARY_CONFIG"),
    LATE_AND_LEAVE_EARLY("LATE_AND_LEAVE_EARLY", "迟到早退", "LATE_AND_LEAVE_EARLY", "LATE/LE"
            , "迟到和早退", "LATE_AND_LEAVE_EARLY"),
    BEFORE_OFFICE_LACK("BEFORE_OFFICE_LACK", "上班缺卡", "No Punch-In", "BEFORE_OFFICE_LACK"
            , "所在班次上班时间开始前未打卡", "No punch-in before work start time of the shift schedule."),
    AFTER_OFFICE_LACK("AFTER_OFFICE_LACK", "下班缺卡", "No Punch-Out", "AFTER_OFFICE_LACK"
            , "所在班次下班时间结束后未打卡", "No punch-out after work end time of the shift schedule."),
    REISSUE_CARD("REISSUE_CARD", "补卡", "Reissue Card", "REISSUE_CARD"
            , "您是否申请了加班，但是忘记了打卡，那么请走补卡申请流程吧！", "Have you applied for overtime, but forgot to punch in/out, Please submit the punch in/out correction application!"),
    NO_PUNCH("NO_PUNCH", "未打卡", "No Punch", "NO_PUNCH"
            , "全天未进行考勤打卡", "No punch during the whole day."),
    ABNORMAL_DURATION("ABNORMAL_DURATION", "时长异常", "Abnormal Duration", "ABNORMAL_DURATION"
            , "实出勤时长或实工作时长不满班次规定的时长", "Actual attendance hours or actual working hours are less than the specified shift duration"),
    ;

    private String code;

    private String desc;

    private String descEn;

    private String simpleCode;

    private String detail;

    private String detailEn;

    AttendanceAbnormalTypeEnum(String code, String desc, String descEn, String simpleCode, String detail, String detailEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
        this.simpleCode = simpleCode;
        this.detail = detail;
        this.detailEn = detailEn;
    }

    public static AttendanceAbnormalTypeEnum getInstanceByCode(String code) {
        for (AttendanceAbnormalTypeEnum value : AttendanceAbnormalTypeEnum.values()) {
            if (StringUtils.equalsIgnoreCase(code, value.getCode())) {
                return value;
            }
        }
        return null;
    }

    public static List<String> getLackCodeList() {
        return Lists.newArrayList(BEFORE_OFFICE_LACK, AFTER_OFFICE_LACK, LEAVE_EARLY, NO_PUNCH).stream().map(AttendanceAbnormalTypeEnum::getCode).collect(Collectors.toList());
    }

    public static List<String> getOutLackCodeList() {
        return Lists.newArrayList(AFTER_OFFICE_LACK, LEAVE_EARLY, NO_PUNCH).stream().map(AttendanceAbnormalTypeEnum::getCode).collect(Collectors.toList());
    }

    public static List<String> getInLackCodeList() {
        return Lists.newArrayList(BEFORE_OFFICE_LACK, LATE).stream().map(AttendanceAbnormalTypeEnum::getCode).collect(Collectors.toList());
    }

    public static List<String> getRessiueCodeList() {
        return Lists.newArrayList(LATE, LEAVE_EARLY, BEFORE_OFFICE_LACK, AFTER_OFFICE_LACK, NO_PUNCH).stream().map(AttendanceAbnormalTypeEnum::getCode).collect(Collectors.toList());
    }
}
