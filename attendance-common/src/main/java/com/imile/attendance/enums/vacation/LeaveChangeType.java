package com.imile.attendance.enums.vacation;

import lombok.Getter;

@Getter
public enum LeaveChangeType {
    INCREASE("increase", "收入"),
    REDUCE("reduce", "支出"),
    REISSUE("reissue", "补发"),
    DEDUCTION("deduction", "补扣"),
    IMPORT("import", "导入"),
    ;

    private String code;

    private String desc;

    LeaveChangeType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
