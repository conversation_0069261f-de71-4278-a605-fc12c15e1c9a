package com.imile.attendance.enums.abnormal;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum AbnormalAttendanceStatusEnum {

   /* PENDING("pending", "处理中"),
    PROCESSED("processed", "已处理"),*/

    UN_PROCESSED("UN_PROCESSED", "Not processed", "待处理"),
    REJECT("REJECT", "reject", "已驳回"),
    IN_REVIEW("IN_REVIEW", "in review", "审核中"),
    PASS("PASS", "pass", "已通过"),
    EXPIRED("EXPIRED", "expired", "已过期"),
/*
    CANCEL("CANCEL", "cancel", "已取消"),
    TERMINATED("TERMINATED", "terminated", "已终止"),
    WITHDRAWN("WITHDRAWN", "withdrawn", "已撤销"),
*/;

    private String code;

    private String descEn;

    private String desc;

    AbnormalAttendanceStatusEnum(String code, String descEn, String desc) {
        this.code = code;
        this.descEn = descEn;
        this.desc = desc;
    }

    public static AbnormalAttendanceStatusEnum getInstanceByCode(String code) {
        for (AbnormalAttendanceStatusEnum value : AbnormalAttendanceStatusEnum.values()) {
            if (StringUtils.equalsIgnoreCase(code, value.getCode())) {
                return value;
            }
        }
        return null;
    }

    /**
     * 已通过 + 已过期 聚合
     */
    public static final List<String> TYPE_OF_PASS_OR_EXPIRED
            = Lists.newArrayList(PASS, EXPIRED).stream()
            .map(AbnormalAttendanceStatusEnum::getCode)
            .collect(Collectors.toList());
}
