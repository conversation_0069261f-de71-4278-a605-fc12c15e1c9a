package com.imile.attendance.enums;


import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Getter
public enum AttributeTypeEnum {
    SYSTEM("SYSTEM", "系统管理员", ResourceTypeEnum.ALL, RoleTypeEnum.SYSTEM),
    //组织架构改造后，含义变为一级组织管理员
    SUBSIDIARY("SUBSIDIARY", "子公司管理员", ResourceTypeEnum.COMPANY_ID, RoleTypeEnum.ORG),
    DEPT_SYS("DEPT_SYS", "运营部门管理员", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    VENDOR_SYS("VENDOR_SYS", "供应商管理员", ResourceTypeEnum.VENDOR_ID, RoleTypeEnum.VENDOR),
    NONE("NONE", "非管理员", ResourceTypeEnum.NONE, RoleTypeEnum.NONE),
    SYSTEM_STAFF("SYSTEM_STAFF", "员工账号管理员", ResourceTypeEnum.ALL, RoleTypeEnum.SYSTEM),
    ATTENDANCE_SYS("ATTENDANCE_SYS", "考勤管理员", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    FINANCIAL_SYS("FINANCIAL_SYS", "财务管理员", ResourceTypeEnum.ALL, RoleTypeEnum.SYSTEM),
    TRAINING_SYS("TRAINING_SYS", "培训管理员", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    AREA_SYS("AREA_SYS", "区域管理员", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    DRIVER_SYS("DRIVER_SYS", "司机管理员", ResourceTypeEnum.COMPANY_ID, RoleTypeEnum.ORG),
    HO_DEPT_SYS("HO_DEPT_SYS", "办公室部门管理员", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    SYSTEM_ITEM("SYSTEM_ITEM", "非薪资管理员", ResourceTypeEnum.ALL, RoleTypeEnum.SYSTEM),
    HQ_HR_P_S("HQ_HR_P_S", "HQ HR（人事+系统）", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HQ_HR_C_B("HQ_HR_C_B", "HQ HR（薪资）", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HQ_HR_T("HQ_HR_T", "HQ HR（培训）", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HQ_HR_O_P("HQ_HR_O_P", "HQ HR（组织+绩效）", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HQ_HR_TA("HQ_HR_TA", "HQ HR（招聘）", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    TA_ADMIN("TA_ADMIN", "招聘管理员", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    CHN_HRM("CHN_HRM", "CHN HRM", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    CHN_HR_TA("CHN_HR_TA", "CHN HR (TA)", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HRD("HRD", "HRD", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_P_S("COUNTRY_HR_P_S", "Country HR（人事+系统）", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_C_B("COUNTRY_HR_C_B", "Country HR（薪资）", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_T("COUNTRY_HR_T", "Country HR（培训）", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_P("COUNTRY_HR_P", "Country HR（绩效）", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_TA("COUNTRY_HR_TA", "Country HR（招聘）", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    COUNTRY_HR_TA_P("COUNTRY_HR_TA_P", "Country HR（招聘+人事）", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HRM("HRM", "HRM", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HOD("HOD", "国家二级部门负责人", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    HUB_LEADER("HUB_LEADER", "Hub Leader", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    FIN_COST_DATA("FIN_COST_DATA", "FIN-Cost data", ResourceTypeEnum.DEPT_ID, RoleTypeEnum.DEPT),
    ;


    private String code;

    private String desc;
    /**
     * 数据权限
     */
    private ResourceTypeEnum resourceType;

    private RoleTypeEnum roleType;

    AttributeTypeEnum(String code, String desc, ResourceTypeEnum resourceType, RoleTypeEnum roleType) {
        this.code = code;
        this.desc = desc;
        this.resourceType = resourceType;
        this.roleType = roleType;
    }

    private static final Map<String, AttributeTypeEnum> cacheMap = new ConcurrentHashMap<>();

    public static AttributeTypeEnum getAttributeTypeEnum(String code) {
        return code == null ? null : cacheMap.get(code);
    }

    static {
        AttributeTypeEnum[] attributes = values();
        int var1 = attributes.length;

        for (int index = 0; index < var1; ++index) {
            AttributeTypeEnum codeEnum = attributes[index];
            cacheMap.put(codeEnum.getCode(), codeEnum);
        }

    }

}
