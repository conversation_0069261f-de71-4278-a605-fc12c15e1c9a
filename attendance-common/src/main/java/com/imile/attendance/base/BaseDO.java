package com.imile.attendance.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ToString
@Accessors(chain = true)
@Data
public abstract class BaseDO implements Serializable {

    private static final long serialVersionUID = -658123255221093526L;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private Date createDate;

    @TableField(value = "create_user_code", fill = FieldFill.INSERT)
    private String createUserCode;

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "last_upd_date", fill = FieldFill.INSERT_UPDATE)
    private Date lastUpdDate;

    @TableField(value = "last_upd_user_code", fill = FieldFill.INSERT_UPDATE)
    private String lastUpdUserCode;

    @TableField(value = "last_upd_user_name", fill = FieldFill.INSERT_UPDATE)
    private String lastUpdUserName;


    /**
     * 是否删除
     */
    private Integer isDelete;


    /**
     * 版本号
     */
    private Long recordVersion;

}
