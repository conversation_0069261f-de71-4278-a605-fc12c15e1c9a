package com.imile.attendance.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description 存储循环过程中的时间范围和索引信息
 */
@Data
public class CycleHelpDTO {
    /**
     * 开始时间
     */
    private Date searchStartTime;
    /**
     * 结束时间
     */
    private Date searchEndTime;
    /**
     * 列表索引
     */
    private Integer userIndex;
    /**
     * 列表索引
     */
    private Integer deptIndex;
}
