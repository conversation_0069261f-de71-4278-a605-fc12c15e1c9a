package com.imile.attendance.loader;

import com.imile.attendance.annon.Strategy;
import com.imile.attendance.func.MatchFunction;
import com.imile.attendance.wrapper.StrategyWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 策略模式加载
 * <AUTHOR>
 * @since 2025/4/14
 */
@Component
public class StrategyLoader implements ApplicationListener<ContextRefreshedEvent> {
    private static final Logger log = LoggerFactory.getLogger(StrategyLoader.class);
    private static final HashMap<String, List<StrategyWrapper<?>>> strategyListMap = new HashMap();

    public StrategyLoader() {
    }

    public static <T> T load(Class<T> clz, MatchFunction<T> matchFunction) {
        if (clz != null && matchFunction != null) {
            T strategyBean = null;
            T defaultBean = null;
            String clzName = clz.getName();
            List<StrategyWrapper<?>> wrapperList = (List)strategyListMap.get(clzName);
            if (CollectionUtils.isEmpty(wrapperList)) {
                return null;
            } else {
                Iterator var6 = wrapperList.iterator();

                while(var6.hasNext()) {
                    StrategyWrapper<?> wrapper = (StrategyWrapper)var6.next();
                    T t = clz.cast(wrapper.getStrategy());
                    if (wrapper.isDefault()) {
                        defaultBean = t;
                    } else {
                        boolean isMatch = matchFunction.isMatch(t);
                        if (isMatch) {
                            strategyBean = t;
                            break;
                        }
                    }
                }

                if (strategyBean != null) {
                    return strategyBean;
                } else {
                    log.warn("StrategyLoader没有命中实现，返回默认实现，{}", defaultBean);
                    return defaultBean;
                }
            }
        } else {
            return null;
        }
    }

    public static <T> T load(Class<T> clz, String implKey) {
        if (clz != null && implKey != null) {
            String clzName = clz.getName();
            List<StrategyWrapper<?>> wrapperList = (List)strategyListMap.get(clzName);
            if (CollectionUtils.isEmpty(wrapperList)) {
                return null;
            } else {
                Map<String, StrategyWrapper<?>> wrapperMap = (Map)wrapperList.stream().collect(Collectors.toMap(StrategyWrapper::getImplKey, Function.identity()));
                if (!wrapperMap.containsKey(implKey)) {
                    throw new UnsupportedOperationException("不支持的类型:" + implKey);
                } else {
                    return clz.cast(((StrategyWrapper)wrapperMap.get(implKey)).getStrategy());
                }
            }
        } else {
            return null;
        }
    }

    public static <T> List<T> load(Class<T> clz) {
        if (clz == null) {
            return null;
        } else {
            String clzName = clz.getName();
            List<StrategyWrapper<?>> wrapperList = (List)strategyListMap.get(clzName);
            if (CollectionUtils.isEmpty(wrapperList)) {
                return null;
            } else {
                List<T> beanList = new ArrayList();
                Iterator var4 = wrapperList.iterator();

                while(var4.hasNext()) {
                    StrategyWrapper<?> wrapper = (StrategyWrapper)var4.next();
                    T t = clz.cast(wrapper.getStrategy());
                    beanList.add(t);
                }

                return beanList;
            }
        }
    }

    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        Map<String, Object> objectMap = contextRefreshedEvent.getApplicationContext().getBeansWithAnnotation(Strategy.class);
        this.loadStrategyListMap(objectMap.values());
    }

    private void loadStrategyListMap(Collection<Object> strategyImplList) {
        if (!CollectionUtils.isEmpty(strategyImplList)) {
            strategyListMap.clear();
            Iterator var2 = strategyImplList.iterator();

            while(var2.hasNext()) {
                Object bean = var2.next();
                Strategy annotation = AopUtils.getTargetClass(bean).getAnnotation(Strategy.class);
                String strategyKey = annotation.value().getName();
                String implKey = annotation.implKey();
                boolean isDefault = annotation.isDefault();
                int priority = annotation.priority();
                StrategyWrapper<?> wrapper = new StrategyWrapper(bean, implKey, isDefault, priority);
                List<StrategyWrapper<?>> wrapperList = (List)strategyListMap.get(strategyKey);
                if (wrapperList == null) {
                    wrapperList = new ArrayList();
                }

                ((List)wrapperList).add(wrapper);
                strategyListMap.put(strategyKey, wrapperList);
            }

            var2 = strategyListMap.values().iterator();

            while(var2.hasNext()) {
                List<StrategyWrapper<?>> wrapperList = (List)var2.next();
                wrapperList.sort(Comparator.comparingInt(StrategyWrapper::getPriority));
            }

        }
    }
}
