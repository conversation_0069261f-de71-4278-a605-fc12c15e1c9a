package com.imile.attendance.apollo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Component
//@RefreshScope todo @RefreshScope配置
@ConfigurationProperties("imile.attendance")
@Data
public class AttendanceProperties {


    /**
     * 考勤相关配置
     */
    private Attendance attendance = new Attendance();

    private Database database = new Database();

    private Zkteco zkteco = new Zkteco();

    private Ipep ipep = new Ipep();



    /**
     * 考勤相关
     */
    @Setter
    @Getter
    public static class Attendance {
        /**
         * 默认考勤方案中文名称
         */
        private String defaultAttendanceConfigNameCn = "默认日历";
        /**
         * 默认考勤方案英文名称
         */
        private String defaultAttendanceConfigNameEn = "Default calendar";
        /**
         * 0-6点默认同步过去32天的数据,负数向历史偏移，正数向未来偏移
         */
        private Integer defaultSyncDay = -32;
        /**
         * 默认一次同步数据为1000
         */
        private Integer defaultLimitSize = 1000;

        /**
         * 默认打卡方案中文名称
         */
        private String defaultAttendancePunchConfigNameCn = "默认打卡规则";
        /**
         * 默认打卡方案英文名称
         */
        private String defaultAttendancePunchConfigNameEn = "Default Punch Rule";

        /**
         * 休息日中文
         */
        private String weekendCn = "休息日";

        /**
         * 休息日英文
         */
        private String weekendEn = "WEEKEND";
        /**
         * 休息日班次id
         */
        private Long weekendClassId = 0L;

        /**
         * 导入考勤数据，是否支持多天导入,默认为false，不支持
         */
        private boolean importMultitude = false;

        /**
         * 需要发送邮件的部门
         */
        private String postNameCnList = "HR";

        /**
         * 计算薪资是否是减法逻辑
         */
        private boolean calculateSalarySubtraction = true;

    }

    /**
     * 多数据源
     */
    @Setter
    @Getter
    public static class Database {
        /**
         * hrms
         */
        public static final String HRMS_URL = "hrms";
        /**
         * hrms(ro)
         */
        public static final String HRMS_RO_URL = "hrmsro";

        /**
         * attendance
         */
        public static final String ATTENDANCE_URL = "attendance";

        /**
         * 中东-中控(8.5)
         */
        public static final String ZKT_URL = "zkbiotime";

        /**
         * 中东8.0
         */
        public static final String ZKT_URL_VERSION_8 = "zktbiotime";

        /**
         * 拉美-中控
         */
        public static final String LM_ZKT_URL = "zkbiotime-lm";

    }

    /**
     * 中控考勤相关
     */
    @Setter
    @Getter
    public static class Zkteco {
        /**
         * 地址8.5
         */
        private String SERVER_URL = "https://test-zkt.imile-inc.com";
        /**
         * 账号8.5
         */
        private String userName = "imile_admin";
        /**
         * 密码8.5
         */
        private String password = "G8XSC5mVFkA";


        /**
         * 地址8.0
         */
        private String SERVER_URL_VERSION_8 = "http://zkt8.imile-inc.com";
        /**
         * 账号8.0
         */
        private String userNameVersion8 = "imile_admin";
        /**
         * 密码8.0
         */
        private String passwordVersion8 = "jqzRHp5ffJh";

//        /**
//         * 8.0子公司
//         */
//        @Deprecated
//        private String companyIdVersion8 = "104";

        /**
         * 8.0子公司
         */
        private String countryVersion8 = "MEX";

        /**
         * 司机部门
         */
        private Integer driverDeptId = 1;

        /**
         * 仓内部门
         */
        private Integer warehouseDeptId = 2;

        /**
         * 办公室员工部门
         */
        private Integer officeDeptId = 3;


        /**
         * 8.0司机部门
         */
        private Integer driverDeptId8 = 9;

        /**
         * 8.0仓内部门
         */
        private Integer warehouseDeptId8 = 10;

        /**
         * 8.0办公室员工部门
         */
        private Integer officeDeptId8 = 8;
    }


    @Getter
    @Setter
    public static class Ipep {
        private String IpepUrl = "http://*********:30045";
    }

}
