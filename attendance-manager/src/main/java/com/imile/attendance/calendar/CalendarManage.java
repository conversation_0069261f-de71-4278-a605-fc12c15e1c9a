package com.imile.attendance.calendar;

import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarRangeCountDTO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDateQuery;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21
 * @Description
 */
public interface CalendarManage {

    //=====================calendarConfig============================================

    /**
     * 根据ID查询最新启用的日历配置
     */
    CalendarConfigDO getActiveCalendarConfigById(Long id);

    /**
     * 根据名称查询最新启用的日历配置
     */
    List<CalendarConfigDO> getActiveCalendarConfigByName(String calendarName);

    /**
     * 根据国家查询最新启用的日历配置
     */
    List<CalendarConfigDO> getActiveCalendarConfigByCountry(String country, String calendarName);

    /**
     * 查询用户指定时间考勤日历
     */
    Map<Long,List<CalendarConfigDO>> mapByUserIds(List<Long> userIdList, Date endDate);

    /**
     * 查询在职非司机且未配置日历的用户列表
     */
    List<UserInfoDO> listOnJobNoDriverUsersExcludeRangeConfigured(RuleRangeUserQuery ruleRangeUserQuery);

    /**
     * 获取员工指定年份的考勤日历明细模版
     * 注意，需要按照考勤模版的生效时间来拼装
     * 举例，A模版生效时间为2021年1月1日-2021年3月1日，3月2日切换到B模版，那么2021年全年的考勤模版为二者的拼接
     */
    List<CalendarConfigDetailDO> getCalendarRangeRecords(Long userId, Long year, String country);

    /**
     * 获取员工指定年月的考勤日历明细模版
     */
    List<CalendarConfigDetailDO> getCalendarRangeRecords(Long userId, Long year, Long month, String country);

    /**
     * 获取用户指定年的考勤日历明细模版map
     */
    @Deprecated
    Map<Long, CalendarConfigDetailDO> getCalendarDetailConfigMap(Long userId, Long year);

    /**
     * 获取用户在指定周期内的日历明细列表
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   开始时间
     * @param country   国家
     * @return 用户在指定周期内的日历明细列表
     */
    List<CalendarConfigDetailDO> getUserCalendarDetailsWithinPeriod(Long userId, Date startTime, Date endTime, String country);

    /**
     * 获取周期内用户日历明细
     */
    List<CalendarConfigDetailDO> getMultiUserCalendarDetailsWithinPeriod(List<Long> userIdList, Date startTime, Date endTime, String country);


    /**
     * 获取当前员工打卡时间生效的当日的日历配置明细
     */
    CalendarConfigDetailDO getLatestCalendarConfigDetail(Long userId, Date punchTime);


    /**
     * 根据日历配置id批量获取日历配置
     */
    List<CalendarConfigDO> getByCalendarConfigIds(List<Long> calendarConfigIds);

    /**
     * 批量查询国家下的所有日历配置
     */
    List<CalendarConfigDO> getCalendarConfigsByCountries(List<String> countries);

    /**
     * 根据日历配置id批量获取最新的日历配置
     */
    List<CalendarConfigDO> getLatestCalendarConfigsByIds(List<Long> calendarConfigIds);

    /**
     * 移动打卡查询休息日历明细
     */
    CalendarConfigDetailDO getMobileLatestCalendarConfigDetail(Long userId, Date punchTime);


    //=====================calendarConfigDetail============================================

    /**
     * 根据日历配置id查询日历明细
     */
    List<CalendarConfigDetailDO> selectCalendarDetailsByConfigIds(List<Long> calendarConfigIds);

    /**
     * 根据日历配置ids + 年份查询日历配置明细
     */
    List<CalendarConfigDetailDO> selectCalendarDetailsByIdsAndYear(List<Long> calendarConfigIds, List<Integer> years);


    //=====================calendarConfigRange============================================

    /**
     * 根据bizIds查询员工的日历配置适用范围(最新)
     */
    List<CalendarConfigRangeDO> selectCalendarConfigRange(List<Long> bizIds);

    /**
     * 根据日历配置id批量查询日历适用范围
     */
    List<CalendarConfigRangeDO> selectCalendarConfigRangeByConfigIds(List<Long> calendarConfigIds);

    /**
     * 查询员工指定时间内的考勤日历配置适用范围
     */
    List<CalendarConfigRangeDO> selectCalendarConfigByDate(CalendarConfigDateQuery query);

    /**
     * 查询用户指定时间考勤日历范围
     */
    List<CalendarConfigRangeDO> listByUserIds(List<Long> userIdList, Date endDate);

    /**
     * 批量修改或新增日历适用范围
     */
    void calendarConfigRangeUpdate(List<CalendarConfigRangeDO> updateList, List<CalendarConfigRangeDO> addList);

    /**
     * todo 不能使用，非日历模块的表无法适配开关行为，直接在原方法双写，等到所有的表都完成适配后，在整体替换
     */
//    void userCalendarAndPunchUpdate(List<CalendarConfigRangeDO> addCalendarConfigRangeDOList,
//                                    List<PunchConfigRangeDO> addPunchConfigRangeDOList,
//                                    List<CalendarConfigRangeDO> updateCalendarConfigRangeDOList,
//                                    List<PunchConfigRangeDO> updatePunchConfigRangeDOList,
//                                    List<HrmsCompanyLeaveConfigRangDO> addLeaveRang,
//                                    List<HrmsCompanyLeaveConfigRangDO> updateLeaveRang,
//                                    List<HrmsAttendanceUserLeaveConfigHistoryDO> addUserLeaveHistory,
//                                    List<HrmsAttendanceUserLeaveConfigHistoryDO> updateUserLeaveHistory);


    //=====================calendarLegalLeave============================================

    void saveLegalLeaveConfigAndAttendanceDetail(List<CalendarLegalLeaveConfigDO> calendarLegalLeaveConfigList,
                                                 List<CalendarConfigDetailDO> needUpdateCalendarConfigDetailDetailList,
                                                 List<CalendarConfigDetailDO> needSaveCalendarConfigDetailDetailList);

    List<CalendarRangeCountDTO> countCalendarRange(List<Long> calendarConfigIds);

    void deleteRangeByBizId(Long bizId);

    /**
     * 根据日历配置id和日期id查询节假日配置
     */
    CalendarLegalLeaveConfigDO getLegalLeaveByCalendarConfigIdAndDayId(Long calendarConfigId, Long dayId);

    /**
     * 根据日历配置id列表和年份查询节假日配置
     *
     * @param calendarConfigIds 日历配置id列表
     * @param years             年份列表
     * @return 节假日配置列表
     */
    List<CalendarLegalLeaveConfigDO> selectListByConfigIdsAndYear(List<Long> calendarConfigIds, List<Integer> years);

    void userCalendarRangeUpdate(List<CalendarConfigRangeDO> updateList, List<CalendarConfigRangeDO> addList);


    /**
     * 查询用户所有日历
     */
    List<CalendarConfigModifyDTO> selectAllByBizId(Long bizId);


}
