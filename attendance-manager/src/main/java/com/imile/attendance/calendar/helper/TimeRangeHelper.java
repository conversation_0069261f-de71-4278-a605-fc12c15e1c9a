package com.imile.attendance.calendar.helper;

import lombok.Data;

import java.util.Date;

/**
 * 日历时间范围辅助类，用于管理考勤日历明细查询中的时间范围和索引信息。
 * 该类的主要功能包括：
 * - 存储当前搜索的时间范围（开始时间和结束时间）。
 * - 管理用户日历配置范围列表中的索引位置。
 * - 提供方法判断循环是否完成，并支持时间范围的递进操作。
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description
 */
@Data
public class TimeRangeHelper {

    /**
     * 当前搜索的起始时间。
     */
    private Date searchStartTime;
    /**
     * 当前搜索的结束时间。
     */
    private Date searchEndTime;
    /**
     * 用户考勤配置范围列表中的当前索引位置。
     */
    private Integer userIndex = 0;

    public TimeRangeHelper(Date startTime, Date endTime) {
        this.searchStartTime = startTime;
        this.searchEndTime = endTime;
    }

    /**
     * 判断当前时间范围是否已完成
     * 如果当前的起始时间已经超过了结束时间，则表示时间范围已完成。
     *
     * @return 如果时间范围已完成，返回 true；否则返回 false。
     */
    public boolean isCompleted() {
        return searchStartTime.after(searchEndTime);
    }

    /**
     * 将时间范围递进到下一个时间段。
     * 该方法将当前的起始时间更新为当前的结束时间，以便在下一次循环中处理新的时间段。
     */
    public void advanceToNextPeriod() {
        this.searchStartTime = searchEndTime;
    }
}
