package com.imile.attendance.form.bo;


import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description 加班单据业务对象
 */
@Data
public class AttendanceApprovalFormDetailBO {

    private AttendanceApprovalFormDO approvalForm;

    private List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList;

    public static AttendanceApprovalFormDetailBO of(AttendanceApprovalFormDO approvalForm,
                                                    List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList) {
        AttendanceApprovalFormDetailBO attendanceApprovalFormDetailBO = new AttendanceApprovalFormDetailBO();
        attendanceApprovalFormDetailBO.setApprovalForm(approvalForm);
        attendanceApprovalFormDetailBO.setApprovalFormUserInfoList(approvalFormUserInfoList);
        return attendanceApprovalFormDetailBO;
    }
}
