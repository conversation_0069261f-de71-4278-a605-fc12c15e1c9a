package com.imile.attendance.form;

import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.form.bo.AttendanceApprovalFormDetailBO;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormUserInfoDao;
import com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormUserInfoQuery;
import com.imile.attendance.infrastructure.repository.form.query.OverTimeListQuery;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/9 
 * @Description 加班单据manage
 */
@Component
public class AttendanceApprovalFormManage {

    @Resource
    private AttendanceApprovalFormDao attendanceApprovalFormDao;
    @Resource
    private AttendanceApprovalFormUserInfoDao attendanceApprovalFormUserInfoDao;

    public AttendanceApprovalFormDetailBO getApprovalFormDetailById(Long approvalFormId){
        if (ObjectUtil.isNull(approvalFormId)) {
            return null;
        }
        AttendanceApprovalFormDO approvalForm = attendanceApprovalFormDao.getById(approvalFormId);

        ApprovalFormUserInfoQuery approvalFormUserInfoQuery = new ApprovalFormUserInfoQuery();
        approvalFormUserInfoQuery.setApprovalFormId(approvalFormId);
        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList =
                attendanceApprovalFormUserInfoDao.selectByCondition(approvalFormUserInfoQuery);

        return AttendanceApprovalFormDetailBO.of(
                approvalForm,
                approvalFormUserInfoList
        );
    }

    public List<AttendanceApprovalFormDO> selectExistForm(List<Long> formIdList){
        ApprovalFormQuery approvalFormQuery = ApprovalFormQuery.builder()
                .formIdList(formIdList)
                .formStatusList(Arrays.asList(FormStatusEnum.PASS.getCode(),
                        FormStatusEnum.IN_REVIEW.getCode()))
                .build();

        return attendanceApprovalFormDao.selectByCondition(approvalFormQuery);
    }

    public List<AttendanceApprovalFormUserInfoDO> selectByCondition(OverTimeListQuery query){
        ApprovalFormUserInfoQuery approvalFormUserInfoQuery = new ApprovalFormUserInfoQuery();
        approvalFormUserInfoQuery.setCountry(query.getCountry());
        approvalFormUserInfoQuery.setDeptIdList(query.getDeptIdList());
        approvalFormUserInfoQuery.setPostIdList(query.getPostIdList());
        approvalFormUserInfoQuery.setUserCodeOrName(query.getUserCodeOrName());
        approvalFormUserInfoQuery.setUserCodes(query.getUserCodeList());

        return attendanceApprovalFormUserInfoDao.selectByCondition(approvalFormUserInfoQuery);
    }

    public List<OverTimeApprovalListDTO> selectListByCondition(OverTimeListQuery query){
        return attendanceApprovalFormUserInfoDao.selectListByCondition(query);
    }
}
