package com.imile.attendance.driver;

import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverPunchRecordDao;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverPunchRecordDetailDao;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDetailDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailInfoQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@Component
public class DriverPunchRecordManage {

    @Resource
    private DriverPunchRecordDao driverPunchRecordDao;
    @Resource
    private DriverPunchRecordDetailDao driverPunchRecordDetailDao;


    public void saveOrUpdate(DriverPunchRecordDO driverPunchRecord) {
        if (ObjectUtil.isNotNull(driverPunchRecord)) {
            driverPunchRecordDao.save(driverPunchRecord);
        }
    }

    public List<DriverPunchRecordDO> selectPunchRecordDetail(DriverPunchRecordDetailQuery driverPunchRecordDetailQuery) {
        return driverPunchRecordDao.listPunchRecordDetail(driverPunchRecordDetailQuery);
    }

    @Transactional(rollbackFor = Exception.class)
    public void savePunchRecordAndDetail(DriverPunchRecordDO driverPunchRecord,
                                         DriverPunchRecordDetailDO driverPunchRecordDetail) {
        if (ObjectUtil.isNotNull(driverPunchRecord)) {
            driverPunchRecordDao.save(driverPunchRecord);
        }
        if (ObjectUtil.isNotNull(driverPunchRecordDetail)) {
            driverPunchRecordDetailDao.save(driverPunchRecordDetail);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updatePunchRecordAndDetail(List<DriverPunchRecordDO> driverPunchRecordList,
                                           List<DriverPunchRecordDetailDO> driverPunchRecordDetailList) {
        if (CollectionUtils.isNotEmpty(driverPunchRecordList)) {
            driverPunchRecordDao.updateBatchById(driverPunchRecordList);
        }
        if (CollectionUtils.isNotEmpty(driverPunchRecordDetailList)) {
            driverPunchRecordDetailDao.updateBatchById(driverPunchRecordDetailList);
        }
    }


    public List<DriverPunchRecordDetailDO> queryDriverPunchRecordDetailByCondition(DriverPunchRecordDetailInfoQuery detailInfoQuery) {
        if (ObjectUtil.isNull(detailInfoQuery)) {
            return Collections.emptyList();
        }
        return driverPunchRecordDetailDao.queryDriverPunchRecordDetailByCondition(detailInfoQuery);
    }
}
