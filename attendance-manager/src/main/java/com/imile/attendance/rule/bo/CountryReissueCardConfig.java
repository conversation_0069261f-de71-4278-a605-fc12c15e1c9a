package com.imile.attendance.rule.bo;

import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/12
 * @Description 国家补卡配置业务对象
 * <p>
 * 该类用于封装特定国家的补卡配置信息，包含国家信息和该国家的所有补卡配置列表。
 * 提供了查询国家级配置和根据部门ID查询配置的方法，用于在补卡规则应用时获取适用的配置。
 * </p>
 */
@Data
public class CountryReissueCardConfig {

    /**
     * 国家,该补卡配置适用的国家
     */
    private String country;

    /**
     * 国家补卡配置列表，包含该国家下所有的补卡配置
     * 可能包含国家级配置和部门级配置
     */
    private List<ReissueCardConfigDO> countryConfigs;


    /**
     * 创建国家补卡配置对象
     *
     * @param country 国家代码
     * @param countryConfigs 该国家的补卡配置列表
     * @return 国家补卡配置对象
     */
    public static CountryReissueCardConfig of(String country, List<ReissueCardConfigDO> countryConfigs) {
        CountryReissueCardConfig countryReissueCardConfig = new CountryReissueCardConfig();
        countryReissueCardConfig.setCountry(country);
        countryReissueCardConfig.setCountryConfigs(countryConfigs);
        return countryReissueCardConfig;
    }

    /**
     * 创建空的国家补卡配置对象
     * <p>
     * 用于表示没有找到匹配的国家补卡配置的情况
     * </p>
     *
     * @return 空的国家补卡配置对象
     */
    public static CountryReissueCardConfig empty() {
        return of(null, null);
    }

    /**
     * 查询国家级补卡配置
     * <p>
     * 国家级配置是指适用于整个国家的配置，通过 ReissueCardConfigDO.areCountryLevel() 方法判断
     * </p>
     *
     * @return 国家级补卡配置，如果不存在则返回null
     */
    public ReissueCardConfigDO queryCountryLevelConfig() {
        if (CollectionUtils.isEmpty(this.countryConfigs)) {
            return null;
        }
        return this.countryConfigs.stream()
                .filter(ReissueCardConfigDO::areCountryLevel)
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据部门ID查询适用的补卡配置
     * <p>
     * 查找配置列表中适用于指定部门ID的配置
     * </p>
     *
     * @param deptId 部门ID
     * @return 适用于指定部门的补卡配置，如果不存在则返回null
     */
    public ReissueCardConfigDO queryConfigByDeptId(Long deptId) {
        if (null == deptId) {
            return null;
        }
        if (CollectionUtils.isEmpty(this.countryConfigs)) {
            return null;
        }
        return this.countryConfigs.stream()
                .filter(configDO -> configDO.listDeptIds().contains(deptId))
                .findFirst()
                .orElse(null);
    }
}
