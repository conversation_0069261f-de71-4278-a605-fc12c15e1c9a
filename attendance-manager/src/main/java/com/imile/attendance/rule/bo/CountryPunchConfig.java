package com.imile.attendance.rule.bo;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;

import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/5/13 
 * @Description 国家打卡配置对象
 */
@Data
public class CountryPunchConfig {

    /**
     * 国家
     */
    private String country;

    /**
     * 国家打卡配置列表，包含该国家下所有的打卡配置
     */
    private List<PunchConfigDO> countryConfigs;

    /**
     * 创建国家打卡配置对象
     * 
     * @param country 国家
     * @param countryConfigs 国家打卡配置列表
     * @return 国家打卡配置对象
     */
    public static CountryPunchConfig of(String country, List<PunchConfigDO> countryConfigs) {
        CountryPunchConfig countryPunchConfig = new CountryPunchConfig();
        countryPunchConfig.setCountry(country);
        countryPunchConfig.setCountryConfigs(countryConfigs);
        return countryPunchConfig;
    }

    /**
     * 空的国家打卡配置对象
     * 
     * @return 空的国家打卡配置对象
     */
    public static CountryPunchConfig empty() {
        return of(null, Collections.emptyList());
    }

    /**
     * 根据国家查询打卡配置
     *
     * @return 打卡配置
     */
    public PunchConfigDO queryCountryLevelConfig() {
        if (CollectionUtils.isEmpty(this.countryConfigs)) {
            return null;
        }
        return this.countryConfigs.stream()
                .filter(PunchConfigDO::areCountryLevel)
                .findFirst()
                .orElse(null);
    }   

    /**
     * 根据部门ID查询打卡配置
     * 
     * @param deptId 部门ID
     * @return 打卡配置
     */
    public PunchConfigDO queryConfigByDeptId(Long deptId) {
        if (null == deptId) {
            return null;
        }
        if (CollectionUtils.isEmpty(this.countryConfigs)) {
            return null;
        }
        return this.countryConfigs.stream()
                .filter(configDO -> configDO.listDeptIds().contains(deptId))
                .findFirst()
                .orElse(null);
    }
}