package com.imile.attendance.rule;

import com.imile.attendance.infrastructure.repository.rule.dto.RuleConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.rule.bo.CountryOverTimeConfig;
import com.imile.attendance.rule.bo.OverTimeConfigBO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16
 * @Description
 */
public interface OverTimeConfigManage {

    /**
     * 更新或添加加班规则适用范围
     *
     * @param updateList 更新列表
     * @param addList    添加列表
     */
    void configRangeUpdateOrAdd(List<OverTimeConfigRangeDO> updateList, List<OverTimeConfigRangeDO> addList);

    /**
     * 更新或添加加班规则
     *
     * @param updateConfig 更新配置
     * @param addConfig    添加配置
     */
    void configUpdateAndAdd(OverTimeConfigDO updateConfig,
                            OverTimeConfigDO addConfig);

    /**
     * 更新或添加加班规则适用范围
     *
     * @param updateConfig        更新配置
     * @param addConfig           添加配置
     * @param updatedConfigRanges 更新适用范围
     * @param addConfigRanges     添加适用范围
     */
    void configUpdateAndAdd(OverTimeConfigDO updateConfig,
                            OverTimeConfigDO addConfig,
                            List<OverTimeConfigRangeDO> updatedConfigRanges,
                            List<OverTimeConfigRangeDO> addConfigRanges);

    /**
     * 获取加班规则BO
     *
     * @param configNo 配置编号
     * @return 加班规则BO
     */
    OverTimeConfigBO getConfigBO(String configNo);

    /**
     * 获取国家的加班规则
     *
     * @param country 国家
     * @return 国家的加班规则
     */
    CountryOverTimeConfig getCountryConfig(String country);

    /**
     * 批量获取国家的加班规则
     *
     * @param countries 国家列表
     * @return 国家的加班规则列表
     */
    List<CountryOverTimeConfig> getCountryListConfig(List<String> countries);

    /**
     * 根据用户ID列表获取加班规则
     *
     * @param userIds 用户ID列表
     * @return 加班规则Map
     */
    Map<Long, OverTimeConfigDO> getConfigMapByUserIdList(List<Long> userIds);

    /**
     * 根据用户ID列表和指定时间获取加班规则
     * 不过滤是否最新和状态
     * key: userId
     */
    Map<Long, OverTimeConfigDO> mapByUserIds(List<Long> userIds, Date endDate);

    /**
     * 根据用户ID获取加班规则
     *
     * @param userId 用户ID
     * @return 加班规则
     */
    OverTimeConfigBO getConfigBOByUserId(Long userId);

    /**
     * 查询用户所有加班规则
     */
    List<RuleConfigModifyDTO> selectAllByBizId(Long bizId);

}
