package com.imile.attendance.rule.impl;

import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigRangeDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.RuleConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.mapstruct.PunchClassConfigMapstruct;
import com.imile.attendance.rule.mapstruct.PunchClassConfigRangeMapstruct;
import com.imile.attendance.rule.mapstruct.PunchClassItemConfigMapstruct;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Component
public class PunchClassConfigManageImpl implements PunchClassConfigManage {
    @Resource
    private PunchClassConfigDao punchClassConfigDao;
    @Resource
    private PunchClassItemConfigDao punchClassItemConfigDao;
    @Resource
    private PunchClassConfigRangeDao punchClassConfigRangeDao;
    @Resource
    private UserInfoDao userInfoDao;


    @Override
    public PunchClassConfigDTO selectById(Long id) {
        PunchClassConfigDO punchClassConfigDO = punchClassConfigDao.selectById(id);
        if (Objects.isNull(punchClassConfigDO)) {
            return null;
        }
        PunchClassConfigDTO punchClassConfigDTO = PunchClassConfigMapstruct.INSTANCE.toPunchClassConfigDTO(punchClassConfigDO);
        List<PunchClassItemConfigDO> punchClassItemConfigDOList = punchClassItemConfigDao.selectByClassIds(Collections.singletonList(id));
        if (CollectionUtils.isNotEmpty(punchClassItemConfigDOList)) {
            List<PunchClassItemConfigDTO> punchClassItemConfigDTOList = PunchClassItemConfigMapstruct.INSTANCE.toPunchClassItemConfigDTOList(punchClassItemConfigDOList);
            punchClassConfigDTO.setClassItemConfigList(punchClassItemConfigDTOList);
        }
        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectByRuleConfigIds(Collections.singletonList(id));
        if (CollectionUtils.isEmpty(punchClassConfigRangeDOList)) {
            return punchClassConfigDTO;
        }
        List<PunchClassConfigRangeDTO> punchClassConfigRangeDTOList = PunchClassConfigRangeMapstruct.INSTANCE.toDTOList(punchClassConfigRangeDOList);
        punchClassConfigDTO.setClassConfigRangeList(punchClassConfigRangeDTOList);
        return punchClassConfigDTO;
    }

    @Override
    public PunchClassConfigDTO selectLatestAndActiveById(Long id) {
        PunchClassConfigDO punchClassConfigDO = punchClassConfigDao.selectLatestAndActiveById(id);
        if (Objects.isNull(punchClassConfigDO)) {
            return null;
        }
        PunchClassConfigDTO punchClassConfigDTO = PunchClassConfigMapstruct.INSTANCE.toPunchClassConfigDTO(punchClassConfigDO);
        List<PunchClassItemConfigDO> punchClassItemConfigDOList = punchClassItemConfigDao.selectByClassIds(Collections.singletonList(id))
                .stream()
                .filter(itemConfig -> Objects.equals(BusinessConstant.Y, itemConfig.getIsLatest())
                        && Objects.equals(StatusEnum.ACTIVE.getCode(), itemConfig.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(punchClassItemConfigDOList)) {
            List<PunchClassItemConfigDTO> punchClassItemConfigDTOList = PunchClassItemConfigMapstruct.INSTANCE.toPunchClassItemConfigDTOList(punchClassItemConfigDOList);
            punchClassConfigDTO.setClassItemConfigList(punchClassItemConfigDTOList);
        }
        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectByRuleConfigIds(Collections.singletonList(id))
                .stream()
                .filter(Range -> Objects.equals(BusinessConstant.Y, Range.getIsLatest())
                        && Objects.equals(StatusEnum.ACTIVE.getCode(), Range.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(punchClassConfigRangeDOList)) {
            return punchClassConfigDTO;
        }
        List<PunchClassConfigRangeDTO> punchClassConfigRangeDTOList = PunchClassConfigRangeMapstruct.INSTANCE.toDTOList(punchClassConfigRangeDOList);
        punchClassConfigDTO.setClassConfigRangeList(punchClassConfigRangeDTOList);
        return punchClassConfigDTO;
    }

    @Override
    public Map<Long, PunchClassConfigDTO> selectByIds(List<Long> ids) {
        List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectByIds(ids);
        if (CollectionUtils.isEmpty(punchClassConfigDOList)) {
            return Collections.emptyMap();
        }

        Map<Long, List<PunchClassItemConfigDO>> classItemGroupByClassId = punchClassItemConfigDao.selectByClassIds(ids)
                .stream().collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));

        Map<Long, List<PunchClassConfigRangeDO>> classRangeGroupByClassId = punchClassConfigRangeDao.selectByRuleConfigIds(ids)
                .stream().collect(Collectors.groupingBy(PunchClassConfigRangeDO::getRuleConfigId));

        Map<Long, PunchClassConfigDTO> result = new HashMap<>();
        for (PunchClassConfigDO punchClassConfigDO : punchClassConfigDOList) {
            PunchClassConfigDTO punchClassConfigDTO = PunchClassConfigMapstruct.INSTANCE.toPunchClassConfigDTO(punchClassConfigDO);
            result.put(punchClassConfigDO.getId(), punchClassConfigDTO);
            List<PunchClassItemConfigDO> punchClassItemConfigDOList = classItemGroupByClassId.get(punchClassConfigDO.getId());
            if (CollectionUtils.isNotEmpty(punchClassItemConfigDOList)) {
                List<PunchClassItemConfigDTO> punchClassItemConfigDTOList = PunchClassItemConfigMapstruct.INSTANCE.toPunchClassItemConfigDTOList(punchClassItemConfigDOList);
                punchClassConfigDTO.setClassItemConfigList(punchClassItemConfigDTOList);
            }
            List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = classRangeGroupByClassId.get(punchClassConfigDO.getId());
            if (CollectionUtils.isEmpty(punchClassConfigRangeDOList)) {
                continue;
            }
            List<PunchClassConfigRangeDTO> punchClassConfigRangeDTOList = PunchClassConfigRangeMapstruct.INSTANCE.toDTOList(punchClassConfigRangeDOList);
            punchClassConfigDTO.setClassConfigRangeList(punchClassConfigRangeDTOList);
        }
        return result;
    }

    @Override
    public List<PunchClassConfigDTO> selectLatestAndActiveLByIds(List<Long> ids) {
        List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectLatestAndActiveByIds(ids);
        if (CollectionUtils.isEmpty(punchClassConfigDOList)) {
            return Collections.emptyList();
        }
        Map<Long, List<PunchClassItemConfigDO>> classItemGroupByClassId = punchClassItemConfigDao.selectByClassIds(ids)
                .stream()
                .filter(itemConfig -> Objects.equals(StatusEnum.ACTIVE.getCode(), itemConfig.getStatus())
                        && Objects.equals(BusinessConstant.Y, itemConfig.getIsLatest()))
                .collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));
        List<PunchClassConfigDTO> result = new ArrayList<>();
        for (PunchClassConfigDO punchClassConfigDO : punchClassConfigDOList) {
            PunchClassConfigDTO punchClassConfigDTO = PunchClassConfigMapstruct.INSTANCE.toPunchClassConfigDTO(punchClassConfigDO);
            List<PunchClassItemConfigDO> punchClassItemConfigDOList = classItemGroupByClassId.get(punchClassConfigDO.getId());
            if (CollectionUtils.isNotEmpty(punchClassItemConfigDOList)) {
                List<PunchClassItemConfigDTO> punchClassItemConfigDTOList = PunchClassItemConfigMapstruct.INSTANCE.toPunchClassItemConfigDTOList(punchClassItemConfigDOList);
                punchClassConfigDTO.setClassItemConfigList(punchClassItemConfigDTOList);
            }
            result.add(punchClassConfigDTO);
        }
        return result;
    }

    @Override
    public List<PunchClassConfigDTO> selectByCountries(String country, String classNature) {
        List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectLatestAndActiveByCountry(Collections.singletonList(country), classNature);
        if (CollectionUtils.isEmpty(punchClassConfigDOList)) {
            return Collections.emptyList();
        }
        List<Long> classIds = punchClassConfigDOList.stream().map(PunchClassConfigDO::getId).collect(Collectors.toList());
        Map<Long, List<PunchClassItemConfigDO>> punchClassItemConfigMap = punchClassItemConfigDao.selectByClassIds(classIds)
                .stream().collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));

        Map<Long, List<PunchClassConfigRangeDO>> punchClassConfigRangeMap = punchClassConfigRangeDao.selectLatestAndActiveByRuleConfigIds(classIds)
                .stream().collect(Collectors.groupingBy(PunchClassConfigRangeDO::getRuleConfigId));
        List<PunchClassConfigDTO> result = new ArrayList<>();
        for (PunchClassConfigDO punchClassConfigDO : punchClassConfigDOList) {
            PunchClassConfigDTO punchClassConfigDTO = PunchClassConfigMapstruct.INSTANCE.toPunchClassConfigDTO(punchClassConfigDO);
            result.add(punchClassConfigDTO);
            List<PunchClassItemConfigDTO> punchClassItemConfigDTOList = PunchClassItemConfigMapstruct.INSTANCE.toPunchClassItemConfigDTOList(punchClassItemConfigMap.get(punchClassConfigDO.getId()));
            punchClassConfigDTO.setClassItemConfigList(punchClassItemConfigDTOList);
            List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeMap.get(punchClassConfigDO.getId());
            if (CollectionUtils.isEmpty(punchClassConfigRangeDOList)) {
                continue;
            }
            List<PunchClassConfigRangeDTO> punchClassConfigRangeDTOList = PunchClassConfigRangeMapstruct.INSTANCE.toDTOList(punchClassConfigRangeDOList);
            punchClassConfigDTO.setClassConfigRangeList(punchClassConfigRangeDTOList);
        }
        return result;
    }


    @Override
    public Map<Long, List<PunchClassConfigDO>> selectUserMultipleClassConfigList(List<Long> userIdList) {
        Map<Long, List<PunchClassConfigDO>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(userIdList)) {
            return result;
        }

        //用户适用范围匹配
        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(userIdList);

        Map<Long, List<Long>> groupByBizId = punchClassConfigRangeDOList.stream()
                .collect(Collectors.groupingBy(PunchClassConfigRangeDO::getBizId, Collectors.mapping(PunchClassConfigRangeDO::getRuleConfigId, Collectors.toList())));

        for (Long userId : groupByBizId.keySet()) {
            List<Long> classIds = groupByBizId.get(userId);
            List<PunchClassConfigDO> punchClassConfigList = punchClassConfigDao.selectByIds(classIds);
            if (CollectionUtils.isEmpty(punchClassConfigList)) {
                continue;
            }
            result.put(userId, punchClassConfigList);
        }
        return result;
    }


    @Override
    public Map<Long, PunchClassConfigDO> selectTopPriorityByUserIds(Collection<Long> userIdList) {
        Map<Long, PunchClassConfigDO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(userIdList)) {
            return result;
        }

        UserDaoQuery userQuery = UserDaoQuery.builder()
                .userIds(new ArrayList<>(userIdList))
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .classNature(ClassNatureEnum.FIXED_CLASS.name())
                .isDriver(BusinessConstant.N)
                .build();
        List<UserInfoDO> userInfoDOList = userInfoDao.userList(userQuery);
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return result;
        }
        //有效用户集合
        List<Long> userIds = userInfoDOList.stream().map(UserInfoDO::getId).collect(Collectors.toList());

        //用户适用范围匹配
        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(userIds);
        if (CollectionUtils.isNotEmpty(punchClassConfigRangeDOList)) {
            List<Long> ruleConfigIds = punchClassConfigRangeDOList.stream().map(PunchClassConfigRangeDO::getRuleConfigId).distinct().collect(Collectors.toList());
            Map<Long, PunchClassConfigDO> punchClassConfigMap = punchClassConfigDao.selectByIds(ruleConfigIds).stream().collect(Collectors.toMap(PunchClassConfigDO::getId, Function.identity()));

            Map<Long, List<PunchClassConfigRangeDO>> groupByBizId = punchClassConfigRangeDOList.stream().collect(Collectors.groupingBy(PunchClassConfigRangeDO::getBizId));
            for (Long userId : groupByBizId.keySet()) {
                List<PunchClassConfigRangeDO> punchClassConfigRangeList = groupByBizId.get(userId);
                punchClassConfigRangeList.sort((o1, o2) -> {
                    RuleRangeTypeEnum type1 = Optional.ofNullable(RuleRangeTypeEnum.getInstance(o1.getRangeType()))
                            .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.RULE_RANGE_TYPE_INVALID.getCode(), I18nUtils.getMessage(ErrorCodeEnum.RULE_RANGE_TYPE_INVALID.getDesc())));
                    RuleRangeTypeEnum type2 = Optional.ofNullable(RuleRangeTypeEnum.getInstance(o2.getRangeType()))
                            .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.RULE_RANGE_TYPE_INVALID.getCode(), I18nUtils.getMessage(ErrorCodeEnum.RULE_RANGE_TYPE_INVALID.getDesc())));
                    return Integer.compare(type1.getPriority(), type2.getPriority());
                });
                PunchClassConfigDO punchClassConfigDO = punchClassConfigMap.get(punchClassConfigRangeList.get(0).getRuleConfigId());
                if (Objects.isNull(punchClassConfigDO)) {
                    continue;
                }
                result.put(userId, punchClassConfigDO);
            }
        }
        return result;
    }

    @Override
    public Set<Long> selectDisabledByUserIds(Collection<Long> userIdList) {
        Set<Long> classIds = new HashSet<>();
        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectByBizIdsAndStatus(userIdList, StatusEnum.DISABLED.getCode())
                .stream().filter(rang -> Objects.equals(BusinessConstant.Y, rang.getIsLatest())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(punchClassConfigRangeDOList)) {
            return classIds;
        }
        return punchClassConfigRangeDOList.stream().map(PunchClassConfigRangeDO::getRuleConfigId).collect(Collectors.toSet());
    }

    @Override
    public List<PunchClassConfigDTO> selectUserClassConfigList(Long userId, String classNature) {
        //用户适用范围匹配
        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectLatestAndActiveByBizIds(Collections.singletonList(userId));
        Map<Long, List<PunchClassConfigRangeDO>> groupByRuleConfig = punchClassConfigRangeDOList.stream().collect(Collectors.groupingBy(PunchClassConfigRangeDO::getRuleConfigId));
        List<Long> classIds = punchClassConfigRangeDOList.stream().map(PunchClassConfigRangeDO::getRuleConfigId).distinct().collect(Collectors.toList());

        List<PunchClassConfigDO> punchClassConfigDOS = punchClassConfigDao.selectByIds(classIds);
        List<PunchClassConfigDTO> result = PunchClassConfigMapstruct.INSTANCE.toPunchClassConfigDTOList(punchClassConfigDOS);

        for (PunchClassConfigDTO punchClassConfigDTO : result) {
            List<PunchClassConfigRangeDO> punchClassConfigRangeList = groupByRuleConfig.get(punchClassConfigDTO.getId());
            if (CollectionUtils.isEmpty(punchClassConfigRangeList)) {
                continue;
            }
            List<PunchClassConfigRangeDTO> rangeDTOList = PunchClassConfigRangeMapstruct.INSTANCE.toDTOList(punchClassConfigRangeList);
            punchClassConfigDTO.setClassConfigRangeList(rangeDTOList);
        }
        return result;
    }

    @Override
    public PunchClassConfigDTO selectTopPriorityByDeptIdOrCountry(Long deptId, String country) {
        List<PunchClassConfigDO> punchClassConfigList = punchClassConfigDao.selectLatestAndActiveByDeptId(deptId, ClassNatureEnum.FIXED_CLASS.name());
        if (CollectionUtils.isNotEmpty(punchClassConfigList)) {
            return PunchClassConfigMapstruct.INSTANCE.toPunchClassConfigDTO(punchClassConfigList.get(0));
        }
        List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectLatestCountryRange(Collections.singletonList(country), ClassNatureEnum.FIXED_CLASS.name());
        if (CollectionUtils.isNotEmpty(punchClassConfigDOList)) {
            return PunchClassConfigMapstruct.INSTANCE.toPunchClassConfigDTO(punchClassConfigDOList.get(0));
        }
        return null;
    }

    @Override
    public Set<Long> selectLatestClassIdListByUserIds(List<Long> userIdList, String classNature) {
        Set<Long> classIdList = new HashSet<>();
        if (Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), classNature)) {
            Set<Long> classIds = punchClassConfigRangeDao.selectLatestByBizIds(userIdList).stream().map(PunchClassConfigRangeDO::getRuleConfigId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(classIds)) {
                return classIdList;
            }
            return classIds;
        }

        //固定班次查询最高优先级
        Map<Long, PunchClassConfigDO> punchClassConfigMap = this.selectTopPriorityByUserIds(userIdList);
        if (MapUtils.isNotEmpty(punchClassConfigMap)) {
            classIdList.addAll(punchClassConfigMap.values().stream().map(PunchClassConfigDO::getId).collect(Collectors.toList()));
        }

        //查询用户停用的班次集合
        Set<Long> disabledClassIds = this.selectDisabledByUserIds(userIdList);
        if (CollectionUtils.isNotEmpty(disabledClassIds)) {
            classIdList.addAll(disabledClassIds);
        }
        return classIdList;
    }

    @Override
    public Map<Long, List<PunchClassConfigDTO>> mapByUserIds(List<Long> userIds, Date endDate) {
        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectAllByBizIds(userIds).stream()
                .filter(item -> item.getEffectTime().compareTo(endDate) < 1 &&
                        item.getExpireTime().compareTo(endDate) > -1)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(punchClassConfigRangeDOList)) {
            return Collections.emptyMap();
        }

        //key:userId , value:List<ruleConfigId>
        Map<Long, List<Long>> rangeGroupMap = punchClassConfigRangeDOList.stream()
                .collect(Collectors.groupingBy(PunchClassConfigRangeDO::getBizId, Collectors.mapping(PunchClassConfigRangeDO::getRuleConfigId, Collectors.toList())));

        List<Long> classIds = rangeGroupMap.values().stream()
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, PunchClassConfigDO> punchClassConfigMap = punchClassConfigDao.selectByIds(classIds)
                .stream()
                .collect(Collectors.toMap(PunchClassConfigDO::getId, Function.identity(), (a, b) -> a));

        Map<Long, List<PunchClassItemConfigDO>> itemConfigGroupMap = punchClassItemConfigDao.selectByClassIds(classIds)
                .stream()
                .collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));

        Map<Long, List<PunchClassConfigDTO>> result = new HashMap<>();
        for (Long userId : rangeGroupMap.keySet()) {
            List<Long> classIdList = rangeGroupMap.get(userId);
            List<PunchClassConfigDTO> punchClassConfigDTOList = new ArrayList<>();
            for (Long classId : classIdList) {
                if (Objects.isNull(punchClassConfigMap.get(classId))) {
                    continue;
                }
                PunchClassConfigDTO punchClassConfigDTO = PunchClassConfigMapstruct.INSTANCE.toPunchClassConfigDTO(punchClassConfigMap.get(classId));
                punchClassConfigDTOList.add(punchClassConfigDTO);

                List<PunchClassItemConfigDO> punchClassItemConfigDOList = itemConfigGroupMap.get(classId);
                if (CollectionUtils.isEmpty(punchClassItemConfigDOList)) {
                    continue;
                }
                punchClassConfigDTO.setClassItemConfigList(PunchClassItemConfigMapstruct.INSTANCE.toPunchClassItemConfigDTOList(punchClassItemConfigDOList));
            }
            result.put(userId, punchClassConfigDTOList);
        }
        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void punchClassConfigAdd(PunchClassConfigDO punchClassConfigDO,
                                    List<PunchClassItemConfigDO> punchClassItemConfigList,
                                    List<PunchClassConfigRangeDO> punchClassConfigRangeList,
                                    List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList) {
        if (Objects.nonNull(punchClassConfigDO) && Objects.nonNull(punchClassConfigDO.getId())) {
            punchClassConfigDao.save(punchClassConfigDO);
        }

        if (CollectionUtils.isNotEmpty(punchClassItemConfigList)) {
            punchClassItemConfigDao.saveBatch(punchClassItemConfigList);
        }

        if (CollectionUtils.isNotEmpty(punchClassConfigRangeList)) {
            List<List<PunchClassConfigRangeDO>> partitionList = Lists.partition(punchClassConfigRangeList, 500);
            partitionList.forEach(list -> punchClassConfigRangeDao.saveBatch(list));
        }

        if (CollectionUtils.isNotEmpty(updatePunchClassConfigRangeDOList)) {
            List<List<PunchClassConfigRangeDO>> partitionList = Lists.partition(updatePunchClassConfigRangeDOList, 500);
            partitionList.forEach(list -> punchClassConfigRangeDao.updateBatchById(list));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void punchClassConfigUpdate(List<PunchClassConfigDO> updatePunchClassConfigDOList,
                                       List<PunchClassItemConfigDO> updatePunchClassItemConfigList,
                                       List<PunchClassConfigRangeDO> updatePunchClassConfigRangeList) {

        if (CollectionUtils.isNotEmpty(updatePunchClassConfigDOList)) {
            punchClassConfigDao.updateBatchById(updatePunchClassConfigDOList);
        }

        if (CollectionUtils.isNotEmpty(updatePunchClassItemConfigList)) {
            punchClassItemConfigDao.updateBatchById(updatePunchClassItemConfigList);
        }

        if (CollectionUtils.isNotEmpty(updatePunchClassConfigRangeList)) {
            List<List<PunchClassConfigRangeDO>> partitionList = Lists.partition(updatePunchClassConfigRangeList, 500);
            partitionList.forEach(list -> punchClassConfigRangeDao.updateBatchById(list));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void punchClassConfigRangeUpdate(List<PunchClassConfigRangeDO> updatePunchClassConfigRangeList, List<PunchClassConfigRangeDO> addPunchClassConfigRangeList) {
        if (CollectionUtils.isNotEmpty(updatePunchClassConfigRangeList)) {
            List<List<PunchClassConfigRangeDO>> partitionList = Lists.partition(updatePunchClassConfigRangeList, 500);
            partitionList.forEach(list -> punchClassConfigRangeDao.updateBatchById(list));
        }
        if (CollectionUtils.isNotEmpty(addPunchClassConfigRangeList)) {
            List<List<PunchClassConfigRangeDO>> partitionList = Lists.partition(addPunchClassConfigRangeList, 500);
            partitionList.forEach(list -> punchClassConfigRangeDao.saveBatch(list));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enableStatus(Long classId,
                             List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList) {

        punchClassConfigDao.enableStatus(classId);
        punchClassItemConfigDao.enableStatus(classId);
        punchClassConfigRangeDao.updateToOld(classId);

        if (CollectionUtils.isNotEmpty(addPunchClassConfigRangeDOList)) {
            punchClassConfigRangeDao.saveBatch(addPunchClassConfigRangeDOList);
        }
    }

    @Override
    public void disabledStatus(Long classId, Date currentTime) {
        if (Objects.isNull(classId)) {
            return;
        }
        punchClassConfigDao.disabledStatus(classId);
        punchClassItemConfigDao.disabledStatus(classId);
        punchClassConfigRangeDao.disabledStatus(classId, currentTime);
    }

    @Override
    public void updateRangeByBizIdAndClassId(Long userId, List<Long> classIds, Date expireTime) {
        if (Objects.isNull(userId) || CollectionUtils.isEmpty(classIds)) {
            return;
        }
        punchClassConfigRangeDao.updateRangeByBizIdAndClassId(userId, classIds, expireTime);
    }

    @Override
    public List<RuleConfigModifyDTO> selectAllByBizId(Long bizId) {
        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectAllByBizIds(Collections.singletonList(bizId));
        if (CollectionUtils.isEmpty(punchClassConfigRangeDOList)) {
            return Collections.emptyList();
        }
        List<Long> ruleConfigIdList = punchClassConfigRangeDOList.stream().map(PunchClassConfigRangeDO::getRuleConfigId).distinct().collect(Collectors.toList());
        Map<Long, PunchClassConfigDO> punchClassConfigDOMap = punchClassConfigDao.selectByIds(ruleConfigIdList)
                .stream().collect(Collectors.toMap(PunchClassConfigDO::getId, Function.identity()));
        return punchClassConfigRangeDOList.stream().map(range -> {
            RuleConfigModifyDTO modifyDTO = new RuleConfigModifyDTO();
            PunchClassConfigDO punchClassConfigDO = punchClassConfigDOMap.getOrDefault(range.getRuleConfigId(), new PunchClassConfigDO());
            modifyDTO.setCountry(punchClassConfigDO.getCountry());
            modifyDTO.setRuleName(punchClassConfigDO.getClassName());
            modifyDTO.setStartDate(range.getEffectTime());
            modifyDTO.setEndDate(range.getExpireTime());
            modifyDTO.setCreateUserName(range.getCreateUserName());
            return modifyDTO;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void classRangeBatchProcess(List<PunchClassConfigRangeDO> list, Consumer<List<PunchClassConfigRangeDO>> operation) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Lists.partition(list, 500).forEach(operation);
    }

    @Override
    public List<PunchClassConfigDO> selectByClassIds(List<Long> classIdList) {
        return punchClassConfigDao.selectByIds(classIdList);
    }

    @Override
    public List<PunchClassItemConfigDO> selectLatestByClassIds(List<Long> classIdList) {
        return punchClassItemConfigDao.selectLatestByClassIds(classIdList);
    }

    @Override
    public List<PunchClassConfigDO> selectByCountries(Collection<String> countryList) {
        return punchClassConfigDao.selectByCountries(countryList);
    }
}
