package com.imile.attendance.rule.mapstruct;


import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2025/4/8
 */
@Mapper(config = MapperConfiguration.class)
public interface PunchClassItemConfigMapstruct {

    PunchClassItemConfigMapstruct INSTANCE = Mappers.getMapper(PunchClassItemConfigMapstruct.class);

    List<PunchClassItemConfigDTO> toPunchClassItemConfigDTOList(List<PunchClassItemConfigDO> punchClassItemConfigDOList);

    List<PunchClassItemConfigDO> toDOList(List<PunchClassItemConfigDTO> dtoList);

}
