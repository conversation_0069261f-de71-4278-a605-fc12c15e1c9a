package com.imile.attendance.punch.bo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Data
public class UserPunchRecordBO {
    /**
     * 打卡记录ID
     */
    private Long id;

    /**
     * 用户账号
     */
    private String userCode;

    /**
     * 审批单ID
     */
    private Long formId;

    /**
     * 转换后的打卡时间，无视秒
     */
    private Date formatPunchTime;

    /**
     * 打卡区域
     */
    private String punchArea;

    /**
     * 班次ID
     */
    private Long classId;
}
