package com.imile.attendance.clover.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Data
public class UserAttendanceCalendarQuery {

    /**
     * userId
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;
    /**
     * 年
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long year;
    /**
     * 当前日期(时间戳)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateTime;
}
