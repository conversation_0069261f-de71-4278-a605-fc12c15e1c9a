package com.imile.attendance.clover.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Data
public class DayPunchClassItemInfoDTO {

    /**
     * 时刻ID
     */
    private Long id;

    /**
     * 序号
     */
    private Integer sortNo;

    /**
     * 上班时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：1970-01-01 18:00:00
     */
    private Date punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date earliestPunchInTime;

    /**
     * 最晚上班打卡时间 仅有时分秒信息
     */

    private Date latestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：1970-01-01 19:00:00
     */
    private Date latestPunchOutTime;

    /**
     * 弹性时间
     */
    private BigDecimal elasticTime;

    /**
     * 休息开始时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date restStartTime;

    /**
     * 休息结束时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date restEndTime;
}
