package com.imile.attendance.clover.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Data
public class UserAttendanceCycleDetailDTO {

    /**
     * 日期对应的考勤周期起始时间
     */
    private Date attendanceStartDate;

    /**
     * 日期对应的考勤周期截止时间
     */
    private Date attendanceEndDate;

    /**
     * 今日班次名称
     */
    private String punchConfigClassName;

    /**
     * 今日打卡规则对应的班次的所有的时刻信息
     */
    private String punchConfigClassItemInfo;

    /**
     * 周期内的异常考勤个数
     */
    private Integer abnormalCount;

    /**
     * 周期内的所有异常信息(处理和未处理的都需要)
     */
    private List<UserAttendanceCycleAbnormalDTO> userAttendanceCycleAbnormalDTOList;
}
