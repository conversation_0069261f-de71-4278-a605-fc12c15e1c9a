package com.imile.attendance.clover.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Data
public class UserAttendanceCycleAbnormalDTO {

    /**
     * 异常ID
     */
    private Long abnormalId;

    /**
     * 补卡类型
     */
    private String reissueCardType;

    /**
     * 补卡类型描述
     */
    private String reissueCardTypeDesc;

    /**
     * 实际打卡时间(没有就为空)，有多个取离上下班最近的一个
     */
    private Date actualPunchTime;

    /**
     * 补卡后的时间(根据时刻时间来补)
     */
    private Date correctPunchTime;

    /**
     * 异常状态
     */
    private String abnormalStatus;
}
