package com.imile.attendance.clover.mapstruct;

import com.imile.attendance.clover.dto.DayAttendancePassFormDTO;
import com.imile.attendance.clover.dto.DayPunchClassItemInfoDTO;
import com.imile.attendance.clover.dto.UserDayAttendanceInfoDTO;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/26 
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface CloverAttendanceMapstruct {

    CloverAttendanceMapstruct INSTANCE = Mappers.getMapper(CloverAttendanceMapstruct.class);


    @Mapping(target = "concreteTypeMinutes", ignore = true)
    UserDayAttendanceInfoDTO toUserDayAttendanceInfoDTO(AttendanceEmployeeDetailDO attendanceEmployeeDetailDO);

    List<UserDayAttendanceInfoDTO> toUserDayAttendanceInfoDTO(List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList);


    DayPunchClassItemInfoDTO toDayPunchClassItemInfoDTO(PunchClassItemConfigDO punchClassItemConfigDO);

    List<DayPunchClassItemInfoDTO> toDayPunchClassItemInfoDTO(List<PunchClassItemConfigDO> punchClassItemConfigDOList);


    default DayAttendancePassFormDTO toDayAttendancePassFormDTO(AttendanceFormDO formDO, Date startDate, Date endDate) {
        DayAttendancePassFormDTO dayAttendancePassFormDTO = new DayAttendancePassFormDTO();
        dayAttendancePassFormDTO.setFormId(formDO.getId());
        dayAttendancePassFormDTO.setApprovalId(formDO.getApprovalId());
        dayAttendancePassFormDTO.setFormType(formDO.getFormType());
        FormTypeEnum formTypeEnum = FormTypeEnum.getInstance(formDO.getFormType());
        if (formTypeEnum != null) {
            dayAttendancePassFormDTO.setFormTypeDesc(RequestInfoHolder.isChinese() ?
                    formTypeEnum.getDesc() : formTypeEnum.getDescEn());
        }
        dayAttendancePassFormDTO.setStartDate(startDate);
        dayAttendancePassFormDTO.setEndDate(endDate);
        return dayAttendancePassFormDTO;
    }
}
