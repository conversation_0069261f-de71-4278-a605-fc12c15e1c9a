package com.imile.attendance.logRecord;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.logRecord.enums.OperationCodeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.OperationModuleEnum;
import com.imile.attendance.infrastructure.logRecord.mapstruct.LogRecordMapstruct;
import com.imile.attendance.infrastructure.repository.log.dao.LogOperationRecordDao;
import com.imile.attendance.infrastructure.repository.log.dto.LogRecordPageDTO;
import com.imile.attendance.infrastructure.repository.log.model.LogOperationRecordDO;
import com.imile.attendance.infrastructure.repository.log.query.LogRecordPageQuery;
import com.imile.attendance.util.PageUtil;
import com.imile.common.page.PaginationResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
@Component
public class LogOperationRecordService {

    @Resource
    private LogOperationRecordDao logOperationRecordDao;

    public PaginationResult<LogRecordPageDTO> page(LogRecordPageQuery logRecordPageQuery) {
        String operationCode = logRecordPageQuery.getOperationCode();
        OperationCodeEnum operationCodeEnum = OperationCodeEnum.getOperationCode(operationCode);
        if (operationCodeEnum == null) {
            throw BusinessLogicException.getException(ErrorCodeEnum.LOG_RECORD_MODULE_NOT_EXIST_OR_NOT_SUPPROT);
        }

        PageInfo<LogOperationRecordDO> pageInfo = PageHelper.startPage(logRecordPageQuery.getCurrentPage(), logRecordPageQuery.getShowCount())
                .doSelectPageInfo(() -> logOperationRecordDao.listPage(logRecordPageQuery));
        List<LogOperationRecordDO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), logRecordPageQuery);
        }
        List<LogRecordPageDTO> logRecordPageDTOS = LogRecordMapstruct.INSTANCE.mapToPageDTO(list);
        return PageUtil.getPageResult(logRecordPageDTOS, logRecordPageQuery, (int) pageInfo.getTotal(), pageInfo.getPages());
    }
}
