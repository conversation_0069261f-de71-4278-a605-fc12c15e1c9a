package com.imile.attendance.punch.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.attendance.common.AttendanceCountryService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.deviceConfig.AttendanceGpsConfigService;
import com.imile.attendance.deviceConfig.AttendanceWifiConfigService;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigDTO;
import com.imile.attendance.enums.punch.SourceTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceGpsConfigQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordInspectionDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordInspectionDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchRecordInspectionQuery;
import com.imile.attendance.punch.param.EmployeePunchRecordInspectionParam;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 考勤打卡记录异常巡检服务(拷贝HRMS原有逻辑)
 *
 * <AUTHOR>
 * @menu 打卡记录异常巡检服务
 * @date 2025/5/10
 */
@Slf4j
@Service
public class EmployeePunchRecordInspectionService {

    @Resource
    private AttendanceCountryService attendanceCountryService;
    @Resource
    private AttendanceGpsConfigService attendanceGpsConfigService;
    @Resource
    private AttendanceWifiConfigService attendanceWifiConfigService;
    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Resource
    private EmployeePunchRecordInspectionDao employeePunchRecordInspectionDao;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private CountryService countryService;

    /**
     * 考勤打卡记录异常巡检定时任务处理类
     *
     * @param param
     * @return
     */
    public void employeePunchRecordInspectionHandler(EmployeePunchRecordInspectionParam param) {
        // 处理未打卡人员每日考勤
        List<String> countryList = param.getCountryList();
        // 根据国家获取wifi与gps的配置信息
        AttendanceGpsConfigQuery gpsQuery = AttendanceGpsConfigQuery.builder()
                .countryList(countryList)
                .build();

        // gps配置信息
        List<AttendanceGpsConfigDTO> gpsConfigList = attendanceGpsConfigService.selectList(gpsQuery);
        // 将gpsConfigList按照国家分组
        AttendanceWifiConfigQuery wifiQuery = AttendanceWifiConfigQuery.builder()
                .countryList(countryList)
                .build();
        // wifi配置信息
        List<AttendanceWifiConfigDTO> wifiConfigList = attendanceWifiConfigService.selectList(wifiQuery);

        // 过滤gps或wifi配置信息的数据
        if (CollUtil.isNotEmpty(param.getGpsOrWifiConfigIdList())) {
            // 过滤出来是gpsOrWifiConfigIdList配置里面的gps或wifi数据
            gpsConfigList = gpsConfigList.stream()
                    .filter(e -> param.getGpsOrWifiConfigIdList().contains(e.getId()))
                    .collect(Collectors.toList());
            wifiConfigList = wifiConfigList.stream()
                    .filter(e -> param.getGpsOrWifiConfigIdList().contains(e.getId()))
                    .collect(Collectors.toList());
        }

        // 将gps跟wifi数据整合,国家相同的主键id合并
        Map<String, List<Long>> targetMap = Maps.newHashMap();

        // 处理wifiConfigList
        for (AttendanceWifiConfigDTO wifiConfig : wifiConfigList) {
            String country = wifiConfig.getCountry();
            Long id = wifiConfig.getId();

            targetMap.computeIfAbsent(country, k -> new ArrayList<>()).add(id);
        }

        // 处理gpsConfigList
        for (AttendanceGpsConfigDTO gpsConfig : gpsConfigList) {
            String country = gpsConfig.getCountry();
            Long id = gpsConfig.getId();

            targetMap.computeIfAbsent(country, k -> new ArrayList<>()).add(id);
        }

        // 获取每个国家对应的时区
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryNames(countryList);
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
        Map<String, String> countryConfigMap = countryConfigList.stream()
                .collect(Collectors.toMap(CountryConfigDTO::getCountryName, CountryConfigDTO::getTimeZone));

        // 获取对应国家的当前时间
        Map<String, Date> countryCurrentDate = attendanceCountryService.getCountryCurrentDate(countryList);

        // 目标集合
        List<EmployeePunchRecordInspectionDO> targetEmployeePunchRecordInspectionList = Lists.newArrayList();

        // 遍历gpsCountryMap
        for (Map.Entry<String, List<Long>> gpsEntry : targetMap.entrySet()) {
            String country = gpsEntry.getKey();
            List<Long> gpsWifiConfigInfoList = gpsEntry.getValue();
            if (ObjectUtil.isEmpty(country)) {
                log.info("employeePunchRecordInspectionHandler country is empty");
                continue;
            }
            if (CollUtil.isEmpty(gpsWifiConfigInfoList)) {
                log.info("employeePunchRecordInspectionHandler gpsConfigInfoList is empty");
                continue;
            }

            // 获取国家对应时区
            String timeZone = countryConfigMap.getOrDefault(country, "");
            if (ObjectUtil.equal(timeZone, "")) {
                log.info("country:{},not exist timeZone", country);
                continue;
            }

            //Long inspectDayId = param.getInspectDayId();
            Date date = countryCurrentDate.get(country);
            String inspectDate = param.getInspectDate();
            if (ObjectUtil.isNotEmpty(inspectDate)) {
                date = DateUtil.parse(inspectDate, DatePattern.NORM_DATETIME_PATTERN);
            }
            if (ObjectUtil.isNull(date)) {
                log.info("employeePunchRecordInspectionHandler country:{} date is null", country);
                continue;
            }

            Long dayId = Long.valueOf(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN));
            int year = DateUtil.year(date);
            int month = DateUtil.month(date) + 1;
            int day = DateUtil.dayOfMonth(date);
            // 校验当前时间dateTime是不是凌晨四点钟
            int hour = DateUtil.hour(date, true);
            log.info("handlerPunchRecordCalculateAttendance locationCountry：{}，localYear：{}，localMonth：{}，localDay：{}，localHour：{},serverDate：{}", country, year, month, day, hour, date);
            // 如果该国家现在不是凌晨0点钟，就不执行。只有每一个国家凌晨四点钟的时候执行后续业务
            if (ObjectUtil.notEqual(hour, 0)) {
                continue;
            }
            log.info("国家：{}，时间：{}，开始执行", country, dayId);

            // 获取当前时间减一天
            DateTime preDateTime = DateUtil.offsetDay(date, -1);

            log.info("employeePunchRecordInspectionHandler country: {},date:{}", country, DateUtil.format(preDateTime, DatePattern.NORM_DATETIME_PATTERN));
            // 根据preDayId获取这一天的开始时间、结束时间。
            DateTime startTime = DateUtil.beginOfDay(preDateTime);
            DateTime endTime = DateUtil.beginOfDay(date);
            // 分页查询打卡记录中gps_config_id是这个国家下配置的id的数据
            // 查询指定国家考勤打卡记录
            EmployeePunchCardRecordQuery query = new EmployeePunchCardRecordQuery();
            // 根据创建时间来塞选数据
            query.setCreateStartTime(startTime);
            query.setCreateEndTime(endTime);
            query.setSourceType(SourceTypeEnum.USER.name());
            query.setGpsOrWifiConfigIds(gpsWifiConfigInfoList);
            if (CollUtil.isNotEmpty(param.getUserCodeList())) {
                query.setUserCodes(param.getUserCodeList());
            }
            pageHandlerPunchRecord(country, param, query, timeZone, targetEmployeePunchRecordInspectionList);

        }

        log.info("employeePunchRecordInspectionHandler targetEmployeePunchRecordInspectionList size:{}", targetEmployeePunchRecordInspectionList.size());
        log.info("employeePunchRecordInspectionHandler targetEmployeePunchRecordInspectionList:{}", JSON.toJSONString(targetEmployeePunchRecordInspectionList));
        if (ObjectUtil.equal(Boolean.TRUE, param.getIsSaveToDb())) {
            // 开始落库
            log.info("employeePunchRecordInspectionHandler save to db start");
            employeePunchRecordInspectionDao.saveBatch(targetEmployeePunchRecordInspectionList);
        }
    }

    /**
     * 分页查询打卡记录
     *
     * @param country                                 国家
     * @param param                                   巡检参数
     * @param query                                   查询条件
     * @param timeZone                                国家对应时区
     * @param targetEmployeePunchRecordInspectionList 目标数据集合
     */
    private void pageHandlerPunchRecord(String country,
                                        EmployeePunchRecordInspectionParam param,
                                        EmployeePunchCardRecordQuery query,
                                        String timeZone,
                                        List<EmployeePunchRecordInspectionDO> targetEmployeePunchRecordInspectionList) {
        int currentPage = 1;
        int pageSize = param.getPageSize();
        Page<EmployeePunchRecordDO> page = PageHelper.startPage(currentPage, pageSize, true);
        PageInfo<EmployeePunchRecordDO> pageInfo = page.doSelectPageInfo(() -> employeePunchRecordDao.listRecords(query));

        // 总记录数
        List<EmployeePunchRecordDO> pageEmployeePunchRecordInfoList = pageInfo.getList();
        if (CollUtil.isNotEmpty(pageEmployeePunchRecordInfoList)) {
            log.info("employeePunchRecordInspectionHandler pageEmployeePunchRecordInfoList size:{}，pageEmployeePunchRecordInfoList：{}", pageEmployeePunchRecordInfoList.size(), JSON.toJSONString(pageEmployeePunchRecordInfoList));
            handlerPunchRecord(country, pageEmployeePunchRecordInfoList, timeZone, param, targetEmployeePunchRecordInspectionList);
        }
        log.info("country：{},pageUserInfoHandle | currentPage:{},pageSize:{},total:{}", country, currentPage, pageSize, pageInfo.getTotal());

        log.info("country：{},currentPage {}，pages：{}", country, currentPage, pageInfo.getPages());

        while (currentPage < pageInfo.getPages()) {
            log.info("country：{},enter while cycle", country);
            currentPage++;
            log.info("country：{},currentPage：{}，pages：{}", country, currentPage, pageInfo.getPages());

            page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> employeePunchRecordDao.listRecords(query));
            pageEmployeePunchRecordInfoList = pageInfo.getList();
            if (CollUtil.isNotEmpty(pageEmployeePunchRecordInfoList)) {
                log.info("country：{},,while cycle：pageUserInfoList size:{}，pageUserInfoList：{}", country, pageEmployeePunchRecordInfoList.size(), JSON.toJSONString(pageEmployeePunchRecordInfoList));
                handlerPunchRecord(country, pageEmployeePunchRecordInfoList, timeZone, param, targetEmployeePunchRecordInspectionList);
            }
            log.info("country：{},while cycle：pageUserInfoHandle | currentPage:{},pageSize:{},total:{}", country, currentPage, pageSize, pageInfo.getTotal());
            log.info("country：{},currentPage {}，while cycle end", country, currentPage);
        }

    }

    /**
     * 处理打卡记录数据
     *
     * @param country                                 国家
     * @param pageEmployeePunchRecordInfoList         打卡记录数据
     * @param timeZone                                国家对应时区
     * @param param                                   参数
     * @param targetEmployeePunchRecordInspectionList 目标打卡记录集合
     */
    private void handlerPunchRecord(String country,
                                    List<EmployeePunchRecordDO> pageEmployeePunchRecordInfoList,
                                    String timeZone,
                                    EmployeePunchRecordInspectionParam param,
                                    List<EmployeePunchRecordInspectionDO> targetEmployeePunchRecordInspectionList) {

        Long gpsIntervalMinutes = param.getGpsIntervalMinutes();
        Long wifiIntervalMinutes = param.getWifiIntervalMinutes();

        // 获取pageEmployeePunchRecordInfoList中所有的主键id
        List<Long> punchRecordIdList = pageEmployeePunchRecordInfoList.stream().map(EmployeePunchRecordDO::getId).collect(Collectors.toList());
        // 根据punchRecordIdList 查询打卡记录巡检表详情
        EmployeePunchRecordInspectionQuery query = EmployeePunchRecordInspectionQuery.builder()
                .punchRecordIdList(punchRecordIdList)
                .build();
        List<EmployeePunchRecordInspectionDO> employeePunchRecordInspectionList = employeePunchRecordInspectionDao.queryByCondition(query);

        // 遍历pageEmployeePunchRecordInfoList，将里面的创建时间转成当地时间，
        // 如果是chn，与打卡时间如果相差大于1分钟，则记录打卡异常
        for (EmployeePunchRecordDO employeePunchRecordDO : pageEmployeePunchRecordInfoList) {
            Long punchRecordId = employeePunchRecordDO.getId();
            if (ObjectUtil.isNull(punchRecordId)) {
                log.info("handlerPunchRecord employeePunchRecordDO is null");
                continue;
            }

            // 获取employeePunchRecordInspectionList中punchRecordId与punchRecordId相同的数据
            List<EmployeePunchRecordInspectionDO> employeePunchRecordInspectionInfoList = employeePunchRecordInspectionList.stream().filter(item -> ObjectUtil.isNotNull(item) && ObjectUtil.isNotNull(item.getPunchRecordId()) && item.getPunchRecordId().equals(punchRecordId)).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(employeePunchRecordInspectionInfoList)) {
                log.info("employeePunchRecordInspectionHandler punchRecordId: {} employeePunchRecordInspectionInfoList is not empty employeePunchRecordInspectionInfoList size :{}, The data already exists", punchRecordId, employeePunchRecordInspectionInfoList.size());
                continue;
            }

            // 获取打卡时间
            Date punchTime = employeePunchRecordDO.getPunchTime();
            Date createDate = employeePunchRecordDO.getCreateDate();
            String punchCardType = employeePunchRecordDO.getPunchCardType();

            // 计算punchTime与createDate的时间差
            if (ObjectUtil.isNull(punchTime) || ObjectUtil.isNull(createDate)) {
                log.info("employeePunchRecordInspectionHandler punchTime or createDate is null");
                continue;
            }

            // 获取当地的创建时间
            Date localCreateDate = CommonUtil.convertDateByTimeZonePlus(timeZone, createDate);

            long between = DateUtil.between(punchTime, localCreateDate, DateUnit.MINUTE, true);

            if (ObjectUtil.isEmpty(punchCardType)) {
                log.info("employeePunchRecordInspectionHandler punchCardType is null");
                if (between >= gpsIntervalMinutes) {
                    handlerEntry(country, targetEmployeePunchRecordInspectionList, employeePunchRecordDO, localCreateDate, punchCardType);
                    // 打卡时间与创建时间相差大于1分钟，记录打卡异常
                    log.info("employeePunchRecordInspectionHandler 打卡异常：country:{},userCode:{},punchTime:{},createDate:{}", country, employeePunchRecordDO.getUserCode(), punchTime, createDate);
                }
            } else {
                switch (punchCardType) {
                    case "GPS":
                        if (between >= gpsIntervalMinutes) {
                            handlerEntry(country, targetEmployeePunchRecordInspectionList, employeePunchRecordDO, localCreateDate, punchCardType);
                        }
                        break;
                    case "WIFI":
                        if (between >= wifiIntervalMinutes) {
                            handlerEntry(country, targetEmployeePunchRecordInspectionList, employeePunchRecordDO, localCreateDate, punchCardType);
                        }
                        break;
                    default:
                        log.info("default");
                        break;
                }
            }
        }

    }

    /**
     * 封装对象
     *
     * @param country                                 国家
     * @param targetEmployeePunchRecordInspectionList 目标打卡记录集合
     * @param employeePunchRecordDO                   打卡记录
     * @param localCreateDate                         当地创建时间
     * @param punchCardType                           打卡方式
     */
    private void handlerEntry(String country,
                              List<EmployeePunchRecordInspectionDO> targetEmployeePunchRecordInspectionList,
                              EmployeePunchRecordDO employeePunchRecordDO,
                              Date localCreateDate,
                              String punchCardType) {
        EmployeePunchRecordInspectionDO employeePunchRecordInspection = new EmployeePunchRecordInspectionDO();
        employeePunchRecordInspection.setId(defaultIdWorker.nextId());
        employeePunchRecordInspection.setPunchRecordId(employeePunchRecordDO.getId());
        employeePunchRecordInspection.setUserCode(employeePunchRecordDO.getUserCode());
        employeePunchRecordInspection.setCountry(country);
        employeePunchRecordInspection.setDayId(employeePunchRecordDO.getDayId());
        employeePunchRecordInspection.setSourceType(employeePunchRecordDO.getSourceType());
        employeePunchRecordInspection.setPunchTime(employeePunchRecordDO.getPunchTime());
        // 设置创建时间转成本地的时间
        employeePunchRecordInspection.setPunchRecordCreateDateLocal(localCreateDate);
        // 设置打卡记录的生成时的创建时间
        employeePunchRecordInspection.setPunchRecordCreateDate(employeePunchRecordDO.getCreateDate());
        employeePunchRecordInspection.setPunchArea(employeePunchRecordDO.getPunchArea());
        employeePunchRecordInspection.setPunchCardType(punchCardType);
        BaseDOUtil.fillDOInsert(employeePunchRecordInspection);
        targetEmployeePunchRecordInspectionList.add(employeePunchRecordInspection);
    }
}
