package com.imile.attendance.punch.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PunchCardRecordResultDTO {

    /**
     * 日期
     */
    private String dayId;
    /**
     * 国家
     */
    private String country;
    /**
     * 网点编码
     */
    private String stationCode;
    /**
     * 网点名称
     */
    private String stationName;
    /**
     * 网点坐标
     */
    private String stationPoint;
    /**
     * 司机编码
     */
    private String driverCode;
    /**
     * 司机名称
     */
    private String driverName;
    /**
     * 上班打卡时间
     */
    private Date onDutyTime;
    /**
     * 上班打卡坐标
     */
    private String onDutyPoint;
    /**
     * 上班打卡距离网点差距
     */
    private BigDecimal onDutyDistance;
    /**
     * 下班打卡时间
     */
    private Date offDutyTime;
    /**
     * 下班打卡坐标
     */
    private String offDutyPoint;
    /**
     * 下班打卡距离网点差距
     */
    private BigDecimal offDutyDistance;
    /**
     * 员工性质  正式/外包
     */
    private String employeeType;

}
