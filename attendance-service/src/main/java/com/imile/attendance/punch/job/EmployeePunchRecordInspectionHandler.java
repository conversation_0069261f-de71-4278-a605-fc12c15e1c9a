package com.imile.attendance.punch.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.punch.job.service.EmployeePunchRecordInspectionService;
import com.imile.attendance.punch.param.EmployeePunchRecordInspectionParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * {@code @author:} allen
 * {@code @className:} EmployeePunchRecordInspectionHandler
 * {@code @since:} 2025-01-08 13:54
 * {@code @description:} 人员打卡记录巡检器：巡检有可能出现作弊行为的打卡记录数据
 */
@Slf4j
@Component
public class EmployeePunchRecordInspectionHandler {

    @Resource
    private EmployeePunchRecordInspectionService employeePunchRecordInspectionService;

    @XxlJob(BusinessConstant.JobHandler.EMPLOYEE_PUNCH_RECORD_INSPECTION_HANDLER)
    public ReturnT<String> employeePunchRecordInspectionHandler(String content) {
        EmployeePunchRecordInspectionParam param = StringUtils.isNotBlank(content)
                ? JSON.parseObject(content, EmployeePunchRecordInspectionParam.class)
                : new EmployeePunchRecordInspectionParam();
        if (ObjectUtil.isNull(param)) {
            log.info("employeePunchRecordInspectionHandler param is null");
            return ReturnT.SUCCESS;
        }
        if (CollUtil.isEmpty(param.getCountryList())) {
            log.info("employeePunchRecordInspectionHandler countryList is empty");
            return ReturnT.SUCCESS;
        }
        if (ObjectUtil.isNull(param.getPageSize())) {
            param.setPageSize(500);
        }
        // 打卡记录异常巡检服务
        employeePunchRecordInspectionService.employeePunchRecordInspectionHandler(param);
        return ReturnT.SUCCESS;
    }
}
