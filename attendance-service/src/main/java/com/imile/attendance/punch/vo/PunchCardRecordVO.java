package com.imile.attendance.punch.vo;

import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PunchCardRecordVO implements Serializable {
    private static final long serialVersionUID = -7822090730580098546L;

    private Long id;
    /**
     * 日期
     */
    private String dayId;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 组织编码
     */
    private String organizationCode;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 数据来源
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.PUNCH_SOURCE_TYPE, ref = "sourceTypeDesc")
    private String sourceType;
    /**
     * 数据来源(枚举描述)
     */
    private String sourceTypeDesc;
    /**
     * 打卡区域
     */
    private String punchArea;
    /**
     * 打卡方式
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.PUNCH_CARD_TYPE, ref = "punchCardTypeDesc")
    private String punchCardType;
    /**
     * 打卡方式(枚举描述)
     */
    private String punchCardTypeDesc;
    /**
     * 打卡时间
     */
    private Date punchTime;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

    /**
     * 日历名称
     */
    private String attendanceConfigName;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

}
