package com.imile.attendance.third;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.ZktVersionEnum;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.third.dao.ZktecoAreaSnRelationDao;
import com.imile.attendance.infrastructure.repository.third.model.ZktecoAreaSnRelationDO;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoAreasDO;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeDO;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeUpdateDO;
import com.imile.attendance.third.helper.ThirdZktecoEnvHelper;
import com.imile.attendance.third.dto.EmployeeResultDTO;
import com.imile.attendance.third.dto.ZktecoAreasDTO;
import com.imile.attendance.third.zkteco.impl.ZktecoManageImpl;
import com.imile.attendance.third.zkteco.impl.ZktecoVersion8ManageImpl;
import com.imile.attendance.third.utils.ZKTecoUtils;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/5
 * @Description
 */
@Slf4j
@Service
public class ThirdZktecoService {

    @Resource
    private ZktecoManageImpl zktecoManage;
    @Resource
    private ZktecoVersion8ManageImpl zktecoVersion8Manage;
    @Resource
    private AttendanceProperties attendanceProperties;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private ZKTecoUtils zkTecoUtils;
    @Resource
    private ThirdZktecoQueryService thirdZktecoQueryService;
    @Resource
    private ZktecoAreaSnRelationDao zktecoAreaSnRelationDao;
    @Resource
    private ThirdZktecoEnvHelper thirdZktecoEnvHelper;


    @Value("${imile.attendance.sync.driver.country:KSA}")
    private String syncDriverCountry;
    @Value("${imile.attendance.sync.driver.vendor.code:300001,2101847,2104408,2104616,2106054}")
    private String syncDriverVendorCode;
    @Value("${imile.attendance.sync.driver.post.id}")
    private String syncDriverPostId;


    /**
     * 查询区域及sn信息
     */
    public Map<String, List<ZktecoAreasDTO>> selectSnByArea() {
        Map<String, List<ZktecoAreasDTO>> map = new HashMap<>();

        List<ZktecoAreasDO> zktecoAreasList = zktecoManage.selectSnByArea();
        if (CollectionUtils.isNotEmpty(zktecoAreasList)) {
            List<ZktecoAreasDTO> zktecoAreasDTOS = convertZktecoAreasDTO(zktecoAreasList);
            map.put("zkt8.5", zktecoAreasDTOS);
        }

        if (StringUtils.isNotBlank(attendanceProperties.getZkteco().getCountryVersion8())) {
            //走8.0版本
            List<ZktecoAreasDO> zktecoAreasList8 = zktecoVersion8Manage.selectSnByArea();
            if (CollectionUtils.isNotEmpty(zktecoAreasList8)) {
                List<ZktecoAreasDTO> zktecoAreasDTOS = convertZktecoAreasDTO(zktecoAreasList8);
                map.put("zkt8", zktecoAreasDTOS);
            }
        }
        return map;
    }

    /**
     * 判断Zkteco是否使用8.0版本
     */
    public boolean isCountryVersion8(String country) {
        if (StringUtils.isBlank(country)) {
            return false;
        }
        String countryVersion8 = attendanceProperties.getZkteco().getCountryVersion8();
        return StringUtils.isNotBlank(countryVersion8)
                && Arrays.stream(countryVersion8.split(",")).
                anyMatch(o -> StringUtils.equalsIgnoreCase(o, country));
    }

    /**
     * 同步员工数据到中控考勤
     */
    public void syncEmployee2Zkteco(Long userId) {
        log.info("syncEmployee2Zkteco开始同步员工到中控考勤机 | 员工ID: {}, 环境: {}", userId, thirdZktecoEnvHelper.getEnv());
        if (thirdZktecoEnvHelper.isNoNeedSyncEnv()) {
            log.info("syncEmployee2Zkteco | env is not prod or uat, return");
            return;
        }
        log.info("syncEmployee2Zkteco | userId :{}，start sync zkt", userId);

        AttendanceUser attendanceUser = userService.getByUserId(userId);
        if (attendanceUser == null || StringUtils.isBlank(attendanceUser.getUserCode()) ||
                ObjectUtil.isEmpty(attendanceUser.getLocationCountry())) {
            log.error("userId:{} not hava a valid info,user={}", userId, attendanceUser);
            return;
        }
        try {
            ZktVersionEnum zktVersionEnum = determineZktVersion(attendanceUser.getLocationCountry());
            List<ZktecoEmployeeDO> zktecoEmployeeDOS = fetchEmployeesByVersion(
                    Collections.singletonList(attendanceUser.getUserCode()), zktVersionEnum);
            String serverUrl = determineServerUrl(zktVersionEnum);
            String token = getZktecoToken(zktVersionEnum);
            if (CollectionUtils.isEmpty(zktecoEmployeeDOS)) {
                //需要同步员工信息到中控考勤机
                addEmployee(token, attendanceUser, serverUrl, zktVersionEnum == ZktVersionEnum.ZKT_VERSION_80);
                return;
            }
            updateEmployee(token, zktecoEmployeeDOS.get(0), attendanceUser, serverUrl, zktVersionEnum == ZktVersionEnum.ZKT_VERSION_80);
        } catch (Exception e) {
            log.error("syncEmployee2Zkteco error", e);
        }

    }

    /**
     * 同步员工信息到中控考勤机
     */
    public void syncEmployees(String country,
                              ZktecoAreaSnRelationDO relationDO,
                              List<Long> deptIdList,
                              List<Long> userIdList) {
        log.info("当前环境: {}", thirdZktecoEnvHelper.getEnv());
        if (thirdZktecoEnvHelper.isNoNeedSyncEnv()) {
            log.info("not uat or prod, not executed");
            return;
        }
        // 确定使用的中控版本和获取对应token
        ZktVersionEnum zktVersionEnum = determineZktVersion(country);
        String token = getZktecoToken(zktVersionEnum);
        // 如果是uat环境
        if (thirdZktecoEnvHelper.isUat()) {
            log.info("UAT环境: 执行全量同步");
            List<AttendanceUser> userList = userService.selectUserForZkteco(deptIdList, userIdList);
            log.info("UAT本次同步中控考勤机的员工数量: {}", userList.size());
            syncEmployeeHandler(token, relationDO, userList, zktVersionEnum);
            return;
        }
        // PROD环境: 根据情况决定是全量同步还是增量同步
        if (thirdZktecoEnvHelper.isProd()) {
            log.info("PROD环境: 开始同步");
            // 如果是zkt第一次同步过来的数据，deptIds是null，country是null
            if (isFirstSync(relationDO)) {
                log.info("首次同步, 执行全量同步");
                // 如果数据库该条数据country = null && deptIds = null，那么就是第一次同步过来的数据，那么直接全部前端传过来的数据到中控考勤机
                List<AttendanceUser> userList = userService.selectUserForZkteco(deptIdList, userIdList);
                syncEmployeeHandler(token, relationDO, userList, zktVersionEnum);
                return;
            }
            // 非首次同步: 增量
            log.info("非首次同步, 执行增量同步");
            List<AttendanceUser> newUsers = getNewUsersToSync(relationDO, deptIdList, userIdList);
            log.info("本次增量同步中控考勤机的员工数量: {}", newUsers.size());
            syncEmployeeHandler(token, relationDO, newUsers, zktVersionEnum);
            return;
        }
        log.info("当前环境既不是UAT也不是PROD, 不执行同步操作");
    }


    private List<ZktecoAreasDTO> convertZktecoAreasDTO(List<ZktecoAreasDO> zktecoAreasList) {
        if (CollectionUtils.isEmpty(zktecoAreasList)) {
            return new ArrayList<>();
        }
        Map<String, List<ZktecoAreasDO>> areaGroup = zktecoAreasList.stream()
                .collect(Collectors.groupingBy(ZktecoAreasDO::getAreaName));

        List<ZktecoAreasDTO> areasDTOList = new ArrayList<>(areaGroup.size());
        for (Map.Entry<String, List<ZktecoAreasDO>> entry : areaGroup.entrySet()) {
            ZktecoAreasDTO areasDTO = new ZktecoAreasDTO();
            ZktecoAreasDO zktecoAreasDO = entry.getValue().get(0);
            areasDTO.setId(zktecoAreasDO.getId());
            areasDTO.setAreaCode(zktecoAreasDO.getAreaCode());
            areasDTO.setAreaName(zktecoAreasDO.getAreaName());
            areasDTO.setSnList(entry.getValue()
                    .stream()
                    .map(ZktecoAreasDO::getSn)
                    .collect(Collectors.toList())
            );
            areasDTOList.add(areasDTO);
        }
        return areasDTOList;
    }

    private void addEmployee(String token,
                             AttendanceUser attendanceUser,
                             String serverUrl,
                             boolean countryVersion8) {
        // 校验员工是否需要同步到考勤机
        if (!checkNeedSyncDriver(attendanceUser)) {
            log.warn("员工无需同步到考勤机，用户：{}", attendanceUser.getUserCode());
            return;
        }
        if (attendanceUser.getDeptId() == null) {
            log.error("员工部门ID为空，无法同步。用户：{}", attendanceUser.getUserCode());
            return;
        }
        // 查询部门对应的区域关系
        List<ZktecoAreaSnRelationDO> zktecoAreaSnRelationDOS = thirdZktecoQueryService.selectByDeptId(attendanceUser.getDeptId());
        if (CollectionUtils.isEmpty(zktecoAreaSnRelationDOS)) {
            log.error("未找到部门对应的区域关系，部门ID：{}，用户：{}", attendanceUser.getDeptId(), attendanceUser.getUserCode());
            return;
        }
        //根据用户身份类型，选择合适的默认部门ID
        Integer deptId = attendanceProperties.getZkteco().getOfficeDeptId();
        if (attendanceUser.getIsWarehouseStaff() == 1) {
            deptId = attendanceProperties.getZkteco().getWarehouseDeptId();
        }
        // 根据zkt版本选择不同的部门
        if (countryVersion8) {
            // 如果是8.0版本的国家
            deptId = attendanceProperties.getZkteco().getOfficeDeptId8();
            if (attendanceUser.getIsWarehouseStaff() == 1) {
                deptId = attendanceProperties.getZkteco().getWarehouseDeptId8();
            }
        }

        // 提取区域ID列表
        List<Integer> areaIds = zktecoAreaSnRelationDOS.stream()
                .map(ZktecoAreaSnRelationDO::getZktecoAreaId)
                .collect(Collectors.toList());

        // 调用考勤机API创建员工
        String result = zkTecoUtils.createEmployee(token, attendanceUser.getUserCode(),
                attendanceUser.getUserName(), null, deptId, areaIds, serverUrl);
        if (StringUtils.isBlank(result)) {
            log.error("考勤机API调用createEmployee失败，返回结果为空。用户：{}", attendanceUser.getUserCode());
            return;
        }
        EmployeeResultDTO employeeResultDTO = JSONObject.parseObject(result, EmployeeResultDTO.class);
        if (employeeResultDTO.getId() == null ||
                !StringUtils.equalsIgnoreCase(attendanceUser.getUserCode(), employeeResultDTO.getEmp_code())) {
            log.error("createEmployee考勤机员工创建失败，响应数据异常。用户：{}，响应：{}", attendanceUser.getUserCode(), result);
        }
    }


    /**
     * 检查是否同步司机信息
     */
    private boolean checkNeedSyncDriver(AttendanceUser attendanceUser) {
        if (attendanceUser.getIsDriver().equals(BusinessConstant.ZERO)) {
            return true;
        }
        List<String> countryList = Arrays.asList(syncDriverCountry.split(","));

        List<String> vendorCodeList = Arrays.asList(syncDriverVendorCode.split(","));

        List<Long> postIdList = Arrays.stream(syncDriverPostId.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());

        if (!postIdList.contains(attendanceUser.getPostId())) {
            return false;
        }

        if (!countryList.contains(attendanceUser.getLocationCountry())) {
            return false;
        }
        if (StringUtils.equalsIgnoreCase(EmploymentTypeEnum.EMPLOYEE.getCode(), attendanceUser.getEmployeeType())
                || StringUtils.equalsIgnoreCase(EmploymentTypeEnum.SUB_EMPLOYEE.getCode(), attendanceUser.getEmployeeType())) {
            return true;
        }
        if (StringUtils.equalsIgnoreCase(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), attendanceUser.getEmployeeType()) &&
                vendorCodeList.contains(attendanceUser.getUserCode())) {
            return true;
        }
        return false;
    }


    private void updateEmployee(String token, ZktecoEmployeeDO employeeDO, AttendanceUser attendanceUser,
                                String serverUrl, boolean countryVersion8) {
        if (!checkNeedSyncDriver(attendanceUser)) {
            return;
        }
        if (attendanceUser.getDeptId() == null) {
            log.error("userCode:{} not have a deptId", attendanceUser.getUserCode());
            return;
        }
        ZktecoEmployeeUpdateDO updateDO = new ZktecoEmployeeUpdateDO();
        updateDO.setId(employeeDO.getId());
        updateDO.setUserCode(attendanceUser.getUserCode());
        updateDO.setFirstName(attendanceUser.getUserName());
        Integer deptId = attendanceProperties.getZkteco().getOfficeDeptId();
        if (attendanceUser.getIsWarehouseStaff() == 1) {
            deptId = attendanceProperties.getZkteco().getWarehouseDeptId();
        }
        // 根据zkt版本选择不同的部门
        if (countryVersion8) {
            // 如果是8.0版本的国家
            deptId = attendanceProperties.getZkteco().getOfficeDeptId8();
            if (attendanceUser.getIsWarehouseStaff() == 1) {
                deptId = attendanceProperties.getZkteco().getWarehouseDeptId8();
            }
        }
        updateDO.setDeptId(deptId);


        List<ZktecoAreaSnRelationDO> userSnList = Lists.newArrayList();
        // 通过部门查询绑定设备区域
        List<ZktecoAreaSnRelationDO> userSnList_dept = thirdZktecoQueryService.selectByDeptId(attendanceUser.getDeptId());
        if (CollectionUtils.isNotEmpty(userSnList_dept)) {
            userSnList.addAll(userSnList_dept);
        }
        // 通过用户查询绑定设备区域
        List<ZktecoAreaSnRelationDO> userSnList_user = thirdZktecoQueryService.selectByUserId(attendanceUser.getId());
        if (CollectionUtils.isNotEmpty(userSnList_user)) {
            userSnList.addAll(userSnList_user);
        }
        if (CollectionUtils.isEmpty(userSnList)) {
            log.info("updateEmployee | userInfo :{}, userInfo :{}, userSnList :{}",
                    attendanceUser.getUserCode(), JSON.toJSONString(attendanceUser), JSON.toJSONString(userSnList));
            return;
        }
        List<Integer> areaIds = userSnList.stream()
                .map(ZktecoAreaSnRelationDO::getZktecoAreaId)
                .collect(Collectors.toList());

        updateDO.setAreaIds(areaIds);

        updateDO.setStatus(0);
        if (StringUtils.equalsIgnoreCase(StatusEnum.DISABLED.getCode(), attendanceUser.getStatus())
                || StringUtils.equalsIgnoreCase(WorkStatusEnum.DIMISSION.getCode(), attendanceUser.getWorkStatus())) {
            updateDO.setStatus(99);
        }
        String result = zkTecoUtils.updateEmployee(token, updateDO, serverUrl);
        EmployeeResultDTO employeeResultDTO = JSONObject.parseObject(result, EmployeeResultDTO.class);
        if (employeeResultDTO.getId() == null ||
                !StringUtils.equalsIgnoreCase(attendanceUser.getUserCode(), employeeResultDTO.getEmp_code())) {
            log.error("updateEmployee考勤机员工更新失败，响应数据异常。用户：{}，响应：{}", attendanceUser.getUserCode(), result);
        }
    }


    private void syncEmployeeHandler(String token,
                                     ZktecoAreaSnRelationDO relationDO,
                                     List<AttendanceUser> userList,
                                     ZktVersionEnum zktVersionEnum) {
        if (CollectionUtils.isEmpty(userList)) {
            log.info("没有需要同步的员工");
            return;
        }
        List<String> countryList = Arrays.asList(syncDriverCountry.split(","));
        Map<Long, List<ZktecoAreaSnRelationDO>> map = new HashMap<>();

        // 将员工分类
        List<AttendanceUser> driverList = filterDrivers(userList, countryList);
        List<AttendanceUser> warehouseList = filterWarehouseStaff(userList);
        List<AttendanceUser> officeList = filterOfficeStaff(userList);

        // 根据不同版本使用对应的部门ID
        boolean isVersion80 = ZktVersionEnum.ZKT_VERSION_80.equals(zktVersionEnum);

        // 同步各类员工,如果ZKTeco版本是8.0，则需要设置一下ZKT的部门id，因为8.0版本的部门id跟8.5的不一样，8.5的部门id是阿波罗配置的，8.0的部门id是新的
        syncEmployee(token,
                isVersion80 ? attendanceProperties.getZkteco().getDriverDeptId8() : attendanceProperties.getZkteco().getDriverDeptId(),
                driverList, map, zktVersionEnum);

        syncEmployee(token,
                isVersion80 ? attendanceProperties.getZkteco().getWarehouseDeptId8() : attendanceProperties.getZkteco().getWarehouseDeptId(),
                warehouseList, map, zktVersionEnum);

        syncEmployee(token,
                isVersion80 ? attendanceProperties.getZkteco().getOfficeDeptId8() : attendanceProperties.getZkteco().getOfficeDeptId(),
                officeList, map, zktVersionEnum);
    }


    private void syncEmployee(String token,
                              Integer deptId,
                              List<AttendanceUser> userList,
                              Map<Long, List<ZktecoAreaSnRelationDO>> map,
                              ZktVersionEnum zktVersionEnum) {
        if (CollectionUtils.isEmpty(userList)) {
            log.info("没有需要同步的员工");
            return;
        }
        List<String> userCodeList = userList.stream()
                .map(AttendanceUser::getUserCode)
                .distinct()
                .collect(Collectors.toList());

        // 根据版本获取服务器URL和员工信息
        String serverUrl = determineServerUrl(zktVersionEnum);
        List<ZktecoEmployeeDO> zktecoEmployeeDOS = fetchEmployeesByVersion(userCodeList, zktVersionEnum);

        // 将员工信息转换为Map以便查询
        Map<String, ZktecoEmployeeDO> employeeMap = zktecoEmployeeDOS.stream()
                .collect(Collectors.toMap(ZktecoEmployeeDO::getEmpCode, o -> o, (v1, v2) -> v1));

        log.info("准备同步员工数据: 员工列表大小={}, 中控系统已存在员工={}, 部门ID={}",
                userList.size(), employeeMap.size(), deptId);

        for (AttendanceUser attendanceUser : userList) {
            try {
                syncSingleEmployee(attendanceUser, employeeMap, deptId, map, token, serverUrl);
            } catch (Exception e) {
                log.error("同步员工{}(编码:{})失败: {}",
                        attendanceUser.getUserName(), attendanceUser.getUserCode(), e.getMessage(), e);
            }
        }
    }

    private List<Integer> getAreaIds(Map<Long, List<ZktecoAreaSnRelationDO>> map,
                                     AttendanceUser attendanceUser) {
        List<ZktecoAreaSnRelationDO> areaByUser = getAreaByUser(attendanceUser);
        Set<Integer> areaIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(areaByUser)) {
            List<Integer> collect = areaByUser.stream()
                    .map(ZktecoAreaSnRelationDO::getZktecoAreaId)
                    .distinct()
                    .collect(Collectors.toList());
            areaIds.addAll(collect);
        }
        List<ZktecoAreaSnRelationDO> areaByDept = map.get(attendanceUser.getDeptId());
        if (CollectionUtils.isNotEmpty(areaByDept)) {
            List<Integer> collect = areaByDept.stream()
                    .map(ZktecoAreaSnRelationDO::getZktecoAreaId)
                    .distinct()
                    .collect(Collectors.toList());
            areaIds.addAll(collect);
            return new ArrayList<>(areaIds);
        }
        List<ZktecoAreaSnRelationDO> zktecoAreaSnRelationDOS = thirdZktecoQueryService.selectByDeptId(attendanceUser.getDeptId());
        if (CollectionUtils.isNotEmpty(zktecoAreaSnRelationDOS)) {
            List<Integer> collect = zktecoAreaSnRelationDOS.stream()
                    .map(ZktecoAreaSnRelationDO::getZktecoAreaId)
                    .distinct()
                    .collect(Collectors.toList());
            areaIds.addAll(collect);
            return new ArrayList<>(areaIds);
        }
        return new ArrayList<>(areaIds);
    }

    private List<ZktecoAreaSnRelationDO> getAreaByUser(AttendanceUser attendanceUser) {
        List<ZktecoAreaSnRelationDO> zktecoAreaSnRelationDOS = new ArrayList<>();
        List<ZktecoAreaSnRelationDO> snRelationDOList = zktecoAreaSnRelationDao.listByUserId(attendanceUser.getId());
        for (ZktecoAreaSnRelationDO relationDO : snRelationDOList) {
            List<String> userIdStr = Arrays.asList(relationDO.getUserIds().split(","));
            if (userIdStr.contains(attendanceUser.getId().toString())) {
                zktecoAreaSnRelationDOS.add(relationDO);
            }
        }
        return zktecoAreaSnRelationDOS;
    }

    /**
     * 确定中控考勤机版本
     */
    private ZktVersionEnum determineZktVersion(String country) {
        return isCountryVersion8(country) ? ZktVersionEnum.ZKT_VERSION_80 : ZktVersionEnum.ZKT_VERSION_85;
    }

    /**
     * 获取中控考勤机访问token
     */
    private String getZktecoToken(ZktVersionEnum versionEnum) {
        if (ZktVersionEnum.ZKT_VERSION_80.equals(versionEnum)) {
            return zkTecoUtils.getToken(
                    attendanceProperties.getZkteco().getUserNameVersion8(),
                    attendanceProperties.getZkteco().getPasswordVersion8(),
                    attendanceProperties.getZkteco().getSERVER_URL_VERSION_8()
            );
        } else {
            return zkTecoUtils.getToken(
                    attendanceProperties.getZkteco().getUserName(),
                    attendanceProperties.getZkteco().getPassword(),
                    attendanceProperties.getZkteco().getSERVER_URL()
            );
        }
    }

    /**
     * 判断是否首次同步
     */
    private boolean isFirstSync(ZktecoAreaSnRelationDO relationDO) {
        return ObjectUtil.isNull(relationDO.getCountry()) && ObjectUtil.isNull(relationDO.getDeptIds());
    }

    /**
     * 获取需要新增同步的用户
     */
    private List<AttendanceUser> getNewUsersToSync(ZktecoAreaSnRelationDO relationDO,
                                                   List<Long> deptIdList,
                                                   List<Long> userIdList) {
        // 获取原有用户
        List<Long> existingDeptIds = relationDO.listDeptIdList();
        List<Long> existingUserIds = relationDO.listUserIdList();
        List<AttendanceUser> existingUsers = userService.selectUserForZkteco(existingDeptIds, existingUserIds);

        // 获取现有用户编码集合
        Set<String> existingUserCodes = existingUsers.stream()
                .map(AttendanceUser::getUserCode)
                .collect(Collectors.toSet());

        // 获取新用户列表
        List<AttendanceUser> newUsers = userService.selectUserForZkteco(deptIdList, userIdList);

        // 过滤出需要新增的用户
        return newUsers.stream()
                .filter(user -> !existingUserCodes.contains(user.getUserCode()))
                .collect(Collectors.toList());
    }


    /**
     * 过滤司机员工
     */
    private List<AttendanceUser> filterDrivers(List<AttendanceUser> userList, List<String> countryList) {
        return userList.stream()
                .filter(user -> user.getIsDriver() == 1
                        && user.getIsVirtual() == 0
                        && countryList.contains(user.getLocationCountry())
                        && StringUtils.equalsIgnoreCase(EmploymentTypeEnum.EMPLOYEE.getCode(), user.getEmployeeType()))
                .collect(Collectors.toList());
    }

    /**
     * 过滤仓库员工
     */
    private List<AttendanceUser> filterWarehouseStaff(List<AttendanceUser> userList) {
        return userList.stream()
                .filter(user -> user.getIsWarehouseStaff() != null
                        && user.getIsVirtual() != null
                        && user.getIsWarehouseStaff() == 1
                        && user.getIsVirtual() == 0)
                .collect(Collectors.toList());
    }

    /**
     * 过滤办公室员工
     */
    private List<AttendanceUser> filterOfficeStaff(List<AttendanceUser> userList) {
        return userList.stream()
                .filter(user -> user.getIsWarehouseStaff() != null
                        && user.getIsVirtual() != null
                        && user.getIsDriver() == 0
                        && user.getIsWarehouseStaff() == 0
                        && user.getIsVirtual() == 0)
                .collect(Collectors.toList());
    }

    /**
     * 根据版本确定服务器URL
     */
    private String determineServerUrl(ZktVersionEnum zktVersionEnum) {
        return ZktVersionEnum.ZKT_VERSION_80.equals(zktVersionEnum)
                ? attendanceProperties.getZkteco().getSERVER_URL_VERSION_8()
                : attendanceProperties.getZkteco().getSERVER_URL();
    }

    /**
     * 根据版本获取员工信息
     */
    private List<ZktecoEmployeeDO> fetchEmployeesByVersion(List<String> userCodeList, ZktVersionEnum zktVersionEnum) {
        return ZktVersionEnum.ZKT_VERSION_80.equals(zktVersionEnum)
                ? zktecoVersion8Manage.selectEmployee(userCodeList)
                : zktecoManage.selectEmployee(userCodeList);
    }

    /**
     * 同步单个员工信息
     */
    private void syncSingleEmployee(AttendanceUser attendanceUser,
                                    Map<String, ZktecoEmployeeDO> employeeMap,
                                    Integer deptId,
                                    Map<Long, List<ZktecoAreaSnRelationDO>> map,
                                    String token,
                                    String serverUrl) {
        // 获取员工应该分配的区域ID
        List<Integer> areaIds = getAreaIds(map, attendanceUser);
        if (CollectionUtils.isEmpty(areaIds)) {
            log.info("员工{}(编码:{})没有对应的区域ID，跳过同步",
                    attendanceUser.getUserName(), attendanceUser.getUserCode());
            return;
        }

        ZktecoEmployeeDO existingEmployee = employeeMap.get(attendanceUser.getUserCode());
        String userName = attendanceUser.getUserName();
        String userCode = attendanceUser.getUserCode();

        // 更新或创建员工
        if (existingEmployee != null) {
            // 更新已存在的员工
            updateExistingEmployee(existingEmployee, userName, userCode, deptId, areaIds, token, serverUrl);
        } else {
            // 创建新员工
            createNewEmployee(userCode, userName, deptId, areaIds, token, serverUrl);
        }
    }


    /**
     * 更新已存在的员工信息
     */
    private void updateExistingEmployee(ZktecoEmployeeDO existingEmployee,
                                        String userName,
                                        String userCode,
                                        Integer deptId,
                                        List<Integer> areaIds,
                                        String token,
                                        String serverUrl) {
        log.info("更新已有员工: {}(编码:{}), 部门ID={}, 区域ID列表={}",
                userName, userCode, deptId, areaIds);

        ZktecoEmployeeUpdateDO updateDO = new ZktecoEmployeeUpdateDO();
        updateDO.setId(existingEmployee.getId());
        updateDO.setFirstName(userName);
        updateDO.setUserCode(userCode);
        updateDO.setDeptId(deptId);
        updateDO.setAreaIds(areaIds);
        updateDO.setStatus(0); // 0表示启用状态

        zkTecoUtils.updateEmployee(token, updateDO, serverUrl);
    }

    /**
     * 创建新员工
     */
    private void createNewEmployee(String userCode,
                                   String userName,
                                   Integer deptId,
                                   List<Integer> areaIds,
                                   String token,
                                   String serverUrl) {
        log.info("创建新员工: {}(编码:{}), 部门ID={}, 区域ID列表={}",
                userName, userCode, deptId, areaIds);

        zkTecoUtils.createEmployee(token, userCode, userName, null, deptId, areaIds, serverUrl);
    }
}
