package com.imile.attendance.user.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AttachmentDTO {
    private Long id;
    /**
     * 附件名称
     */
    private String attachmentName;
    /**
     * 附件类型
     */
    private String attachmentType;
    /**
     * 附件大小
     */
    private Integer size;
    /**
     * 文件路径
     */
    private String urlPath;

    public static List<AttachmentDTO> convert2List(String files) {
        if (StringUtils.isBlank(files)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(files, AttachmentDTO.class);
    }
}
