package com.imile.attendance.user.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserLeaveDetailVO implements Serializable {
    private static final long serialVersionUID = -3357899119753955843L;

    private Long id;

    /**
     * 假期规则主键
     */
    private Long configId;

    /**
     * 国家
     */
    private String country;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 假期类型
     */
    private Integer isDispatch;

    /**
     * 是否永久有效
     */
    private Integer isInvalid;

    /**
     * 更新周期
     */
    private String useCycle;

//    /**
//     * 假期可用的开始时间
//     */
//    private String useStartDate;
//
//    /**
//     * 假期可用的结束时间
//     */
//    private String useEndDate;

    /**
     * 使用状态 (是否可用: 1可用, 0不可用)
     */
    private Integer availability;

    /**
     * 使用条件 (是否可用: 1入职后, 2转正后, 3入职一年后, 4入职两年后)
     */
    private Integer useCondition;

    /**
     * 假期阶段信息
     */
    private List<UserLeaveItemDetailVO> itemDetailVOList;
}
