package com.imile.attendance.user.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-26
 * @version: 1.0
 */
@Data
public class UserLeaveResidualVO {

    /**
     * 假期规则主键
     */
    private Long configId;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期名称描述(多语转换)
     */
    private String leaveNameByLang;

    /**
     * 假期简称
     */
    private String leaveShortName;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 假期类型描述(多语转换)
     */
    private String leaveTypeByLang;

    /**
     * 假期剩余时间(分钟)
     */
    private BigDecimal leaveResidueMinutes;

    /**
     * 节假日是否消耗假期
     */
    private String consumeLeaveType;

    /**
     * 是否上传附件
     */
    private Integer isUploadAttachment;

    /**
     * 上传附件条件
     */
    private Long uploadAttachmentCondition;

    /**
     * 上传附件单位
     */
    private String attachmentUnit;

    /**
     * 请假单位
     */
    private String leaveUnit;

    /**
     * 最小请假时长
     */
    private Integer miniLeaveDuration;
}
