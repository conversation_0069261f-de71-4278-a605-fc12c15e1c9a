package com.imile.attendance.user.dto;

import lombok.Data;

import java.util.List;

@Data
public class UserLeaveInfoDTO {

    private Long id;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 组织编码
     */
    @Deprecated
    private String organizationCode;
    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 岗位名称
     */
    private String postName;
    /**
     * 用户账号
     */
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 性别
     */
    private Integer sex;

    /**
     * 入职日期
     */
    private String entryDate;

    private List<UserLeaveDetailDTO> userLeaveDetailList;
}
