package com.imile.attendance.user.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserLeaveInfoVO implements Serializable {
    private static final long serialVersionUID = -3357899119753955843L;

    private Long id;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 组织编码
     */
    private String organizationCode;
    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 岗位名称
     */
    private String postName;
    /**
     * 用户账号
     */
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 入职日期
     */
    private String entryDate;
    /**
     * 员工假期信息
     */
    List<UserLeaveDetailVO> detailVOList;

}
