package com.imile.attendance.user.dto;

import com.imile.attendance.annon.HyperLink;
import lombok.Data;

import java.util.Date;

@Data
public class UserLeaveDTO {

    private Long id;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 工号
     */
    private String workNo;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 组织编码
     */
    private String organizationCode;
    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 图片url
     */
    @HyperLink(ref = "profilePhotoUrlHttps")
    private String profilePhotoUrl;

    /**
     * 员工头像地址Https
     */
    private String profilePhotoUrlHttps;

    /**
     * 账号状态
     */
    private String status;

    /**
     * 在职状态
     */
    private String workStatus;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

    private String country;

    /**
     * 年假剩余分钟
     */
//    private BigDecimal annualLeaveResidueMinutes;

}
