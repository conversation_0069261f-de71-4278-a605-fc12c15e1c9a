package com.imile.attendance.user.dto;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-6-13
 * @version: 1.0
 */
@Data
public class UserAvailableLeaveDTO {
    /**
     * hrms_user_leave_detail表ID
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 该中类型假期的具体类型，可以请的阶段，比如病假有阶段1,2，  1已经请完了，这里就保存2
     */
    List<UserStageLeaveDTO> userStageLeaveDTOList;
}
