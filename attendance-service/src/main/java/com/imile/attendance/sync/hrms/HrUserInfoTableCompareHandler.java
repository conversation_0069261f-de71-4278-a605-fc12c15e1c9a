package com.imile.attendance.sync.hrms;

import com.imile.attendance.exception.DataDifferenceException;
import com.imile.attendance.infrastructure.repository.employee.dao.UserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserInfoDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import com.imile.attendance.infrastructure.sync.TableCompareHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/3/27 
 * @Description hrms用户信息表数据比对
 */
@Component
@Slf4j
public class HrUserInfoTableCompareHandler {

    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private HrmsUserEntryRecordDao hrmsUserEntryRecordDao;
    @Resource
    private HrmsUserDimissionRecordDao hrmsUserDimissionRecordDao;

    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private UserEntryRecordDao userEntryRecordDao;
    @Resource
    private UserDimissionRecordDao userDimissionRecordDao;


    @XxlJob("hrUserInfoTableCompareHandler")
    public ReturnT<String> syncHrUserInfo(String param) {
        compareUserInfo();
        compareUseEntry();
        compareUseDismission();
        return ReturnT.SUCCESS;
    }


    public void compareUserInfo() {
        TableCompareHelper.compareTable(
                "hrms_user_info",
                hrmsUserInfoDao::listByPage,
                userInfoDao::listByPage,
                this::compareUserInfo
        );
    }

    public void compareUseEntry() {
        TableCompareHelper.compareTable(
                "hrms_user_entry_record",
                hrmsUserEntryRecordDao::listByPage,
                userEntryRecordDao::listByPage,
                this::compareUserEntryRecord
        );
    }

    public void compareUseDismission() {
        TableCompareHelper.compareTable(
                "hrms_user_dimission_record",
                hrmsUserDimissionRecordDao::listByPage,
                userDimissionRecordDao::listByPage,
                this::compareUserDimissionRecord
        );
    }

    private void compareUserInfo(HrmsUserInfoDO oldData, UserInfoDO newData) {
        List<String> differences = new ArrayList<>();

        // 比较新表中存在的字段
        if (!Objects.equals(oldData.getUserCode(), newData.getUserCode())) {
            differences.add(String.format("userCode不一致: 旧=%s, 新=%s", oldData.getUserCode(), newData.getUserCode()));
        }
        if (!Objects.equals(oldData.getUserName(), newData.getUserName())) {
            differences.add(String.format("userName不一致: 旧=%s, 新=%s", oldData.getUserName(), newData.getUserName()));
        }
        if (!Objects.equals(oldData.getUserNameEn(), newData.getUserNameEn())) {
            differences.add(String.format("userNameEn不一致: 旧=%s, 新=%s", oldData.getUserNameEn(), newData.getUserNameEn()));
        }
        if (!Objects.equals(oldData.getSex(), newData.getSex())) {
            differences.add(String.format("sex不一致: 旧=%s, 新=%s", oldData.getSex(), newData.getSex()));
        }
        if (!Objects.equals(oldData.getBirthday(), newData.getBirthday())) {
            differences.add(String.format("birthday不一致: 旧=%s, 新=%s", oldData.getBirthday(), newData.getBirthday()));
        }
        if (!Objects.equals(oldData.getCountryCode(), newData.getCountryCode())) {
            differences.add(String.format("countryCode不一致: 旧=%s, 新=%s", oldData.getCountryCode(), newData.getCountryCode()));
        }
        if (!Objects.equals(oldData.getPhone(), newData.getPhone())) {
            differences.add(String.format("phone不一致: 旧=%s, 新=%s", oldData.getPhone(), newData.getPhone()));
        }
        if (!Objects.equals(oldData.getLocationCountry(), newData.getLocationCountry())) {
            differences.add(String.format("locationCountry不一致: 旧=%s, 新=%s", oldData.getLocationCountry(), newData.getLocationCountry()));
        }
        if (!Objects.equals(oldData.getLocationProvince(), newData.getLocationProvince())) {
            differences.add(String.format("locationProvince不一致: 旧=%s, 新=%s", oldData.getLocationProvince(), newData.getLocationProvince()));
        }
        if (!Objects.equals(oldData.getLocationCity(), newData.getLocationCity())) {
            differences.add(String.format("locationCity不一致: 旧=%s, 新=%s", oldData.getLocationCity(), newData.getLocationCity()));
        }
        if (!Objects.equals(oldData.getIsGlobalRelocation(), newData.getIsGlobalRelocation())) {
            differences.add(String.format("isGlobalRelocation不一致: 旧=%s, 新=%s", oldData.getIsGlobalRelocation(), newData.getIsGlobalRelocation()));
        }
        if (!Objects.equals(oldData.getEmployeeType(), newData.getEmployeeType())) {
            differences.add(String.format("employeeType不一致: 旧=%s, 新=%s", oldData.getEmployeeType(), newData.getEmployeeType()));
        }
        if (!Objects.equals(oldData.getIsDriver(), newData.getIsDriver())) {
            differences.add(String.format("isDriver不一致: 旧=%s, 新=%s", oldData.getIsDriver(), newData.getIsDriver()));
        }
        if (!Objects.equals(oldData.getIsDtl(), newData.getIsDtl())) {
            differences.add(String.format("isDtl不一致: 旧=%s, 新=%s", oldData.getIsDtl(), newData.getIsDtl()));
        }
        if (!Objects.equals(oldData.getIsWarehouseStaff(), newData.getIsWarehouseStaff())) {
            differences.add(String.format("isWarehouseStaff不一致: 旧=%s, 新=%s", oldData.getIsWarehouseStaff(), newData.getIsWarehouseStaff()));
        }
        if (!Objects.equals(oldData.getPostId(), newData.getPostId())) {
            differences.add(String.format("postId不一致: 旧=%s, 新=%s", oldData.getPostId(), newData.getPostId()));
        }
        if (!Objects.equals(oldData.getDeptId(), newData.getDeptId())) {
            differences.add(String.format("deptId不一致: 旧=%s, 新=%s", oldData.getDeptId(), newData.getDeptId()));
        }
        if (!Objects.equals(oldData.getOcCode(), newData.getOcCode())) {
            differences.add(String.format("ocCode不一致: 旧=%s, 新=%s", oldData.getOcCode(), newData.getOcCode()));
        }
        if (!Objects.equals(oldData.getVendorCode(), newData.getVendorCode())) {
            differences.add(String.format("vendorCode不一致: 旧=%s, 新=%s", oldData.getVendorCode(), newData.getVendorCode()));
        }
        if (!Objects.equals(oldData.getVendorName(), newData.getVendorName())) {
            differences.add(String.format("vendorName不一致: 旧=%s, 新=%s", oldData.getVendorName(), newData.getVendorName()));
        }
        if (!Objects.equals(oldData.getSettlementCenterCode(), newData.getSettlementCenterCode())) {
            differences.add(String.format("settlementCenterCode不一致: 旧=%s, 新=%s", oldData.getSettlementCenterCode(), newData.getSettlementCenterCode()));
        }
        if (!Objects.equals(oldData.getEmail(), newData.getEmail())) {
            differences.add(String.format("email不一致: 旧=%s, 新=%s", oldData.getEmail(), newData.getEmail()));
        }
        if (!Objects.equals(oldData.getStatus(), newData.getStatus())) {
            differences.add(String.format("status不一致: 旧=%s, 新=%s", oldData.getStatus(), newData.getStatus()));
        }
        if (!Objects.equals(oldData.getWorkStatus(), newData.getWorkStatus())) {
            differences.add(String.format("workStatus不一致: 旧=%s, 新=%s", oldData.getWorkStatus(), newData.getWorkStatus()));
        }
        if (!Objects.equals(oldData.getProfilePhotoUrl(), newData.getProfilePhotoUrl())) {
            differences.add(String.format("profilePhotoUrl不一致: 旧=%s, 新=%s", oldData.getProfilePhotoUrl(), newData.getProfilePhotoUrl()));
        }

        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("用户信息数据[oldId=" + oldData.getId() + ",newId=" + newData.getId() + "]不一致差异为：" + String.join(", ", differences));
        }
    }

    private void compareUserEntryRecord(HrmsUserEntryRecordDO oldData, UserEntryRecordDO newData) {
        List<String> differences = new ArrayList<>();

        // 比较新表中存在的字段
        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("id不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }
        if (!Objects.equals(oldData.getUserId(), newData.getUserId())) {
            differences.add(String.format("userId不一致: 旧=%s, 新=%s", oldData.getUserId(), newData.getUserId()));
        }
        if (!Objects.equals(oldData.getEntryDate(), newData.getEntryDate())) {
            differences.add(String.format("entryDate不一致: 旧=%s, 新=%s",
                    oldData.getEntryDate(), newData.getEntryDate()));
        }
        if (!Objects.equals(oldData.getConfirmDate(), newData.getConfirmDate())) {
            differences.add(String.format("confirmDate不一致: 旧=%s, 新=%s",
                    oldData.getConfirmDate(), newData.getConfirmDate()));
        }
        if (!Objects.equals(oldData.getEntryStatus(), newData.getEntryStatus())) {
            differences.add(String.format("entryStatus不一致: 旧=%s, 新=%s",
                    oldData.getEntryStatus(), newData.getEntryStatus()));
        }

        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("用户入职记录数据[oldId=" + oldData.getId() + ",newId=" + newData.getId() + "]不一致差异为：" + String.join(", ", differences));

        }
    }

    private void compareUserDimissionRecord(HrmsUserDimissionRecordDO oldData, UserDimissionRecordDO newData) {
        List<String> differences = new ArrayList<>();

        // 比较新表中存在的字段
        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("id不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }
        if (!Objects.equals(oldData.getUserId(), newData.getUserId())) {
            differences.add(String.format("userId不一致: 旧=%s, 新=%s", oldData.getUserId(), newData.getUserId()));
        }
        if (!Objects.equals(oldData.getPlanDimissionDate(), newData.getPlanDimissionDate())) {
            differences.add(String.format("planDimissionDate不一致: 旧=%s, 新=%s",
                    oldData.getPlanDimissionDate(), newData.getPlanDimissionDate()));
        }
        if (!Objects.equals(oldData.getActualDimissionDate(), newData.getActualDimissionDate())) {
            differences.add(String.format("actualDimissionDate不一致: 旧=%s, 新=%s",
                    oldData.getActualDimissionDate(), newData.getActualDimissionDate()));
        }
        if (!Objects.equals(oldData.getDimissionStatus(), newData.getDimissionStatus())) {
            differences.add(String.format("dimissionStatus不一致: 旧=%s, 新=%s",
                    oldData.getDimissionStatus(), newData.getDimissionStatus()));
        }

        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("用户离职记录数据[oldId=" + oldData.getId() + ",newId=" + newData.getId() + "]不一致差异为：" + String.join(", ", differences));

        }
    }



}
