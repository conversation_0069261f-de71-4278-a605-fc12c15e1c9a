package com.imile.attendance.shift.factory;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.shift.ShiftSourceEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserClassConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.model.ShiftTaskRecordDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.ShiftQueryService;
import com.imile.attendance.shift.ShiftTaskManage;
import com.imile.attendance.shift.ShiftTaskService;
import com.imile.attendance.shift.bo.ShiftTask;
import com.imile.attendance.shift.bo.ShiftTaskBO;
import com.imile.attendance.shift.dto.BatchUserShiftCheckDTO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateFormatterUtil;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description
 */
public abstract class UserShiftConfigAbstractFactory {

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private ShiftTaskService shiftTaskService;
    @Resource
    private ShiftTaskManage shiftTaskManage;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private ShiftQueryService shiftQueryService;

    /**
     * 判断是否匹配当前排班类型
     * @param shiftType 排班类型
     * @return boolean
     */
    public abstract boolean isMatch(String shiftType);


    /**
     * 批量检查用户班次配置是否满足排班条件
     * 检查规则：
     * 1. 所有用户的班次类型必须相同
     * 2. 用户关联的班次必须与最常见的班次配置一致（服从多数）
     *
     * 示例：
     * 用户A关联班次：[早班,中班]
     * 用户B关联班次：[早班,中班]
     * 用户C关联班次：[早班,中班,晚班]
     * 结果：用户C不满足条件（因为A、B的班次配置[早班,中班]出现频率更高）
     *
     * @param userIdList 待检查的用户ID列表
     * @return BatchUserShiftCheckDTO 检查结果
     *         - userIdList: 输入的用户列表
     *         - currentUserCount: 当前检查的用户总数
     *         - notMatchUserDTOList: 不满足条件的用户列表，为空表示所有用户都满足条件
     * @throws BusinessLogicException 当用户班次类型不一致时抛出异常
     */
    public BatchUserShiftCheckDTO checkBatchUserShiftConfig(List<Long> userIdList){
        if (CollectionUtils.isEmpty(userIdList)) {
            return BatchUserShiftCheckDTO.initEmpty();
        }
        List<UserClassConfigDTO> userClassConfigDTOS = punchClassConfigQueryService.selectUserClassConfigList(userIdList);
        if (CollectionUtils.isEmpty(userClassConfigDTOS)) {
            return BatchUserShiftCheckDTO.initEmpty();
        }
        if (userClassConfigDTOS.size() != userIdList.size()) {
            Set<Long> usersWithConfig = userClassConfigDTOS.stream()
                    .map(UserClassConfigDTO::getUserId)
                    .collect(Collectors.toSet());
            List<Long> usersWithoutConfig = userIdList.stream()
                    .filter(userId -> !usersWithConfig.contains(userId))
                    .collect(Collectors.toList());
            List<AttendanceUser> usersWithoutConfigInfo = userService.listUsersByIds(usersWithoutConfig);
            List<BatchUserShiftCheckDTO.NotMatchUserDTO> notMatchUserDTOList = usersWithoutConfigInfo.stream()
                    .map(user -> {
                        BatchUserShiftCheckDTO.NotMatchUserDTO notMatchUserDTO = new BatchUserShiftCheckDTO.NotMatchUserDTO();
                        notMatchUserDTO.setUserId(user.getId());
                        notMatchUserDTO.setUserCode(user.getUserCode());
                        notMatchUserDTO.setUserName(user.getUserName());
                        notMatchUserDTO.setUserNameEn(user.getUserNameEn());
                        return notMatchUserDTO;
                    }).collect(Collectors.toList());
            return BatchUserShiftCheckDTO.builder()
                    .userIdList(userIdList)
                    .currentUserCount(userIdList.size())
                    .notMatchUserDTOList(notMatchUserDTOList)
                    .build();
        }
        //1.班次类型相同 2：人关联的班次完全一致
        Set<String> classNatureSet = userClassConfigDTOS.stream()
                .map(UserClassConfigDTO::getClassNature)
                .collect(Collectors.toSet());
        if (classNatureSet.size() > 1) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "人员的班次类型不一致，不可以排班");
        }
        //获取排班不一致的员工
        Map<Long, List<PunchClassConfigSelectDTO>> userPunchClassMap = userClassConfigDTOS.stream()
                .collect(Collectors.toMap(UserClassConfigDTO::getUserId, UserClassConfigDTO::getClassConfigSelectList, (existing, replacement) -> existing));

        Map<Set<Long>, List<Long>> classConfigGroupMap = new HashMap<>();

        // 按班次配置对用户进行分组
        for (Map.Entry<Long, List<PunchClassConfigSelectDTO>> entry : userPunchClassMap.entrySet()) {
            Set<Long> classIds = entry.getValue().stream()
                    .map(PunchClassConfigSelectDTO::getId)
                    .collect(Collectors.toSet());

            if (!classConfigGroupMap.containsKey(classIds)) {
                classConfigGroupMap.put(classIds, new ArrayList<>());
            }
            classConfigGroupMap.get(classIds).add(entry.getKey());
        }

        // 找出人数最多的班次配置组
        Map.Entry<Set<Long>, List<Long>> majorityGroup = classConfigGroupMap.entrySet().stream()
                .max(Comparator.comparingInt(entry -> entry.getValue().size()))
                .orElse(null);


        if (majorityGroup != null && classConfigGroupMap.size() > 1) {
            List<Long> notMatchUserIds = userPunchClassMap.entrySet().stream()
                    .filter(entry -> {
                        Set<Long> currentUserClassIds = entry.getValue().stream()
                                .map(PunchClassConfigSelectDTO::getId)
                                .collect(Collectors.toSet());
                        return !majorityGroup.getKey().equals(currentUserClassIds);
                    })
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(notMatchUserIds)) {
                // 获取不匹配用户的信息
                List<AttendanceUser> notMatchUsers = userService.listUsersByIds(notMatchUserIds);
                List<BatchUserShiftCheckDTO.NotMatchUserDTO> notMatchUserDTOList = notMatchUsers.stream()
                        .map(user -> {
                            BatchUserShiftCheckDTO.NotMatchUserDTO notMatchUserDTO = new BatchUserShiftCheckDTO.NotMatchUserDTO();
                            notMatchUserDTO.setUserId(user.getId());
                            notMatchUserDTO.setUserCode(user.getUserCode());
                            notMatchUserDTO.setUserName(user.getUserName());
                            notMatchUserDTO.setUserNameEn(user.getUserNameEn());
                            return notMatchUserDTO;
                        }).collect(Collectors.toList());

                return BatchUserShiftCheckDTO.builder()
                        .userIdList(userIdList)
                        .currentUserCount(userIdList.size())
                        .notMatchUserDTOList(notMatchUserDTOList)
                        .build();
            }
        }

        return BatchUserShiftCheckDTO.builder()
                .userIdList(userIdList)
                .currentUserCount(userIdList.size())
                .notMatchUserDTOList(Collections.emptyList())
                .build();
    }


    public void checkSchedulingLimit(Boolean fromPage, AttendanceUser user) {
        if (judgeSchedulingLimit(user) && fromPage) {
            throw BusinessLogicException.getException(ErrorCodeEnum.SCHEDULING_LIMIT);
        }
    }

    /**
     * 排班限制
     * 目前墨西哥仓内劳务派遣员工不允许页面排班
     *
     * @param attendanceUser 用户
     */
    public boolean judgeSchedulingLimit(AttendanceUser attendanceUser) {
        return shiftQueryService.judgeSchedulingLimit(attendanceUser);
    }


    /**
     * 排班模板方法
     * 定义了排班的标准流程:
     * 1. 参数校验
     * 2. 任务占用检查
     * 3. 初始化任务
     * 4. 异步保存任务
     * 5. 执行任务
     * 6. 更新任务状态
     *
     * @param userIdList 待排班用户列表
     * @param shiftSource 排班来源
     * @param shiftOperation 具体的排班操作
     */
    public void doShift(List<Long> userIdList,
                        ShiftSourceEnum shiftSource,
                        Consumer<String> shiftOperation) {
        // 1. 检查用户是否被其他任务占用
        Boolean isUserOccupied = shiftTaskService.checkUsersIsOccupiedByTask(userIdList);
        if (isUserOccupied) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "Please try again later as the user is in a scheduling task that is currently in progress.");
        }
        // 2. 生成任务标识
        String shiftTaskFlag = ShiftTask.generateShiftTaskFlag(shiftSource);

        // 3. 初始化任务
        ShiftTask shiftTask = ShiftTask.of(shiftTaskFlag, shiftSource, userIdList, shiftOperation);
        ShiftTaskBO shiftTaskBO = shiftTaskService.init(shiftTask);


        // 4. 异步保存任务结果
        CompletableFuture.runAsync(
                () -> shiftTaskManage.saveBO(shiftTaskBO)
        );

        // 5. 执行任务
        Boolean isTaskSuccess = shiftTaskService.runTask(shiftTask);
        // 获取任务记录
        ShiftTaskRecordDO shiftTaskRecordDO = shiftTaskBO.getShiftTaskRecordDO();
        // 设置任务执行时间
        shiftTaskRecordDO.setCostSecond(shiftTask.getCostSecond());

        // 6. 更新任务状态
        if (isTaskSuccess) {
            // 设置任务成功信息
            shiftTaskService.updateCompleted(shiftTaskBO);
        } else {
            // 设置错误信息
            shiftTaskRecordDO.setErrorMessage(shiftTask.getErrorMsg());
            // 设置任务失败信息
            shiftTaskService.updateFailed(shiftTaskBO);
        }
    }

    /**
     * 构造排班DO&排班打卡DO
     */
    public void shiftDayInfoBuild(Long userId,
                                  Long dayId,
                                  Long classId,
                                  String dayShiftRule,
                                  Long attendanceConfigId,
                                  ShiftSourceEnum shiftSourceEnum,
                                  String taskFlag,
                                  List<UserShiftConfigDO> addUserShiftConfigList) {
        UserShiftConfigDO userShiftConfigDO = new UserShiftConfigDO();
        userShiftConfigDO.setId(defaultIdWorker.nextId());
        userShiftConfigDO.setUserId(userId);
        userShiftConfigDO.setAttendanceConfigId(attendanceConfigId);
        userShiftConfigDO.setPunchClassConfigId(classId);
        userShiftConfigDO.setClassTime(DateUtil.parse(dayId.toString(), DateFormatterUtil.FORMAT_YYYYMMDD));
        userShiftConfigDO.setDayId(dayId);
//        userShiftConfigDO.setDayShiftRule(StringUtils.isNotBlank(dayShiftRule) ? dayShiftRule : punchConfigDO.getPunchConfigName());
        userShiftConfigDO.setDayShiftRule(dayShiftRule);
        userShiftConfigDO.setDataSource(shiftSourceEnum.getCode());
        userShiftConfigDO.setShiftType(shiftSourceEnum.getCategory().getCode());
        userShiftConfigDO.setTaskFlag(taskFlag);
        userShiftConfigDO.setIsLatest(BusinessConstant.Y);
        BaseDOUtil.fillDOInsertByUsrOrSystem(userShiftConfigDO);
        addUserShiftConfigList.add(userShiftConfigDO);

    }
}
