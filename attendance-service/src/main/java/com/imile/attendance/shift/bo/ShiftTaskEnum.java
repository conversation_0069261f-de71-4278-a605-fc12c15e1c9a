package com.imile.attendance.shift.bo;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> chen
 * @Date 2025/5/15
 * @Description 排班任务状态枚举
 */
@Getter
public enum ShiftTaskEnum {

    /**
     * 执行中
     */
    EXECUTING(1, "执行中", "Executing"),

    /**
     * 执行完成
     */
    COMPLETED(2, "执行完成", "Completed"),

    /**
     * 执行失败
     */
    FAILED(3, "执行失败", "Failed");

    private final Integer code;
    private final String desc;
    private final String descEn;

    private static final Map<Integer, ShiftTaskEnum> cacheMap = new ConcurrentHashMap<>();

    static {
        for (ShiftTaskEnum statusEnum : values()) {
            cacheMap.put(statusEnum.getCode(), statusEnum);
        }
    }

    ShiftTaskEnum(Integer code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static ShiftTaskEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        return cacheMap.get(code);
    }
}
