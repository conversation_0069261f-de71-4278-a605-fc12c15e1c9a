package com.imile.attendance.shift.command;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description
 */
@Data
public class CycleShiftCommand {

    /**
     * 批量排班用户
     */
    @NotEmpty(message = "userIdList不能为空")
    private List<Long> userIdList;

    /**
     * 循环周期
     */
    @NotNull(message = "cyclePeriod不能为空")
    @Min(value = 1, message = "cyclePeriod不能小于1")
    @Max(value = 31, message = "cyclePeriod不能大于31")
    private Integer cyclePeriod;

    /**
     * 是否覆盖公共节假日  1:覆盖 0:不覆盖 todo
     */
    @NotNull(message = "isOverrideHoliday不能为空")
    private Integer isOverrideHoliday;

    /**
     * 批量排班每天班次信息
     */
    @NotEmpty(message = "cycleShiftDayParamList不能为空")
    private List<CycleShiftDayCommand> cycleShiftDayParamList;

    /**
     * 筛选器选择的班次，没有为空即可
     */
    private List<Long> classIdList;

    /**
     * 来自页面排班
     */
    private Boolean fromPage = Boolean.FALSE;


}
