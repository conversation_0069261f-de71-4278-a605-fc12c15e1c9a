package com.imile.attendance.shift.job;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.shift.ShiftSourceEnum;
import com.imile.attendance.enums.shift.ShiftTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserCycleShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.shift.ShiftTaskService;
import com.imile.attendance.shift.UserCycleShiftConfigManage;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.shift.command.CycleShiftDayCommand;
import com.imile.attendance.shift.dto.CycleShiftDayDTO;
import com.imile.attendance.shift.factory.AutoShiftConfigFactory;
import com.imile.attendance.shift.factory.CycleShiftConfigFactory;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.StatusEnum;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import com.imile.util.BeanUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 循环排班自动续期
 *
 * <AUTHOR>
 * @since 2025/4/23
 */
@Slf4j
@Component
public class CycleShiftHandler {

    @Resource
    private UserCycleShiftConfigManage userCycleShiftConfigManage;
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private PunchClassConfigDao punchClassConfigDao;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private AutoShiftConfigFactory autoShiftConfigFactory;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private ShiftTaskService shiftTaskService;
    @Resource
    private CycleShiftConfigFactory cycleShiftConfigFactory;


    @XxlJob(BusinessConstant.JobHandler.CYCLE_SHIFT_HANDLER)
    public ReturnT<String> cycleShiftHandler(String param) {
        XxlJobLogger.log("循环排班自动续期任务开始,参数为:{}", param);

        CycleShiftHandler.CycleShiftHandlerParam handlerParam = StringUtils.isNotBlank(param)
                ? JSON.parseObject(param, CycleShiftHandler.CycleShiftHandlerParam.class)
                : new CycleShiftHandler.CycleShiftHandlerParam();

        long currentDayId;
        Date nowDate;
        if (Objects.nonNull(handlerParam.getDayId())) {
            currentDayId = handlerParam.getDayId();
            nowDate = DateHelper.transferDayIdToDate(currentDayId);
        } else {
            nowDate = new Date();
            currentDayId = DateHelper.getDayId(nowDate);
        }
        cycleShiftConfigFactory.cycleShiftAutoRenewal(nowDate, currentDayId);
//        List<UserCycleShiftConfigDO> cycleShiftConfigDOList = userCycleShiftConfigManage.selectAllLatest();
//        List<UserCycleShiftConfigDO> updateCycleShiftConfigDOList = cycleShiftConfigDOList
//                .stream()
//                .filter(item -> Objects.equals(DateHelper.getDayId(DateUtil.offsetDay(item.getExpireDate(), 1)), currentDayId))
//                .collect(Collectors.toList());
//
//        List<Long> userIdList = updateCycleShiftConfigDOList.stream().map(UserCycleShiftConfigDO::getUserId).distinct().collect(Collectors.toList());
//
//        List<UserInfoDO> userInfoDOList = userInfoManage.selectByUserIdsAndClassNature(userIdList, null);
//        userInfoDOList = userInfoDOList.stream()
//                .filter(this::checkSchedulingLimit)
//                .collect(Collectors.toList());
//
//        if (CollectionUtils.isEmpty(userInfoDOList)) {
//            XxlJobLogger.log("XXL-JOB,  {} 有效循环排班用户查询为空", BusinessConstant.JobHandler.CYCLE_SHIFT_HANDLER);
//            return ReturnT.SUCCESS;
//        }
//
//        Map<Long, UserInfoDO> userIdMap = userInfoDOList.stream()
//                .collect(Collectors.toMap(UserInfoDO::getId, Function.identity()));
//
//        //查询用户的考勤日历
//        List<CalendarConfigRangeDO> calendarConfigRangeDOList = calendarManage.selectCalendarConfigRange(userIdList);
//        if (CollectionUtils.isEmpty(calendarConfigRangeDOList)) {
//            XxlJobLogger.log("XXL-JOB,  {} 日历查询为空", BusinessConstant.JobHandler.CYCLE_SHIFT_HANDLER);
//            return ReturnT.SUCCESS;
//        }
//        Map<Long, List<CalendarConfigRangeDO>> attendanceConfigRangeMap = calendarConfigRangeDOList.stream().collect(Collectors.groupingBy(CalendarConfigRangeDO::getBizId));
//
//        //查询用户排班信息
//        List<UserShiftConfigDO> userShiftConfigList = userShiftConfigManage.selectRecordByUserIdList(userIdList, currentDayId, null);
//
//        //过滤得到循环&自动排班记录
//        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigList.stream().filter(config -> !Objects.equals(ShiftTypeEnum.CUSTOM_SHIFT.getCode(), config.getShiftType())).collect(Collectors.toList());
//        //自定义排班记录
//        List<UserShiftConfigDO> customUserShiftConfigDOList = userShiftConfigList.stream().filter(config -> Objects.equals(ShiftTypeEnum.CUSTOM_SHIFT.getCode(), config.getShiftType())).collect(Collectors.toList());
//        Map<Long, List<UserShiftConfigDO>> customShiftConfigMap = customUserShiftConfigDOList.stream().collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));
//
//        //更新旧的排班记录为历史版本
//        userShiftConfigDOList.forEach(item -> {
//            item.setIsLatest(BusinessConstant.N);
//            BaseDOUtil.fillDOInsertByUsrOrSystem(item);
//        });
//
//        List<UserCycleShiftConfigDO> addCycleShiftConfigDOList = new ArrayList<>();
//        List<UserShiftConfigDO> addUserShiftConfigDOList = new ArrayList<>();
//
//        for (UserCycleShiftConfigDO cycleShiftConfigDO : updateCycleShiftConfigDOList) {
//            UserInfoDO userInfoDO = userIdMap.get(cycleShiftConfigDO.getUserId());
//            if (Objects.isNull(userInfoDO)) {
//                continue;
//            }
//            boolean isOccupied = shiftTaskService.checkUsersIsOccupiedByTask(Collections.singletonList(userInfoDO.getId()));
//            if (isOccupied) {
//                log.error("员工{}循环排班续期,存在其他未执行完的排班任务", userInfoDO.getUserCode());
//                continue;
//            }
//
//            cycleShiftConfigDO.setExpireDate(DateUtil.offset(DateUtil.endOfDay(cycleShiftConfigDO.getExpireDate()), DateField.MILLISECOND, -999));
//            cycleShiftConfigDO.setIsLatest(BusinessConstant.N);
//            BaseDOUtil.fillDOUpdateByUserOrSystem(cycleShiftConfigDO);
//
//            //生成新的循环排班规则
//            UserCycleShiftConfigDO addConfigDO = BeanUtils.convert(cycleShiftConfigDO, UserCycleShiftConfigDO.class);
//            addConfigDO.setId(defaultIdWorker.nextId());
//            addConfigDO.setEffectDate(DateUtil.beginOfDay(nowDate));
//            Date expireDate = DateUtil.endOfDay(DateUtil.offsetDay(nowDate, addConfigDO.getCyclePeriod() - 1));
//            addConfigDO.setExpireDate(DateUtil.offset(expireDate, DateField.MILLISECOND, -999));
//            addConfigDO.setIsLatest(BusinessConstant.Y);
//            BaseDOUtil.fillDOInsertByUsrOrSystem(addConfigDO);
//            addCycleShiftConfigDOList.add(addConfigDO);
//
//            //生成排班和记录
//            List<CycleShiftDayCommand> cycleShiftDayParamList = JSON.parseArray(addConfigDO.getDayShiftInfo(), CycleShiftDayCommand.class);
//            List<CycleShiftDayDTO> cycleShiftDayDTOList = new ArrayList<>();
//            Set<Long> classIds = new HashSet<>();
//            for (CycleShiftDayCommand dayCommand : cycleShiftDayParamList) {
//                String[] daySort = dayCommand.getSort().split(BusinessConstant.DEFAULT_DELIMITER);
//                for (String sort : daySort) {
//                    CycleShiftDayDTO dayDTO = new CycleShiftDayDTO();
//                    dayDTO.setSort(Integer.valueOf(sort));
//                    dayDTO.setClassId(dayCommand.getClassId());
//                    dayDTO.setDayShiftRule(dayCommand.getDayShiftRule());
//                    cycleShiftDayDTOList.add(dayDTO);
//                }
//                if (Objects.nonNull(dayCommand.getClassId())) {
//                    classIds.add(dayCommand.getClassId());
//                }
//            }
//            //升序
//            cycleShiftDayDTOList = cycleShiftDayDTOList.stream().sorted(Comparator.comparing(CycleShiftDayDTO::getSort)).collect(Collectors.toList());
//
//            List<CalendarConfigRangeDO> calendarConfigRangeList = attendanceConfigRangeMap.get(addConfigDO.getUserId());
//            if (CollectionUtils.isEmpty(calendarConfigRangeList)) {
//                continue;
//            }
//
//            //查询用户最新班次信息
//            List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectByIds(classIds)
//                    .stream().filter(config -> Objects.equals(StatusEnum.ACTIVE.getCode(), config.getStatus()) && Objects.equals(BusinessConstant.Y, config.getIsLatest()))
//                    .collect(Collectors.toList());
//
//            if (CollectionUtils.isEmpty(punchClassConfigDOList)) {
//                log.info("{} 有效班次查询为空", BusinessConstant.JobHandler.CYCLE_SHIFT_HANDLER);
//                continue;
//            }
//
//            //找个该用户的打卡的班次
//            PunchClassConfigDO punchClassConfigDO = punchClassConfigDOList.get(0);
//            if (Objects.isNull(punchClassConfigDO)) {
//                continue;
//            }
//
//            List<UserShiftConfigDO> existUserShiftConfigList = customShiftConfigMap.get(cycleShiftConfigDO.getUserId());
//            //有排班记录且等级高于循环排班
//            List<Long> existDayIdList = existUserShiftConfigList.stream().map(UserShiftConfigDO::getDayId).collect(Collectors.toList());
//
//            int initCycleValueTemp = 0;
//            Long dayIdTemp = currentDayId;
//            do {
//                for (CycleShiftDayDTO dayDTO : cycleShiftDayDTOList) {
//                    if (initCycleValueTemp >= INIT_CYCLE_VALUE) {
//                        break;
//                    }
//                    //存在优先级高的排班记录，不能覆盖
//                    if (existDayIdList.contains(dayIdTemp)) {
//                        //后移一天
//                        dayIdTemp = DateHelper.getNextDayId(dayIdTemp);
//                        initCycleValueTemp++;
//                        continue;
//                    }
//                    //当天不需要排班
//                    if (dayDTO.getClassId() == null && StringUtils.isBlank(dayDTO.getDayShiftRule())) {
//                        dayIdTemp = DateHelper.getNextDayId(dayIdTemp);
//                        initCycleValueTemp++;
//                        continue;
//                    }
//                    autoShiftConfigFactory.shiftDayInfoBuild(
//                            userInfoDO.getId(),
//                            dayIdTemp,
//                            dayDTO.getClassId(),
//                            dayDTO.getDayShiftRule(),
//                            calendarConfigRangeList.get(0).getId(),
//                            ShiftSourceEnum.CYCLE_SHIFT,
//                            "循环排班自动续期",
//                            addUserShiftConfigDOList
//                    );
//                    dayIdTemp = DateHelper.getNextDayId(dayIdTemp);
//                    initCycleValueTemp++;
//                }
//            } while (initCycleValueTemp < INIT_CYCLE_VALUE);
//        }
//        userShiftConfigManage.cycleShiftSave(userShiftConfigDOList, addUserShiftConfigDOList, updateCycleShiftConfigDOList, addCycleShiftConfigDOList);
        XxlJobLogger.log("循环排班自动续期任务结束");
        return ReturnT.SUCCESS;
    }


    /**
     * 过滤仓内MEX、BRA劳务员工
     */
    public boolean checkSchedulingLimit(UserInfoDO user) {
        return !(BusinessConstant.WAREHOUSE_COUNTRY_FILTER.contains(user.getLocationCountry())
                && Objects.equals(BusinessConstant.Y, user.getIsWarehouseStaff())
                && Objects.equals(BusinessConstant.N, user.getIsDriver())
                && Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), user.getEmployeeType()))
                && Objects.equals(WorkStatusEnum.ON_JOB.getCode(), user.getWorkStatus());
    }


    @Data
    private static class CycleShiftHandlerParam {

        /**
         * 日期 yyyyMMdd
         */
        private Long dayId;
    }
}
