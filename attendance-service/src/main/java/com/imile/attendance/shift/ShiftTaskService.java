package com.imile.attendance.shift;

import com.imile.attendance.shift.bo.ShiftTask;
import com.imile.attendance.shift.bo.ShiftTaskBO;
import com.imile.attendance.shift.dto.UserOccupiedByTaskResult;

import java.util.List;

/**
 * 排班任务服务接口
 *
 * <AUTHOR> chen
 * @Date 2025/4/19 
 */
public interface ShiftTaskService {

    ShiftTaskBO init(ShiftTask shiftTask);

    void updateCompleted(ShiftTaskBO shiftTaskBO);

    void updateFailed(ShiftTaskBO shiftTaskBO);

    void batchSetUserOccupiedByTask(List<Long> userIdList, String shiftTaskFlag);

    void batchSetUserReleaseByTask(List<Long> userIdList, String shiftTaskFlag);

    /**
     * 检查用户列表是否被排班任务占用
     * 
     * @param userIdList 待检查的用户ID列表
     * @return true: 如果列表中任一用户被占用
     *         false: 如果所有用户都未被占用，或传入空列表
     */
    Boolean checkUsersIsOccupiedByTask(List<Long> userIdList);

    /**
     * 获取用户列表的排班任务占用详情，如果传入空列表，返回空集合
     * 
     * @param userIdList 待查询的用户ID列表
     * @return 用户排班任务占用状态结果列表
     *
     */
    List<UserOccupiedByTaskResult> getUserOccupiedByTaskResult(List<Long> userIdList);


    Integer getRunningTaskSize();

    Boolean runTask(ShiftTask shiftTask);
}
