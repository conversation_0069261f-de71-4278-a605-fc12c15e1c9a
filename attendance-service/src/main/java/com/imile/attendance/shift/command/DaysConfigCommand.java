package com.imile.attendance.shift.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description
 */
@Data
public class DaysConfigCommand {

    /**
     * 日期，yyyy-MM-dd格式
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    /**
     * 考勤日历ID
     */
    private Long attendanceConfigId;

    /**
     * 班次信息id
     */
    private Long classId;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 当天的排班规则，有班次时为班次名称，无班次是是OFF H
     * 这个字段很重要，通过这个字段是否为空，来判断当天有没有排班
     */
    private String dayShiftRule;

    /**
     * yyyyMMdd格式
     */
    private Long dayId;

    /**
     * yyyy格式
     */
    private Long year;

    /**
     * 01 02这种形式
     */
    private String month;

    /**
     * 月份的第几天
     */
    private Long day;

    /**
     * yyyyMM格式
     */
    private String yearMonth;
}
