package com.imile.attendance.shift.factory;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.calendar.UserCalendarService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.shift.ShiftSourceEnum;
import com.imile.attendance.enums.shift.ShiftTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserCycleShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.UserCycleShiftConfigManage;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.shift.UserShiftLogService;
import com.imile.attendance.shift.command.CancelCycleShiftCommand;
import com.imile.attendance.shift.command.CycleShiftCommand;
import com.imile.attendance.shift.command.CycleShiftDayCommand;
import com.imile.attendance.shift.dto.BatchUserShiftCheckDTO;
import com.imile.attendance.shift.dto.CycleShiftDayDTO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.StatusEnum;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import com.imile.util.BeanUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 循环排班配置工厂类
 * <p>
 * 该类负责处理与循环排班相关的所有操作，包括：
 * 1. 创建循环排班配置
 * 2. 取消循环排班配置
 * 3. 循环排班自动续期
 * 4. 检查用户是否符合循环排班条件
 * 5. 构建循环排班配置
 * </p>
 * <p>
 * 循环排班是一种按照固定周期重复的排班方式，适用于需要定期重复相同排班模式的场景。
 * 该类继承自UserShiftConfigAbstractFactory，使用工厂模式处理不同类型的排班配置。
 * </p>
 *
 * <AUTHOR> chen
 * @Date 2025/4/18
 */
@Slf4j
@Component
public class CycleShiftConfigFactory extends UserShiftConfigAbstractFactory {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private UserCycleShiftConfigManage userCycleShiftConfigManage;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private UserCalendarService userCalendarService;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private LogRecordService logRecordService;
    @Resource
    private UserShiftLogService userShiftLogService;
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private PunchClassConfigDao punchClassConfigDao;

    /**
     * 默认循环周期值，用于限制循环排班的最大天数
     */
    private final static Integer INIT_CYCLE_VALUE = 31;

    /**
     * 检查用户是否符合排班限制条件
     *
     * @param user 用户信息对象
     * @return 如果用户符合排班条件返回true，否则返回false
     */
    public boolean checkSchedulingLimit(UserInfoDO user) {
        return !(BusinessConstant.WAREHOUSE_COUNTRY_FILTER.contains(user.getLocationCountry())
                && Objects.equals(BusinessConstant.Y, user.getIsWarehouseStaff())
                && Objects.equals(BusinessConstant.N, user.getIsDriver())
                && Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), user.getEmployeeType()))
                && Objects.equals(WorkStatusEnum.ON_JOB.getCode(), user.getWorkStatus());
    }

    /**
     * 判断当前工厂是否匹配指定的排班类型
     * <p>
     * 该方法用于工厂模式中的类型匹配，决定使用哪个工厂处理特定类型的排班。
     * CycleShiftConfigFactory匹配AUTO_SHIFT类型的排班。
     * </p>
     *
     * @param shiftType 排班类型编码
     * @return 如果匹配返回true，否则返回false
     */
    @Override
    public boolean isMatch(String shiftType) {
        return ShiftTypeEnum.AUTO_SHIFT.getCode().equals(shiftType);
    }

    /**
     * 创建循环排班配置
     * <p>
     * 该方法用于为指定用户创建循环排班配置。它会首先检查用户列表是否为空，
     * 然后调用doShift方法执行排班任务，并将具体的排班逻辑委托给doCycleShift方法。
     * </p>
     *
     * @param cycleShiftCommand 循环排班命令对象，包含用户列表、周期、班次信息等
     */
    public void cycleShift(CycleShiftCommand cycleShiftCommand) {
        log.info("Adding cycle shift config:{}", cycleShiftCommand);
        List<Long> userIdList = cycleShiftCommand.getUserIdList();
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        doShift(
                userIdList,
                ShiftSourceEnum.CYCLE_SHIFT,
                (taskFlag) -> doCycleShift(taskFlag, cycleShiftCommand)
        );
    }

    /**
     * 执行循环排班的具体逻辑
     * <p>
     * 该方法实现循环排班的核心逻辑，包括：
     * 1. 检查用户是否符合循环排班条件
     * 2. 获取用户的日历配置
     * 3. 处理现有的排班记录
     * 4. 构建循环排班日期和班次的对应关系
     * 5. 为每个用户生成新的排班记录
     * 6. 构建新的循环排班配置
     * 7. 保存或更新排班配置
     * 8. 记录操作日志
     * </p>
     *
     * @param taskFlag 任务标识，由doShift方法传入
     * @param cycleShiftCommand 循环排班命令对象
     */
    private void doCycleShift(String taskFlag, CycleShiftCommand cycleShiftCommand) {
        List<Long> userIdList = cycleShiftCommand.getUserIdList();
        BatchUserShiftCheckDTO batchUserShiftCheckDTO =
                checkCycleShiftUser(cycleShiftCommand.getFromPage(), cycleShiftCommand.getClassIdList(), userIdList);
        if (batchUserShiftCheckDTO.isAnyNotMatch()) {
            throw BusinessLogicException.getException(ErrorCodeEnum.BATCH_SHIFT_USER_NOT_MATCH,
                    batchUserShiftCheckDTO.getCurrentUserCount(),
                    batchUserShiftCheckDTO.getNotMatchUserNameStr());
        }
        Map<Long, CalendarConfigDO> userCalendarConfigMap = userCalendarService.getCalendarConfigs(userIdList);
        Date nowDate = new Date();
        long nowDayId = DateHelper.getDayId(nowDate);
        //查询用户旧的排班数据和记录（注意优先级）
        List<UserShiftConfigDO> oldUserShiftConfigDOList = new ArrayList<>();
        //添加循环排班+自动排班的记录到oldUserShiftConfigDOList。返回优先级比循环排班高的数据（自定义排班）
        List<UserShiftConfigDO> existUserShiftConfigDOList = queryAndSetOldRecordPlus(
                userIdList,
                nowDayId,
                oldUserShiftConfigDOList
        );
        List<UserShiftConfigDO> newUserShiftConfigDOList = new ArrayList<>();

        List<CycleShiftDayCommand> cycleShiftDayParamList = cycleShiftCommand.getCycleShiftDayParamList();
        List<CycleShiftDayDTO> cycleShiftDayDTOList = new ArrayList<>();
        for (CycleShiftDayCommand cycleShiftDayCommand : cycleShiftDayParamList) {
            String[] daySort = cycleShiftDayCommand.getSort().split(BusinessConstant.DEFAULT_DELIMITER);
            for (String sort : daySort) {
                CycleShiftDayDTO dayDTO = new CycleShiftDayDTO();
                dayDTO.setSort(Integer.valueOf(sort));
                dayDTO.setClassId(cycleShiftDayCommand.getClassId());
                dayDTO.setDayShiftRule(cycleShiftDayCommand.getDayShiftRule());
                cycleShiftDayDTOList.add(dayDTO);
            }
        }
        cycleShiftDayDTOList = cycleShiftDayDTOList.stream()
                .sorted(Comparator.comparing(CycleShiftDayDTO::getSort))
                .collect(Collectors.toList());

        for (Long userId : userIdList) {
            CalendarConfigDO userCalendarConfigDO = userCalendarConfigMap.get(userId);
            if (null == userCalendarConfigDO) {
                log.info("userId:{} 未配置考勤日历,不排班", userId);
                continue;
            }
            List<Long> existUserShiftDayIdList = existUserShiftConfigDOList
                    .stream()
                    .filter(item -> item.getUserId().equals(userId))
                    .map(UserShiftConfigDO::getDayId)
                    .collect(Collectors.toList());
            long dayIdTemp = nowDayId;
            int initCycleValueTemp = 0;
            do {
                for (CycleShiftDayDTO dayDTO : cycleShiftDayDTOList) {
                    if (initCycleValueTemp >= INIT_CYCLE_VALUE) {
                        break;
                    }
                    //存在优先级高的排班记录，不能覆盖
                    if (existUserShiftDayIdList.contains(dayIdTemp)) {
                        //后移一天
                        dayIdTemp = DateHelper.getNextDayId(dayIdTemp);
                        initCycleValueTemp++;
                        continue;
                    }
                    //当天不需要排班
                    if (dayDTO.getClassId() == null && StringUtils.isBlank(dayDTO.getDayShiftRule())) {
                        dayIdTemp = DateHelper.getNextDayId(dayIdTemp);
                        initCycleValueTemp++;
                        continue;
                    }
                    shiftDayInfoBuild(
                            userId,
                            dayIdTemp,
                            dayDTO.getClassId(),
                            dayDTO.getDayShiftRule(),
                            userCalendarConfigDO.getId(),
                            ShiftSourceEnum.CYCLE_SHIFT,
                            taskFlag,
                            newUserShiftConfigDOList
                    );
                    dayIdTemp = DateHelper.getNextDayId(dayIdTemp);
                    initCycleValueTemp++;
                }
            } while (initCycleValueTemp < INIT_CYCLE_VALUE);
        }
        // 新的循环排班配置列表
        List<UserCycleShiftConfigDO> addCycleShiftConfigDOS = new ArrayList<>();
        // 获取用户已存在的循环排班规则,更新为过期
        List<UserCycleShiftConfigDO> updateCycleShiftConfigDOS = userCycleShiftConfigManage.selectByUserIdList(userIdList);
        // 构建新的循环排班配置
        buildCycleShiftConfig(nowDate, cycleShiftCommand, updateCycleShiftConfigDOS, addCycleShiftConfigDOS);
        // 保存或更新循环排班配置
        userShiftConfigManage.cycleShiftSave(oldUserShiftConfigDOList, newUserShiftConfigDOList,
                updateCycleShiftConfigDOS, addCycleShiftConfigDOS);
        // 记录循环排班操作日志
        logRecordService.recordOperation(
                addCycleShiftConfigDOS.get(0),
                LogRecordOptions.buildWithRemark(OperationTypeEnum.CYCLE_SHIFT.getCode(),
                        userShiftLogService.cycleShiftLogRecord(userService.listUsersByIds(userIdList), cycleShiftDayParamList)
                )
        );
    }

    /**
     * 检查用户是否符合循环排班条件
     * <p>
     * 该方法用于检查用户是否符合循环排班的条件，包括：
     * 1. 检查用户是否存在
     * 2. 检查用户是否受排班限制
     * 3. 检查用户是否为多班次类型（循环排班只支持多班次类型的用户）
     * 4. 如果指定了班次列表，检查用户是否关联了这些班次
     * 5. 如果只有一个用户，进行特殊处理
     * </p>
     *
     * @param fromPage 是否来自页面操作，影响排班限制的应用
     * @param classIdList 班次ID列表，可以为空
     * @param userIdList 用户ID列表
     * @return 批量用户排班检查结果对象
     * @throws BusinessLogicException 如果用户不符合循环排班条件
     */
    public BatchUserShiftCheckDTO checkCycleShiftUser(Boolean fromPage, List<Long> classIdList, List<Long> userIdList) {
        // 获取用户信息
        List<AttendanceUser> userList = userService.listUsersByIds(userIdList);
        if (CollectionUtils.isEmpty(userList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
        }
        // 判断排班限制
        userList.forEach(user -> checkSchedulingLimit(fromPage, user));
        // 排班人员必须都为多班次
        boolean isAnyUserMatchFixedClass = userList.stream()
                .allMatch(user -> StringUtils.equals(user.getClassNature(), ClassNatureEnum.FIXED_CLASS.name()));
        if (isAnyUserMatchFixedClass) {
            throw BusinessLogicException.getException(ErrorCodeEnum.CYCLE_SHIFT_USER_MUST_BELONG_TO_MULTI_CLASS);
        }
        // 筛选器筛选了班次，只需要选择的员工关联的班次都包含了选择的班次即可
        if (CollectionUtils.isNotEmpty(classIdList)) {
            List<UserClassConfigDTO> userClassConfigDTOList = punchClassConfigQueryService.selectUserClassConfigList(userIdList);
            boolean userIsMatchFixedClass = userClassConfigDTOList.stream()
                    .anyMatch(UserClassConfigDTO::checkUserIsFixedClass);
            if (userIsMatchFixedClass) {
                throw BusinessLogicException.getException(ErrorCodeEnum.CYCLE_SHIFT_USER_MUST_BELONG_TO_MULTI_CLASS);
            }
            userClassConfigDTOList.forEach(userClassConfigDTO -> {
                if (!userClassConfigDTO.checkClassIdListIsContains(classIdList)) {
                    throw BusinessLogicException.getException(ErrorCodeEnum.CYCLE_SHIFT_USER_CLASS_NOT_MATCH_SELECTED_CLASS);
                }
            });
            return BatchUserShiftCheckDTO.initEmpty();
        }
        // 没有选择班次，则需要校验班次类型和关联的班次是否一致
        if (userIdList.size() == 1) {
            // 只有一人,校验班次类型和班次不为空即可
            List<UserClassConfigDTO> userClassConfigDTOList = punchClassConfigQueryService.selectUserClassConfigList(userIdList);
            if (CollectionUtils.isEmpty(userClassConfigDTOList)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.CYCLE_SHIFT_USER_CLASS_NOT_HAVE_RELATE_PUNCH_CLASS_RULE);
            }
            UserClassConfigDTO userClassConfigDTO = userClassConfigDTOList.get(0);
            if (StringUtils.equals(userClassConfigDTO.getClassNature(), ClassNatureEnum.FIXED_CLASS.name())) {
                throw BusinessLogicException.getException(ErrorCodeEnum.CYCLE_SHIFT_USER_MUST_BELONG_TO_MULTI_CLASS);
            }
            List<PunchClassConfigSelectDTO> classConfigSelectList = userClassConfigDTO.getClassConfigSelectList();
            if (CollectionUtils.isEmpty(classConfigSelectList)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.CYCLE_SHIFT_USER_CLASS_NOT_HAVE_RELATE_PUNCH_CLASS_RULE);
            }
            return BatchUserShiftCheckDTO.initEmpty();
        } else {
            // 多人情况下调用批量检查方法
            return checkBatchUserShiftConfig(userIdList);
        }
    }


    /**
     * 取消循环排班
     * <p>
     * 该方法用于取消指定用户的循环排班配置。它会将用户的循环排班配置标记为过期，
     * 并删除今日及之后的所有循环排班记录。最后记录取消操作的日志。
     * </p>
     *
     * @param cancelCycleShiftCommand 取消循环排班命令对象，包含用户列表等信息
     */
    public void cancelCycleShift(CancelCycleShiftCommand cancelCycleShiftCommand) {
        log.info("取消循环排班:{}", cancelCycleShiftCommand);
        List<Long> userIdList = cancelCycleShiftCommand.getUserIdList();
        Date nowDate = new Date();
        long nowDayId = DateHelper.getDayId(nowDate);
        // 获取用户的循环排班配置
        List<UserCycleShiftConfigDO> userCycleShiftConfigDOS = userCycleShiftConfigManage.selectByUserIdList(userIdList);
        // 将循环排班配置标记为过期
        userCycleShiftConfigDOS.forEach(item -> {
            item.setExpireDate(nowDate);
            item.setIsLatest(BusinessConstant.N);
            BaseDOUtil.fillDOUpdate(item);
        });
        // 删除今日之后（包括今日）所有循环排班
        List<UserShiftConfigDO> oldUserShiftConfigDOList = new ArrayList<>();
        queryAndSetOldRecordPlus(userIdList, nowDayId, oldUserShiftConfigDOList);

        // 保存变更
        userShiftConfigManage.cancelCycleShift(userCycleShiftConfigDOS, oldUserShiftConfigDOList);
        // 记录操作日志
        logRecordService.recordOperation(
                new UserShiftConfigDO(),
                LogRecordOptions.buildWithRemark(OperationTypeEnum.CANCEL_CYCLE_SHIFT.getCode(),
                        userShiftLogService.cancelCycleShiftLogRecord(userService.listUsersByIds(userIdList)))
        );
    }


    /**
     * 执行循环排班自动续期的具体逻辑
     * <p>
     * 该方法实现循环排班自动续期的核心逻辑，包括：
     * 1. 查询用户的考勤日历
     * 2. 查询用户的现有排班记录
     * 3. 将旧的循环排班配置标记为过期
     * 4. 创建新的循环排班配置
     * 5. 根据循环排班规则生成新的排班记录
     * 6. 保存新的循环排班配置和排班记录
     * </p>
     *
     * @param taskFlag 任务标识，由doShift方法传入
     * @param nowDate 当前日期
     * @param currentDayId 当前日期ID
     * @param userIdList 需要续期的用户ID列表
     * @param userInfoDOList 用户信息列表
     * @param updateCycleShiftConfigDOList 需要更新的循环排班配置列表
     */
    private void doCycleShiftAutoRenewal(String taskFlag,
                                         Date nowDate,
                                         Long currentDayId,
                                         List<Long> userIdList,
                                         List<UserInfoDO> userInfoDOList,
                                         List<UserCycleShiftConfigDO> updateCycleShiftConfigDOList) {
        log.info("循环排班续期开始,dayId:{}", currentDayId);
        XxlJobLogger.log("循环排班续期开始,dayId:{}", currentDayId);
        // 将用户信息转换为Map，方便查询
        Map<Long, UserInfoDO> userIdMap = userInfoDOList.stream()
                .collect(Collectors.toMap(UserInfoDO::getId, Function.identity()));

        // 查询用户的考勤日历
        List<CalendarConfigRangeDO> calendarConfigRangeDOList = calendarManage.selectCalendarConfigRange(userIdList);
        if (CollectionUtils.isEmpty(calendarConfigRangeDOList)) {
            log.info("循环排班续期日历查询为空,dayId:{},userIdList:{}", currentDayId, userIdList);
            XxlJobLogger.log("循环排班续期日历查询为空,dayId:{},userIdList:{}", currentDayId, userIdList);
            return;
        }
        Map<Long, List<CalendarConfigRangeDO>> attendanceConfigRangeMap = calendarConfigRangeDOList.stream()
                .collect(Collectors.groupingBy(CalendarConfigRangeDO::getBizId));

        // 查询用户排班信息
        List<UserShiftConfigDO> userShiftConfigList = userShiftConfigManage.selectRecordByUserIdList(userIdList, currentDayId, null);

        // 过滤得到循环&自动排班记录
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigList.stream()
                .filter(config -> !Objects.equals(ShiftTypeEnum.CUSTOM_SHIFT.getCode(), config.getShiftType()))
                .collect(Collectors.toList());
        // 自定义排班记录
        List<UserShiftConfigDO> customUserShiftConfigDOList = userShiftConfigList.stream()
                .filter(config -> Objects.equals(ShiftTypeEnum.CUSTOM_SHIFT.getCode(), config.getShiftType()))
                .collect(Collectors.toList());
        Map<Long, List<UserShiftConfigDO>> customShiftConfigMap = customUserShiftConfigDOList.stream().
                collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));

        // 更新旧的排班记录为历史版本
        userShiftConfigDOList.forEach(item -> {
            item.setIsLatest(BusinessConstant.N);
            BaseDOUtil.fillDOInsertByUsrOrSystem(item);
        });

        List<UserCycleShiftConfigDO> addCycleShiftConfigDOList = new ArrayList<>();
        List<UserShiftConfigDO> addUserShiftConfigDOList = new ArrayList<>();

        for (UserCycleShiftConfigDO cycleShiftConfigDO : updateCycleShiftConfigDOList) {
            UserInfoDO userInfoDO = userIdMap.get(cycleShiftConfigDO.getUserId());
            if (Objects.isNull(userInfoDO)) {
                continue;
            }

            // 将旧的循环排班配置标记为过期
            cycleShiftConfigDO.setExpireDate(DateUtil.offset(DateUtil.endOfDay(cycleShiftConfigDO.getExpireDate()), DateField.MILLISECOND, -999));
            cycleShiftConfigDO.setIsLatest(BusinessConstant.N);
            BaseDOUtil.fillDOUpdateByUserOrSystem(cycleShiftConfigDO);

            // 生成新的循环排班规则
            UserCycleShiftConfigDO addConfigDO = BeanUtils.convert(cycleShiftConfigDO, UserCycleShiftConfigDO.class);
            addConfigDO.setId(defaultIdWorker.nextId());
            // 新配置的生效日期是当前日期的开始时间
            addConfigDO.setEffectDate(DateUtil.beginOfDay(nowDate));
            // 使用calculateExpireDate方法计算过期时间
            addConfigDO.setExpireDate(calculateExpireDate(nowDate, addConfigDO.getCyclePeriod()));
            addConfigDO.setIsLatest(BusinessConstant.Y);
            BaseDOUtil.fillDOInsertByUsrOrSystem(addConfigDO);
            addCycleShiftConfigDOList.add(addConfigDO);

            // 生成排班和记录
            List<CycleShiftDayCommand> cycleShiftDayParamList = JSON.parseArray(addConfigDO.getDayShiftInfo(), CycleShiftDayCommand.class);
            List<CycleShiftDayDTO> cycleShiftDayDTOList = new ArrayList<>();
            Set<Long> classIds = new HashSet<>();
            for (CycleShiftDayCommand dayCommand : cycleShiftDayParamList) {
                String[] daySort = dayCommand.getSort().split(BusinessConstant.DEFAULT_DELIMITER);
                for (String sort : daySort) {
                    CycleShiftDayDTO dayDTO = new CycleShiftDayDTO();
                    dayDTO.setSort(Integer.valueOf(sort));
                    dayDTO.setClassId(dayCommand.getClassId());
                    dayDTO.setDayShiftRule(dayCommand.getDayShiftRule());
                    cycleShiftDayDTOList.add(dayDTO);
                }
                if (Objects.nonNull(dayCommand.getClassId())) {
                    classIds.add(dayCommand.getClassId());
                }
            }
            // 升序排序
            cycleShiftDayDTOList = cycleShiftDayDTOList.stream()
                    .sorted(Comparator.comparing(CycleShiftDayDTO::getSort))
                    .collect(Collectors.toList());

            List<CalendarConfigRangeDO> calendarConfigRangeList = attendanceConfigRangeMap.get(addConfigDO.getUserId());
            if (CollectionUtils.isEmpty(calendarConfigRangeList)) {
                continue;
            }

            // 查询用户最新班次信息
            List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectByIds(classIds)
                    .stream().filter(config -> Objects.equals(StatusEnum.ACTIVE.getCode(), config.getStatus()) &&
                            Objects.equals(BusinessConstant.Y, config.getIsLatest()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(punchClassConfigDOList)) {
                log.info("{} 有效班次查询为空", BusinessConstant.JobHandler.CYCLE_SHIFT_HANDLER);
                XxlJobLogger.log("{} 有效班次查询为空", BusinessConstant.JobHandler.CYCLE_SHIFT_HANDLER);
                continue;
            }

            // 找到该用户的打卡班次
            PunchClassConfigDO punchClassConfigDO = punchClassConfigDOList.get(0);
            if (Objects.isNull(punchClassConfigDO)) {
                continue;
            }

            List<UserShiftConfigDO> existUserShiftConfigList =
                    customShiftConfigMap.getOrDefault(cycleShiftConfigDO.getUserId(), Collections.emptyList());
            // 有排班记录且等级高于循环排班
            List<Long> existDayIdList = existUserShiftConfigList.stream()
                    .map(UserShiftConfigDO::getDayId)
                    .collect(Collectors.toList());

            // 生成新的排班记录
            int initCycleValueTemp = 0;
            Long dayIdTemp = currentDayId;
            do {
                for (CycleShiftDayDTO dayDTO : cycleShiftDayDTOList) {
                    if (initCycleValueTemp >= INIT_CYCLE_VALUE) {
                        break;
                    }
                    // 存在优先级高的排班记录，不能覆盖
                    if (existDayIdList.contains(dayIdTemp)) {
                        // 后移一天
                        dayIdTemp = DateHelper.getNextDayId(dayIdTemp);
                        initCycleValueTemp++;
                        continue;
                    }
                    // 当天不需要排班
                    if (dayDTO.getClassId() == null && StringUtils.isBlank(dayDTO.getDayShiftRule())) {
                        dayIdTemp = DateHelper.getNextDayId(dayIdTemp);
                        initCycleValueTemp++;
                        continue;
                    }
                    // 构建排班信息
                    shiftDayInfoBuild(
                            userInfoDO.getId(),
                            dayIdTemp,
                            dayDTO.getClassId(),
                            dayDTO.getDayShiftRule(),
                            calendarConfigRangeList.get(0).getId(),
                            ShiftSourceEnum.CYCLE_SHIFT,
                            taskFlag,
                            addUserShiftConfigDOList
                    );
                    dayIdTemp = DateHelper.getNextDayId(dayIdTemp);
                    initCycleValueTemp++;
                }
            } while (initCycleValueTemp < INIT_CYCLE_VALUE);
        }
        // 保存新的循环排班配置和排班记录
        userShiftConfigManage.cycleShiftSave(userShiftConfigDOList, addUserShiftConfigDOList, updateCycleShiftConfigDOList, addCycleShiftConfigDOList);
    }


    /**
     * 循环排班自动续期
     * <p>
     * 该方法用于自动续期即将到期的循环排班配置。它会查询所有最新的循环排班配置，
     * 筛选出到期日期的下一天等于当前日期的配置，并为这些配置创建新的循环排班周期。
     * 该方法通常由定时任务调用，每天检查是否有需要续期的配置。
     * </p>
     *
     * @param nowDate 当前日期
     * @param currentDayId 当前日期ID（格式为yyyyMMdd的长整型）
     */
    public void cycleShiftAutoRenewal(Date nowDate, Long currentDayId) {
        // 获取所有最新的循环排班配置
        List<UserCycleShiftConfigDO> cycleShiftConfigDOList = userCycleShiftConfigManage.selectAllLatest();
        // 筛选出今天到期的循环排班配置,通过比较配置的到期日期+1天是否等于当前日期来判断
        List<UserCycleShiftConfigDO> updateCycleShiftConfigDOList = cycleShiftConfigDOList
                .stream()
                .filter(item -> Objects.equals(DateHelper.getDayId(DateUtil.offsetDay(item.getExpireDate(), 1)), currentDayId))
                .collect(Collectors.toList());
        // 提取需要续期的用户ID列表
        List<Long> userIdList = updateCycleShiftConfigDOList.stream()
                .map(UserCycleShiftConfigDO::getUserId)
                .distinct()
                .collect(Collectors.toList());
        // 应用排班限制规则，过滤掉不符合条件的用户
        List<UserInfoDO> userInfoDOList = userInfoManage.selectByUserIdsAndClassNature(userIdList, null);
        userInfoDOList = userInfoDOList.stream()
                .filter(this::checkSchedulingLimit)
                .collect(Collectors.toList());
        XxlJobLogger.log("循环排班自动续期开始处理,dayId:{},符合条件的用户数量:{}", currentDayId, userInfoDOList.size());

        if (CollectionUtils.isEmpty(userInfoDOList)) {
            log.info("有效循环排班用户查询为空,dayId:{}", currentDayId);
            XxlJobLogger.log("有效循环排班用户查询为空,dayId:{}", currentDayId);
            return;
        }
        List<UserInfoDO> finalUserInfoDOList = userInfoDOList;
        // 执行排班任务
        doShift(userIdList,
                ShiftSourceEnum.CYCLE_SHIFT_RENEWAL,
                (taskFlag) -> {
                    doCycleShiftAutoRenewal(taskFlag, nowDate, currentDayId,
                            userIdList, finalUserInfoDOList, updateCycleShiftConfigDOList);
                }
        );
        XxlJobLogger.log("循环排班自动续期完成,dayId:{}", currentDayId);
    }

    /**
     * 取消指定班次的用户循环排班
     */
   /* public void cancelAppointClassCycleShift(CancelCycleShiftCommand cancelCycleShiftCommand) {
        log.info("取消指定班次用户循环排班:{}", cancelCycleShiftCommand);
        List<Long> userIdList = cancelCycleShiftCommand.getUserIdList();
        Long classId = cancelCycleShiftCommand.getClassId();
        Date nowDate = new Date();
        long nowDayId = DateHelper.getDayId(nowDate);
        List<UserCycleShiftConfigDO> userCycleShiftConfigList = userCycleShiftConfigManage.selectByUserIdListAndClassId(userIdList, classId);
        userCycleShiftConfigList.forEach(item -> {
            item.setExpireDate(nowDate);
            item.setIsLatest(BusinessConstant.N);
            BaseDOUtil.fillDOUpdate(item);
        });
        //删除今日之后（包括今日）所有循环排班
        List<UserShiftConfigDO> oldUserShiftConfigDOList = queryCycleShiftOldRecord(userIdList, classId, nowDayId);
        userShiftConfigManage.cancelCycleShift(userCycleShiftConfigList, oldUserShiftConfigDOList);
    }*/

    /**
     * 查询并设置旧的排班记录
     * <p>
     * 该方法用于查询用户的排班记录，并将优先级低于循环排班的记录（如自动排班和循环排班）
     * 标记为非最新版本，并添加到oldUserClassConfigDOList中。返回优先级高于循环排班的记录（如自定义排班）。
     * </p>
     *
     * @param userIdList 用户ID列表
     * @param startDayId 开始日期ID
     * @param oldUserClassConfigDOList 旧的排班记录列表，用于添加需要更新的记录
     * @return 优先级高于循环排班的排班记录列表
     */
    private List<UserShiftConfigDO> queryAndSetOldRecordPlus(List<Long> userIdList,
                                                             Long startDayId,
                                                             List<UserShiftConfigDO> oldUserClassConfigDOList) {
        // 查询用户的排班记录
        List<UserShiftConfigDO> userShiftConfigDOList =
                userShiftConfigManage.selectRecordByUserIdList(userIdList, startDayId, null);
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return Collections.emptyList();
        }

        // 过滤出优先级低于循环排班的记录
        List<UserShiftConfigDO> filterUserShiftConfigDOS = userShiftConfigDOList
                .stream()
                .filter(item -> ShiftTypeEnum.listCycleShiftLowPriority()
                        .contains(ShiftTypeEnum.getByCode(item.getShiftType())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterUserShiftConfigDOS)) {
            return userShiftConfigDOList;
        }
        // 将优先级低的记录标记为非最新版本
        filterUserShiftConfigDOS.forEach(item -> {
            item.setIsLatest(BusinessConstant.N);
            BaseDOUtil.fillDOUpdate(item);
        });
        oldUserClassConfigDOList.addAll(filterUserShiftConfigDOS);

        // 获取优先级比循环排班高的记录
        userShiftConfigDOList = userShiftConfigDOList.stream()
                .filter(i -> !filterUserShiftConfigDOS.contains(i))
                .collect(Collectors.toList());
        return userShiftConfigDOList;
    }

    /**
     * 查询循环排班的旧记录
     * <p>
     * 该方法用于查询指定用户、指定班次的循环排班记录，并将这些记录标记为非最新版本。
     * 主要用于取消指定班次的循环排班时使用。
     * </p>
     *
     * @param userIdList 用户ID列表
     * @param classId 班次ID
     * @param startDayId 开始日期ID
     * @return 需要更新的循环排班记录列表
     */
    private List<UserShiftConfigDO> queryCycleShiftOldRecord(List<Long> userIdList,
                                                             Long classId,
                                                             Long startDayId) {
        List<UserShiftConfigDO> result = new ArrayList<>();
        // 查询指定用户、指定班次的排班记录
        List<UserShiftConfigDO> userShiftConfigDOList =
                userShiftConfigManage.selectRecordByUserIdListAndClassId(userIdList, classId, startDayId, null);
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return result;
        }

        // 过滤出循环排班类型的记录
        List<UserShiftConfigDO> filterUserShiftConfigList = userShiftConfigDOList
                .stream()
                .filter(item -> ShiftTypeEnum.CYCLE_SHIFT.getCode().equals(item.getShiftType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterUserShiftConfigList)) {
            return result;
        }
        // 将循环排班记录标记为非最新版本
        filterUserShiftConfigList.forEach(item -> {
            item.setIsLatest(BusinessConstant.N);
            BaseDOUtil.fillDOUpdate(item);
        });
        result.addAll(filterUserShiftConfigList);
        return result;
    }

    /**
     * 计算循环排班配置的过期时间
     * <p>
     * 该方法用于计算循环排班配置的过期时间。计算逻辑为：
     * 起始日期 + 循环周期 - 1天的结束时间（减去1毫秒以避免精度问题）
     * </p>
     *
     * @param startDate 起始日期
     * @param cyclePeriod 循环周期（天数）
     * @return 计算后的过期日期
     */
    private Date calculateExpireDate(Date startDate, Integer cyclePeriod) {
        // 计算到期日期：起始日期 + 周期 - 1天的结束时间
        Date expireDate = DateUtil.endOfDay(DateUtil.offsetDay(startDate, cyclePeriod - 1));
        // 减去1毫秒以避免精度问题
        return DateUtil.offset(expireDate, DateField.MILLISECOND, -999);
    }

    /**
     * 构建循环排班配置
     * <p>
     * 该方法用于构建循环排班配置。它包含两个主要步骤：
     * 1. 将用户现有的循环排班配置标记为过期和非最新版本
     * 2. 为每个用户创建新的循环排班配置，包括设置周期、班次信息、生效日期和到期日期等
     * </p>
     *
     * @param nowDate 当前日期，用于设置新配置的生效日期
     * @param param 循环排班命令对象，包含用户列表、周期、班次信息等
     * @param updateCycleShiftConfigDOS 需要更新的循环排班配置列表（现有配置）
     * @param addCycleShiftConfigDOS 需要添加的循环排班配置列表（新配置）
     */
    private void buildCycleShiftConfig(Date nowDate,
                                       CycleShiftCommand param,
                                       List<UserCycleShiftConfigDO> updateCycleShiftConfigDOS,
                                       List<UserCycleShiftConfigDO> addCycleShiftConfigDOS) {
        // 更新循环排班规则为过期，将现有的循环排班配置标记为过期和非最新版本
        updateCycleShiftConfigDOS.forEach(item -> {
            item.setExpireDate(nowDate);
            item.setIsLatest(BusinessConstant.N);
            BaseDOUtil.fillDOUpdate(item);
        });

        for (Long userId : param.getUserIdList()) {
            UserCycleShiftConfigDO cycleShiftConfigDO = new UserCycleShiftConfigDO();
            cycleShiftConfigDO.setId(defaultIdWorker.nextId());
            cycleShiftConfigDO.setUserId(userId);
            cycleShiftConfigDO.setCyclePeriod(param.getCyclePeriod());
            cycleShiftConfigDO.setDayShiftInfo(JSON.toJSONString(param.getCycleShiftDayParamList()));
            cycleShiftConfigDO.setEffectDate(nowDate);
            cycleShiftConfigDO.setExpireDate(calculateExpireDate(nowDate, param.getCyclePeriod()));
            cycleShiftConfigDO.setIsLatest(BusinessConstant.Y);
            BaseDOUtil.fillDOInsert(cycleShiftConfigDO);
            addCycleShiftConfigDOS.add(cycleShiftConfigDO);
        }
    }
}
