package com.imile.attendance.shift.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/7
 */
@Data
public class UserArchiveShiftVO {

    /**
     * 用户排班配置
     */
    private List<ShiftDayConfigVO> shiftDayConfigInfo;

    @Data
    public static class ShiftDayConfigVO {
        /**
         * 日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date date;

        /**
         * dayId
         */
        private Long dayId;

        /**
         * 排班时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date classTime;

        /**
         * 班次信息id
         */
        private Long classId;

        /**
         * 当天的排班规则，有班次时是班次名称，无班次是OFF H 等
         */
        private String dayShiftRule;

        /**
         * 实际日历类型，P,H,W
         */
        private String calendarDayType;

        /**
         * 当为H时，展示假期名称
         */
        private String holidayName;
    }
}
