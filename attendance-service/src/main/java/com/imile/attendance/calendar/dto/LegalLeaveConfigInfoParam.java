package com.imile.attendance.calendar.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 国家法定假期信息参数
 */
@Data
@EqualsAndHashCode
public class LegalLeaveConfigInfoParam {

    /**
     * 法定假期名称
     */
    private String legalLeaveName;

    /**
     * 法定假期开始时间：legal_leave_start_day_id 示例：20240124
     */
    private Long legalLeaveStartDayId;

    /**
     * 法定假期结束时间：legal_leave_end_day_id 示例：20240124
     */
    private Long legalLeaveEndDayId;

    /**
     * 法定假期时长
     */
    private Integer legalLeaveDuration;
}
