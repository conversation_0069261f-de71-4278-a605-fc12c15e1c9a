package com.imile.attendance.calendar.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Data
public class DaysConfigParam {

    /**
     * 日期，yyyy-MM-dd格式
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;
    /**
     * 日期类型：WEEKEND 休息日 HOLIDAY 节假日
     */
    private String dayType;
}
