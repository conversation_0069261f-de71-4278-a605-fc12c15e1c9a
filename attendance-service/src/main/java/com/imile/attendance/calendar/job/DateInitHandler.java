package com.imile.attendance.calendar.job;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.calendar.BaseDayInfoService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.calendar.model.BaseDayInfoDO;
import com.imile.attendance.util.BaseDOUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description 日期相关数据初始化
 */
@Component
public class DateInitHandler {

    @Resource
    private BaseDayInfoService baseDayInfoService;

    private static final Integer DEFAULT_OFFSET_YEARS = 3;

    @XxlJob(BusinessConstant.JobHandler.DATE_INIT_HANDLER)
    public ReturnT<String> dateInit(String param) {
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}",
                BusinessConstant.JobHandler.DATE_INIT_HANDLER, param);

        DateInitParam dateInitParam = StringUtils.isEmpty(param) ?
                new DateInitParam() : JSONObject.parseObject(param, DateInitParam.class);

        // 偏移年数
        Integer offsetYear = dateInitParam.getOffset() != null && dateInitParam.getOffset() > 0 ?
                dateInitParam.getOffset() : DEFAULT_OFFSET_YEARS;
        // 默认必须18年以后的数据
        final Date startDate = dateInitParam.getStartYear() != null && dateInitParam.getStartYear() >= 2018 ?
                DateUtil.parse(dateInitParam.getStartYear().toString(), "yyyy") : DateUtil.beginOfYear(new Date());
        Integer count = 0;
        while (count < offsetYear) {

            DateTime startDateTemp = DateUtil.offset(startDate, DateField.YEAR, count);
            DateTime endTimeTemp = DateUtil.endOfYear(startDateTemp);
            XxlJobLogger.log("开始初始化【{}】年数据", DateUtil.year(startDateTemp));
            List<BaseDayInfoDO> allOneYearDays = new ArrayList<>();
            while (startDateTemp.before(endTimeTemp)) {
                // 转换数据并添加
                allOneYearDays.add(convert(startDateTemp));

                startDateTemp = DateUtil.offsetDay(startDateTemp, 1);

            }
            baseDayInfoService.saveOrUpdateBatch(allOneYearDays);

            XxlJobLogger.log("已初始化【{}】年数据，时间范围为:{} ~ {},一共{}条数据", DateUtil.year(allOneYearDays.get(0).getDate())
                    , DateUtil.formatDate(allOneYearDays.get(0).getDate()), DateUtil.formatDate(allOneYearDays.get(allOneYearDays.size() - 1).getDate()), allOneYearDays.size());
            count++;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 输入 2021-12-09,则返回对应的年份、月份、季度等信息
     *
     * @param date
     * @return
     */
    private BaseDayInfoDO convert(DateTime date) {
        BaseDayInfoDO dateObj = new BaseDayInfoDO();
        /* JDK的Calendar中默认一周的第一天是周日，Hutool中将此默认值设置为周一<br>
         *设置一周的第一天主要影响{@link #weekOfMonth()}和{@link #weekOfYear()} 两个方法
         * 因此在这里重新设置为周日
         */
        date.setFirstDayOfWeek(Week.SUNDAY);
        dateObj.setDate(date);
        // 2021
        dateObj.setYear(DateUtil.year(date));
        // 4
        dateObj.setQuarter(DateUtil.quarter(date));
        // 12
        dateObj.setMonth(DateUtil.month(date) + 1);
        // 50
        dateObj.setWeek(DateUtil.weekOfYear(date));
        // 9
        dateObj.setDay(DateUtil.dayOfMonth(date));
        // 星期四/THURSDAY/THURSDAY
        dateObj.setDayOfWeek(DateUtil.dayOfWeekEnum(date).name());
        // 20211209
        dateObj.setId(Long.parseLong(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN)));
        BaseDOUtil.fillDOInsert(dateObj);
        return dateObj;
    }


    @Data
    public static class DateInitParam {

        /**
         * 开始年份
         */
        private Integer startYear;

        /**
         * 偏移年数
         */
        private Integer offset;

    }
}
