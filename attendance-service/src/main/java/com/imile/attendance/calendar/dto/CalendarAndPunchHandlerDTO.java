package com.imile.attendance.calendar.dto;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/2/7 
 * @Description
 */
@Data
public class CalendarAndPunchHandlerDTO {

    private Long userId;

    private String userCode;

    private Long newDeptId;

    private String newCountry;

    private Long oldDeptId;

    private String oldCountry;


    /**
     * 判断国家是否变更
     * @return 是否变更
     */
    public Boolean areCountryChanged() {
        return !this.oldCountry.equals(this.newCountry);
    }

    /**
     * 判断部门是否变更
     * @return 是否变更
     */
    public Boolean areDeptChanged() {
        return !Objects.equals(this.oldDeptId, this.newDeptId);
    }
}
