package com.imile.attendance.calendar.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * <AUTHOR>
 * @since 2025/5/8
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class AttendanceArchiveCalendarUpdateDTO {

    /**
     * 旧考勤日历ID
     */
    private Long oldCalendarId;

    /**
     * 新考勤日历ID
     */
    private Long newCalendarId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 开始生效时间
     */
    private Date startDate;
}
