package com.imile.attendance.calendar.notify.listener;

import com.imile.attendance.calendar.notify.EmployeeSchedulingParam;
import com.imile.attendance.calendar.notify.event.EmployeeSchedulingRegisterEvent;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.shift.EmployeeSchedulingService;
import com.imile.attendance.shift.factory.CalendarChangeShiftConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
@Slf4j
@Component
@Resource
public class EmployeeSchedulingEventListener {

    @Resource
    private CalendarChangeShiftConfigFactory calendarChangeShiftConfigFactory;

    @Async("bizTaskThreadPool")
    @EventListener
    public void onApplicationEvent(EmployeeSchedulingRegisterEvent event) {
        if (Objects.isNull(event.getData())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        EmployeeSchedulingParam data = event.getData();
        long startTime = System.currentTimeMillis();
        log.info("EmployeeSchedulingEventListener | startTime:{},country:{},userId:{}",
                startTime, data.getCountry(), data.getUserIds());
        //处理人员排班
        calendarChangeShiftConfigFactory.handlerEmployeeScheduling(event.getData());
        log.info("EmployeeSchedulingEventListener | 耗时:{},country:{}",
                System.currentTimeMillis() - startTime, event.getData().getCountry());
    }
}
