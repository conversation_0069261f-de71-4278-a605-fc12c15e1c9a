package com.imile.attendance.calendar.notify.publish;

import com.imile.attendance.calendar.notify.EmployeeSchedulingParam;
import com.imile.attendance.calendar.notify.event.EmployeeSchedulingRegisterEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Service
public class EmployeeSchedulingEventPublisher {

    @Resource
    private ApplicationEventPublisher publisher;

    public void sendSchedulingEvent(EmployeeSchedulingParam param) {
        publisher.publishEvent(new EmployeeSchedulingRegisterEvent(this, param));
    }
}
