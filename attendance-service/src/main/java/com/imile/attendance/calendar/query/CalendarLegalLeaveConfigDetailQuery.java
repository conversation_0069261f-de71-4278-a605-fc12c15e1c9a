package com.imile.attendance.calendar.query;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 日历法定假期记录表详情查询入参
 */
@Data
public class CalendarLegalLeaveConfigDetailQuery {

    /**
     * 常驻地国家
     */
    @NotEmpty(message = "locationCountry can not null")
    private String locationCountry;

    /**
     * 年份
     */
    @NotNull(message = "year can not be null")
    private Integer year;

    /**
     * 日历id
     */
    @NotNull(message = "calendarConfigId can not be null")
    private Long calendarConfigId;
}
