package com.imile.attendance.calendar.command;

import com.imile.attendance.dto.StatusSwitchParamDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/2/7 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CalendarStatusSwitchCommand extends StatusSwitchParamDTO {

    /**
     * 当部门/用户已存在其他考勤日历方案时，是否强制覆盖 1：强制覆盖  0：不覆盖
     */
    private Integer isCoverOld;
}
