package com.imile.attendance.calendar.query;

import com.imile.common.query.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AttendanceConfigQuery extends BaseQuery {

    /**
     * 国家
     */
    private String country;

    /**
     * 配置编码批量查询
     */
    private Collection<String> nos;

    /**
     * 配置ID批量查询
     */
    private Collection<? extends Serializable> ids;

    /**
     * 配置类型
     */
    private String type;
    /**
     * 状态
     */
    private String status;

    /**
     * 是否默认
     */
    private Integer isDefault;

    private List<String> countryList;

    private List<Long> deptIdList;

    private List<Long> userIdList;
}
