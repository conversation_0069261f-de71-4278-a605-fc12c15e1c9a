package com.imile.attendance.employee.converter;

import com.imile.attendance.employee.Employee;
import com.imile.attendance.infrastructure.repository.employee.dto.UserDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> chen
 * @Date 2025/1/16 
 * @Description
 */
@Mapper
public interface EmployeeConverter {

    EmployeeConverter INSTANCE = Mappers.getMapper(EmployeeConverter.class);


    Employee mapToEmployee(UserDTO userDTO);

}
