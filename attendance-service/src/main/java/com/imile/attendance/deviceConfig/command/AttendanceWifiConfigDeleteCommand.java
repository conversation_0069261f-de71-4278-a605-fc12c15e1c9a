package com.imile.attendance.deviceConfig.command;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceWifiConfigDeleteCommand {

    private Long wifiConfigId;

    public static AttendanceWifiConfigDeleteCommand of(Long wifiConfigId) {
        return new AttendanceWifiConfigDeleteCommand(wifiConfigId);
    }
}
