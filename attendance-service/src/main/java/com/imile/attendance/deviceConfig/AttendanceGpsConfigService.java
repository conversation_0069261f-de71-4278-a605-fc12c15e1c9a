package com.imile.attendance.deviceConfig;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigExportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigImportDTO;
import com.imile.attendance.deviceConfig.mapstruct.AttendanceGpsConfigMapstruct;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceGpsConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceGpsConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceConfigFilterQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceGpsConfigQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.IpepUtils;
import com.imile.attendance.util.PageUtil;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.ucenter.api.context.RequestInfoHolder;
import com.imile.util.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * H5/移动 考勤打卡GPS配置业务类
 *
 * <AUTHOR>
 * @menu GPS打卡地址配置
 * @date 2025/4/15
 */
@Service
public class AttendanceGpsConfigService {
    @Resource
    private AttendanceGpsConfigDao attendanceGpsConfigDao;
    @Resource
    private CountryService countryService;
    @Resource
    private LogRecordService logRecordService;

    /**
     * GPS打卡配置列表
     */
    public PaginationResult<AttendanceGpsConfigDTO> list(AttendanceGpsConfigQuery query) {
        PageInfo<AttendanceGpsConfigDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> attendanceGpsConfigDao.list(query));

        List<AttendanceGpsConfigDO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<AttendanceGpsConfigDTO> gpsConfigDTOS = AttendanceGpsConfigMapstruct.INSTANCE.toPunchGpsConfigDTO(list);
        return PageUtil.getPageResult(gpsConfigDTOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }


    /**
     * 导入GPS配置
     */
    public List<AttendanceGpsConfigImportDTO> importGpsConfig(List<AttendanceGpsConfigImportDTO> param) {
        List<AttendanceGpsConfigImportDTO> failImportList = new ArrayList<>();
        // gps import check
        List<AttendanceGpsConfigImportDTO> params = checkImportParam(param, failImportList);
        // save
        Object obj = null;
        for (AttendanceGpsConfigImportDTO item : params) {
            try {
                AttendanceGpsConfigDO gpsConfigDO = BeanUtils.convert(item, AttendanceGpsConfigDO.class);
                BaseDOUtil.fillDOInsert(gpsConfigDO);
                attendanceGpsConfigDao.save(gpsConfigDO);
                if (Objects.isNull(obj)) {
                    obj = gpsConfigDO;
                }
            } catch (Exception e) {
                IpepUtils.putFail(item, e.getMessage());
                failImportList.add(item);
            }
        }
        // operation log
        if (Objects.nonNull(obj)) {
            logRecordService.recordOperation(obj,
                    LogRecordOptions.builder()
                            .pageOperateType(PageOperateType.IMPORT)
                            .operationType(OperationTypeEnum.GPS_CONFIG_IMPORT.getCode())
                            .remark("导入GPS")
                            .build());
        }
        return failImportList;
    }

    /**
     * gps批量查询
     */
    public List<AttendanceGpsConfigDTO> selectList(AttendanceGpsConfigQuery query) {
        List<AttendanceGpsConfigDO> list = attendanceGpsConfigDao.list(query);
        return AttendanceGpsConfigMapstruct.INSTANCE.toPunchGpsConfigDTO(list);
    }

    /**
     * gps筛选条件查询(国家和城市)
     */
    public List<String> selectFilterList(AttendanceConfigFilterQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getType())) {
            return Collections.emptyList();
        }
        return attendanceGpsConfigDao.selectFilterList(query);
    }

    /**
     * 导出GPS
     */
    public PaginationResult<AttendanceGpsConfigExportDTO> export(AttendanceGpsConfigQuery query) {
        PaginationResult<AttendanceGpsConfigDTO> pageList = this.list(query);
        PaginationResult<AttendanceGpsConfigExportDTO> pageResult = AttendanceGpsConfigMapstruct.INSTANCE.toPageDTO(pageList);
        if (CollectionUtils.isEmpty(pageList.getResults())) {
            return pageResult;
        }
        //operation log
        AttendanceGpsConfigDO punchGpsConfigDO = AttendanceGpsConfigMapstruct.INSTANCE.toPunchGpsConfigDO(pageList.getResults().get(0));
        logRecordService.recordOperation(punchGpsConfigDO,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.EXPORT)
                        .operationType(OperationTypeEnum.GPS_CONFIG_EXPORT.getCode())
                        .remark("导出GPS")
                        .build());
        return pageResult;
    }


    private List<AttendanceGpsConfigImportDTO> checkImportParam(List<AttendanceGpsConfigImportDTO> params,
                                                                List<AttendanceGpsConfigImportDTO> failImportList) {
        List<AttendanceGpsConfigImportDTO> normal = new ArrayList<>();
        // 获取系统国家
        List<String> importCountryNames = params.stream()
                .map(AttendanceGpsConfigImportDTO::getCountry)
                .collect(Collectors.toList());
        List<String> countryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(importCountryNames)) {
            CountryApiQuery countryQuery = new CountryApiQuery();
            countryQuery.setCountryNames(importCountryNames);
            countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
            List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
            if (CollectionUtils.isNotEmpty(countryConfigList)) {
                countryList = countryConfigList.stream()
                        .map(CountryConfigDTO::getCountryName)
                        .collect(Collectors.toList());
            }
        }
        for (AttendanceGpsConfigImportDTO param : params) {
            if (StringUtils.isBlank(param.getCountry()) || StringUtils.isBlank(param.getLocationCity())
                    || StringUtils.isBlank(param.getAddressName()) || StringUtils.isBlank(param.getAddressDetail())
                    || Objects.isNull(param.getEffectiveRange()) || Objects.isNull(param.getLongitude()) || Objects.isNull(param.getLatitude())) {
                IpepUtils.putFail(param, RequestInfoHolder.isChinese() ?
                        "必填项不能为空" :
                        "The required fields cannot be blank");
                failImportList.add(param);
                continue;
            }

            if (!countryList.contains(param.getCountry())) {
                IpepUtils.putFail(param, ErrorCodeEnum.COUNTRY_CODE_NOT_EXISTS.getMessage());
                failImportList.add(param);
                continue;
            }

            AttendanceGpsConfigQuery query = AttendanceGpsConfigQuery.builder()
                    .country(param.getCountry())
                    .longitude(param.getLongitude())
                    .latitude(param.getLatitude())
                    .build();
            List<AttendanceGpsConfigDO> list = attendanceGpsConfigDao.list(query);
            if (CollectionUtils.isNotEmpty(list)) {
                IpepUtils.putFail(param, ErrorCodeEnum.GPS_ADDRESS_DUPLICATE_ERROR.getMessage());
                failImportList.add(param);
                continue;
            }
            normal.add(param);
        }
        return normal;
    }

}
