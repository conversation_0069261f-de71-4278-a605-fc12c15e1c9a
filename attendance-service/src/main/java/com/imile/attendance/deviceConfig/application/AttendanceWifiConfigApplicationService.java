package com.imile.attendance.deviceConfig.application;

import com.imile.attendance.deviceConfig.AttendanceWifiConfigService;
import com.imile.attendance.deviceConfig.MobilePunchQueryService;
import com.imile.attendance.deviceConfig.command.AttendanceWifiConfigAddCommand;
import com.imile.attendance.deviceConfig.command.AttendanceWifiConfigDeleteCommand;
import com.imile.attendance.deviceConfig.command.AttendanceWifiConfigUpdateCommand;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigExportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigImportDTO;
import com.imile.attendance.deviceConfig.factory.AttendanceWifiConfigFactory;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceConfigFilterQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;
import com.imile.common.page.PaginationResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
@Service
public class AttendanceWifiConfigApplicationService {

    @Resource
    private AttendanceWifiConfigFactory attendanceWifiConfigFactory;
    @Resource
    private AttendanceWifiConfigService attendanceWifiConfigService;
    @Resource
    private MobilePunchQueryService mobilePunchQueryService;


    public PaginationResult<AttendanceWifiConfigDTO> list(AttendanceWifiConfigQuery query) {
        return attendanceWifiConfigService.list(query);
    }

    public List<AttendanceWifiConfigDTO> selectList(AttendanceWifiConfigQuery query) {
        return attendanceWifiConfigService.selectList(query);
    }

    public List<String> selectFilterList(AttendanceConfigFilterQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getType())) {
            return Collections.emptyList();
        }
        return attendanceWifiConfigService.selectFilterList(query);
    }

    public void add(AttendanceWifiConfigAddCommand addCommand) {
        attendanceWifiConfigFactory.add(addCommand);
    }

    public AttendanceWifiConfigDTO update(AttendanceWifiConfigUpdateCommand updateCommand) {
        return attendanceWifiConfigFactory.update(updateCommand);
    }

    public AttendanceWifiConfigDTO delete(AttendanceWifiConfigDeleteCommand deleteCommand) {
        return attendanceWifiConfigFactory.delete(deleteCommand);
    }

    public AttendanceWifiConfigDTO detail(AttendanceWifiConfigQuery query) {
        return attendanceWifiConfigService.detail(query);
    }

    public List<AttendanceWifiConfigImportDTO> importWifiConfig(List<AttendanceWifiConfigImportDTO> param) {
        return attendanceWifiConfigService.importWifiConfig(param);
    }

    public PaginationResult<AttendanceWifiConfigExportDTO> export(AttendanceWifiConfigQuery query) {
        return attendanceWifiConfigService.export(query);
    }

    /**
     * 获取所有的wifi配置
     */
    public List<AttendanceWifiConfigDTO> getAllWifiConfig(){
        return mobilePunchQueryService.getAllWifiConfig();
    }


}
