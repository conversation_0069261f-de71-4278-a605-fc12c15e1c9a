package com.imile.attendance.deviceConfig.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/18
 * @Description
 */
@Data
public class AttendanceMobileHistoryConfigDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 手机唯一标识
     */
    private String mobileUnicode;

    /**
     * 手机型号
     */
    private String mobileModel;

    /**
     * 手机品牌
     */
    private String mobileBranch;

    /**
     * 手机系统版本
     */
    private String mobileVersion;

    /**
     * 绑定时间
     */
    private Date createDate;

    /**
     * 解绑时间
     */
    private Date lastUpdDate;

    /**
     * 操作人
     */
    private String lastUpdUserName;
}
