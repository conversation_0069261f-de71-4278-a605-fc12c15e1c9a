package com.imile.attendance.deviceConfig.application;

import com.imile.attendance.deviceConfig.AttendanceMobileConfigService;
import com.imile.attendance.deviceConfig.MobilePunchQueryService;
import com.imile.attendance.deviceConfig.command.AttendanceMobileConfigAddCommand;
import com.imile.attendance.deviceConfig.command.AttendanceMobileConfigDeleteCommand;
import com.imile.attendance.deviceConfig.dto.AttendanceMobileConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceMobileHistoryConfigDTO;
import com.imile.attendance.deviceConfig.factory.AttendanceMobileConfigFactory;
import com.imile.attendance.infrastructure.repository.deviceConfig.dto.AttendanceMobileConfigListDTO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigListQuery;
import com.imile.common.page.PaginationResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/18
 * @Description
 */
@Service
public class AttendanceMobileConfigApplicationService {

    @Resource
    private AttendanceMobileConfigFactory attendanceMobileConfigFactory;
    @Resource
    private AttendanceMobileConfigService attendanceMobileConfigService;
    @Resource
    private MobilePunchQueryService mobilePunchQueryService;


    public PaginationResult<AttendanceMobileConfigListDTO> list(AttendanceMobileConfigListQuery query) {
        return attendanceMobileConfigService.list(query);
    }

    public void add(AttendanceMobileConfigAddCommand addCommand) {
        attendanceMobileConfigFactory.add(addCommand);
    }

    public void unbind(AttendanceMobileConfigDeleteCommand deleteCommand) {
        attendanceMobileConfigFactory.delete(deleteCommand);
    }

    /**
     * 用户考勤手机查询(优化打卡用)
     */
    public List<AttendanceMobileConfigDTO> selectByUserCodeForPunch(String userCode) {
        return mobilePunchQueryService.selectByUserCodeForPunch(userCode);
    }

    /**
     * 用户考勤手机历史记录查询
     */
    public List<AttendanceMobileHistoryConfigDTO> selectHistoryByUserCode(String userCode) {
        return mobilePunchQueryService.selectHistoryByUserCode(userCode);
    }

    /**
     * 用户考勤手机查询(含打卡时间)
     */
    public List<AttendanceMobileConfigDTO> selectByUserCodeForDetail(String userCode) {
        return mobilePunchQueryService.selectByUserCodeForDetail(userCode);
    }




}
