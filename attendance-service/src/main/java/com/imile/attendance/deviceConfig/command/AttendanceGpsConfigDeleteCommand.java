package com.imile.attendance.deviceConfig.command;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceGpsConfigDeleteCommand {

    private Long gpsConfigId;


    public static AttendanceGpsConfigDeleteCommand of(Long gpsConfigId) {
        return new AttendanceGpsConfigDeleteCommand(gpsConfigId);
    }
}
