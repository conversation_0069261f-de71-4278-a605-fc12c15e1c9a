package com.imile.attendance.deviceConfig.mapstruct;


import com.imile.attendance.deviceConfig.command.AttendanceGpsConfigAddCommand;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigExportDTO;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceGpsConfigDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.page.PaginationResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface AttendanceGpsConfigMapstruct {

    AttendanceGpsConfigMapstruct INSTANCE = Mappers.getMapper(AttendanceGpsConfigMapstruct.class);


    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    AttendanceGpsConfigDO mapToPunchGpsConfigDO(AttendanceGpsConfigAddCommand addCommand);


    default AttendanceGpsConfigDO toPunchGpsConfigDO(AttendanceGpsConfigAddCommand addCommand) {
        AttendanceGpsConfigDO gpsConfigDO = mapToPunchGpsConfigDO(addCommand);
        BaseDOUtil.fillDOInsert(gpsConfigDO);
        return gpsConfigDO;
    }

    AttendanceGpsConfigDTO toPunchGpsConfigDTO(AttendanceGpsConfigDO gpsConfigDO);

    AttendanceGpsConfigDO toPunchGpsConfigDO(AttendanceGpsConfigDTO gpsConfigDTO);

    List<AttendanceGpsConfigDTO> toPunchGpsConfigDTO(List<AttendanceGpsConfigDO> gpsConfigDO);

    AttendanceGpsConfigExportDTO toPageDTO(AttendanceGpsConfigDTO gpsConfigDTO);

    List<AttendanceGpsConfigExportDTO> toPageDTO(List<AttendanceGpsConfigDTO> gpsConfigDTO);

    default PaginationResult<AttendanceGpsConfigExportDTO> toPageDTO(PaginationResult<AttendanceGpsConfigDTO> gpsConfigDTOList) {
        PaginationResult<AttendanceGpsConfigExportDTO> result = new PaginationResult<>();
        result.setPagination(gpsConfigDTOList.getPagination());
        List<AttendanceGpsConfigExportDTO> results = new ArrayList<>();
        for (AttendanceGpsConfigDTO dto : gpsConfigDTOList.getResults()) {
            AttendanceGpsConfigExportDTO exportDTO = toPageDTO(dto);
            exportDTO.setCreateDate(DateHelper.formatYYYYMMDDHHMMSS(dto.getCreateDate()));
            exportDTO.setLastUpdDate(DateHelper.formatYYYYMMDDHHMMSS(dto.getLastUpdDate()));
            results.add(exportDTO);
        }
        result.setResults(results);
        return result;
    }

    // deep copy
    AttendanceGpsConfigDO deepCopy(AttendanceGpsConfigDO gpsConfigDO);
}
