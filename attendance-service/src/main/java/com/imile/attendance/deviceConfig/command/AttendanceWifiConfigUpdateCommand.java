package com.imile.attendance.deviceConfig.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AttendanceWifiConfigUpdateCommand extends AttendanceWifiConfigAddCommand {

    /**
     * 主键id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;
}
