package com.imile.attendance.deviceConfig.mapstruct;


import com.imile.attendance.deviceConfig.command.AttendanceWifiConfigAddCommand;
import com.imile.attendance.deviceConfig.command.AttendanceWifiConfigUpdateCommand;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigExportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigExportDTO;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceWifiConfigDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.page.PaginationResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface AttendanceWifiConfigMapstruct {

    AttendanceWifiConfigMapstruct INSTANCE = Mappers.getMapper(AttendanceWifiConfigMapstruct.class);

    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    AttendanceWifiConfigDO toAttendanceWifiConfigDO(AttendanceWifiConfigAddCommand addCommand);


    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    @Mapping(target = "id", source = "id")
    AttendanceWifiConfigDO toAttendanceWifiConfigDO(AttendanceWifiConfigUpdateCommand updateCommand);

    default AttendanceWifiConfigDO toAddAttendanceWifiConfigDO(AttendanceWifiConfigAddCommand addCommand) {
        AttendanceWifiConfigDO AttendanceWifiConfigDO = toAttendanceWifiConfigDO(addCommand);
        BaseDOUtil.fillDOInsert(AttendanceWifiConfigDO);
        return AttendanceWifiConfigDO;
    }

    default AttendanceWifiConfigDO toUpdateAttendanceWifiConfigDO(AttendanceWifiConfigUpdateCommand updateCommand) {
        AttendanceWifiConfigDO AttendanceWifiConfigDO = toAttendanceWifiConfigDO(updateCommand);
        BaseDOUtil.fillDOUpdate(AttendanceWifiConfigDO);
        return AttendanceWifiConfigDO;
    }

    AttendanceWifiConfigDO toAttendanceWifiConfigDO(AttendanceWifiConfigDTO wifiConfigDTO);

    AttendanceWifiConfigDTO toAttendanceWifiConfigDTO(AttendanceWifiConfigDO attendanceWifiConfigDO);

    List<AttendanceWifiConfigDTO> toAttendanceWifiConfigDTO(List<AttendanceWifiConfigDO> attendanceWifiConfigDOList);

    AttendanceWifiConfigExportDTO toPageDTO(AttendanceWifiConfigDTO wifiConfigDTO);

    List<AttendanceWifiConfigExportDTO> toPageDTO(List<AttendanceWifiConfigDTO> wifiConfigDTO);

    default PaginationResult<AttendanceWifiConfigExportDTO> toPageDTO(PaginationResult<AttendanceWifiConfigDTO> wifiConfigDTOList) {
        PaginationResult<AttendanceWifiConfigExportDTO> result = new PaginationResult<>();
        result.setPagination(wifiConfigDTOList.getPagination());
        List<AttendanceWifiConfigExportDTO> results = new ArrayList<>();
        for (AttendanceWifiConfigDTO dto : wifiConfigDTOList.getResults()) {
            AttendanceWifiConfigExportDTO exportDTO = toPageDTO(dto);
            exportDTO.setCreateDate(DateHelper.formatYYYYMMDDHHMMSS(dto.getCreateDate()));
            exportDTO.setLastUpdDate(DateHelper.formatYYYYMMDDHHMMSS(dto.getLastUpdDate()));
            results.add(exportDTO);
        }
        result.setResults(results);
        return result;
    }

    // deep copy
    AttendanceWifiConfigDO deepCopy(AttendanceWifiConfigDO wifiConfigDO);
}
