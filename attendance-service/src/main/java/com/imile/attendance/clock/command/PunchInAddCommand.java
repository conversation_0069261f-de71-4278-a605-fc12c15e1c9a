package com.imile.attendance.clock.command;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.util.AesUtil;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/20 
 * @Description
 */
@Data
public class PunchInAddCommand {

    /**
     * 打卡规则id
     */
    private Long configId;

    /**
     * 班次id
     */
    private Long classId;

    /**
     * 班次详情id
     */
    private Long classItemId;

    /**
     * 手机设备id
     */
    private Long mobileConfigId;

    /**
     * wifi配置id
     */
    private Long wifiConfigId;

    /**
     * wifi名称
     */
    private String wifiConfigName;

    /**
     * wifi城市
     */
    private String wifiConfigCity;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * gps配置id
     */
    private Long gpsConfigId;

    /**
     * gps地址名称
     */
    private String gpsConfigName;

    /**
     * gps城市
     */
    private String gpsConfigCity;

    /**
     * 手机唯一标识
     */
    private String mobileUnicode;

    /**
     * 手机型号
     */
    private String mobileModel;

    /**
     * 手机品牌
     */
    private String mobileBranch;

    /**
     * 手机系统版本
     */
    private String mobileVersion;

    /**
     * 考勤日(没安排考勤日,则默认当天)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long dayId;

    /**
     * 用户ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    private Date dateTime;


    /**
     * 当前请求时间戳
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long currentTimeStamp;


    public static void main(String[] args) throws Exception {
        String str = new JSONObject()
                .fluentPut("configId", 12345L)
                .fluentPut("wifiConfigCity", "hangzhou")
                .fluentPut("longitude", BigDecimal.valueOf(120.0))
                .fluentPut("dateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .fluentPut("currentTimeStamp", Instant.now().toEpochMilli())
                .toJSONString();
        for (int i = 0; i < 20; i++) {
            String IV_STRING = AesUtil.genBase64IV();
            System.out.println("ivr:"+IV_STRING);
            String encrypt = AesUtil.encrypt(str, AesUtil.SECRET_KEY_STRING, IV_STRING);
            System.out.println(encrypt);
            String decrypt = AesUtil.decrypt(encrypt, AesUtil.SECRET_KEY_STRING, IV_STRING);
            System.out.println(decrypt);
            PunchInAddCommand param = JSONObject.parseObject(decrypt, PunchInAddCommand.class);
            System.out.println(param);
            System.out.println(" ");
        }
    }
}
