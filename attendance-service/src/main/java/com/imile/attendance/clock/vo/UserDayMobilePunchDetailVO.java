package com.imile.attendance.clock.vo;

import com.imile.attendance.base.Operator;
import com.imile.attendance.clock.bo.UserPunchMissingRuleType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/23
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDayMobilePunchDetailVO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户常驻国
     */
    private String locationCountry;

    /**
     * 考勤日
     */
    private Long dayId;

    /**
     * 员工手机打卡等规则配置
     */
    private UserMobileRuleConfigVO ruleConfigVO;

    /**
     * 班次配置
     */
    private PunchClassConfigVO punchClassConfigVO;

    /**
     * 班次详情配置
     */
    private List<PunchClassItemConfigVO> punchClassItemConfigVO;

    /**
     * 打卡记录
     */
    private List<MobilePunchCardRecordVO> punchCardRecordVO;

    /**
     * 请假/外勤记录
     */
    private List<MobileFormVO> mobileFormVOList;

    /**
     * 考勤异常类型(枚举值)
     */
    private List<MobileAbnormalVO> abnormalPunchList;

    /**
     * 用户打卡缺失规则类型
     */
    private UserPunchMissingRuleType userPunchMissingRuleType;

    /**
     * 用户上级部门hr列表
     */
    private List<Operator> superiorDeptHrList;
}
