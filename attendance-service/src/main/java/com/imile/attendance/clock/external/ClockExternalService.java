package com.imile.attendance.clock.external;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19 
 * @Description
 */
@Slf4j
@Service
public class ClockExternalService {

    private static final String GEONAMES_API_HOST = "http://api.geonames.org/timezoneJSON?username=shengyomi";

    /**
     * 通过经纬度获取时间
     */
    public Date getTimeZone(String lat, String lng) {
        // 设置参数
        JSONObject paramObj = new JSONObject();
        paramObj.put("lat", lat);
        paramObj.put("lng", lng);
        Date time;
        try {
            String resultStr = HttpUtil.get(GEONAMES_API_HOST, paramObj);
            // 结果解析
            JSONObject resultObj = JSON.parseObject(resultStr);
            time = resultObj.getDate("time");
        } catch (Exception e) {
            log.warn("getTimeZone from lat={},lng={} fail", lat, lat, e);
            time = null;
        }
        return time;
    }
}
