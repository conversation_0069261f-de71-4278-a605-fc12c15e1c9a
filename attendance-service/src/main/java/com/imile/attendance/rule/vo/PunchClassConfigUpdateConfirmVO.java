package com.imile.attendance.rule.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigUpdateConfirmVO implements Serializable {

    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 时段信息变更
     */
    private Boolean itemInfoUpdate = Boolean.FALSE;

    /**
     * 适用范围变更
     */
    private Boolean rangeUpdate = Boolean.FALSE;

    /**
     * 已应用排班员工数量
     */
    private Integer employeeSchedulingCount = 0;

    /**
     * 移除适用范围的员工数量
     */
    private Integer removeRangeEmployeeCount = 0;

    /**
     * 新增加适用范围的员工数量
     */
    private Integer addRangeEmployeeCount = 0;

    /**
     * 变更前适用范围的员工数量
     */
    private Integer rangeEmployeeCount = 0;

    /**
     * 班次新增返回结构
     */
    private PunchClassConfigAddVO punchClassConfigAddVO;
}
