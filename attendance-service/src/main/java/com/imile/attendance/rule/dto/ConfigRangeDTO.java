package com.imile.attendance.rule.dto;

import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import lombok.Data;

import java.util.function.Function;

/**
 * <AUTHOR> chen
 * @Date 2025/4/10 
 * @Description
 */
@Data
public class ConfigRangeDTO {

    /**
     * 范围类型 DEPT,USER,COUNTRY
     */
    private String rangeType;

    /**
     * 业务ID
     */
    private Long bizId;

    /**
     * 业务名称
     */
    private String bizNameByLang;

    public static ConfigRangeDTO buildCountryRangeDTO(String country) {
        ConfigRangeDTO rangeDTO = new ConfigRangeDTO();
        rangeDTO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
        rangeDTO.setBizId(null);
        rangeDTO.setBizNameByLang(country);
        return rangeDTO;
    }

    public static <T> ConfigRangeDTO buildUserRangeDTO(AttendanceUser attendanceUser) {
        return getPunchConfigRangeDTO(
                RuleRangeTypeEnum.USER,
                t -> attendanceUser.getId(),
                t -> RequestInfoHolder.isChinese() ? attendanceUser.getUserName() : attendanceUser.getUserNameEn(),
                attendanceUser
        );
    }

    public static <T> ConfigRangeDTO buildDeptRangeDTO(AttendanceDept attendanceDept) {
        return getPunchConfigRangeDTO(
                RuleRangeTypeEnum.DEPT,
                t -> attendanceDept.getId(),
                t -> RequestInfoHolder.isChinese() ? attendanceDept.getDeptNameCn() : attendanceDept.getDeptNameEn(),
                attendanceDept);
    }


    private static <T> ConfigRangeDTO getPunchConfigRangeDTO(RuleRangeTypeEnum rangeType,
                                                             Function<T, Long> idGetter,
                                                             Function<T, String> nameGetter,
                                                             T record) {
        ConfigRangeDTO rangeDTO = new ConfigRangeDTO();
        rangeDTO.setBizId(idGetter.apply(record));
        rangeDTO.setBizNameByLang(nameGetter.apply(record));
        rangeDTO.setRangeType(rangeType.getCode());
        return rangeDTO;
    }
}
