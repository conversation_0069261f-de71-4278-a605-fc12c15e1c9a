package com.imile.attendance.rule.mapstruct;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.rule.bo.OverTimeConfig;
import com.imile.attendance.rule.dto.OverTimeConfigDetailDTO;
import com.imile.attendance.rule.dto.OverTimeConfigPageDTO;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface OverTimeConfigMapstruct {

    OverTimeConfigMapstruct INSTANCE = Mappers.getMapper(OverTimeConfigMapstruct.class);

    default OverTimeConfigDO toModel(OverTimeConfig overTimeConfig) {
        OverTimeConfigDO overTimeConfigDO = new OverTimeConfigDO();
        overTimeConfigDO.setId(overTimeConfig.getId());
        overTimeConfigDO.setCountry(overTimeConfig.getCountry());
        overTimeConfigDO.setConfigNo(overTimeConfig.getConfigNo());
        overTimeConfigDO.setConfigName(overTimeConfig.getConfigName());
        overTimeConfigDO.setOvertimeConfig(JSONObject.toJSONString(overTimeConfig.getOverTime()));
        overTimeConfigDO.setIsCountryLevel(overTimeConfig.getIsCountryLevel());
        overTimeConfigDO.setStatus(overTimeConfig.getStatus());
        overTimeConfigDO.setDeptIds(overTimeConfig.getDeptIds());
        overTimeConfigDO.setIsLatest(overTimeConfig.getIsLatest());
        overTimeConfigDO.setEffectTime(overTimeConfig.getEffectTime());
        overTimeConfigDO.setExpireTime(overTimeConfig.getExpireTime());
        overTimeConfigDO.setEffectTimestamp(overTimeConfig.getEffectTimestamp());
        overTimeConfigDO.setExpireTimestamp(overTimeConfig.getExpireTimestamp());
        overTimeConfigDO.setWorkingOutStartTime(overTimeConfig.getWorkingOutStartTime());
        overTimeConfigDO.setWorkingEffectiveTime(overTimeConfig.getWorkingEffectiveTime());
        overTimeConfigDO.setWorkingSubsidyType(overTimeConfig.getWorkingSubsidyType());
        overTimeConfigDO.setRestEffectiveTime(overTimeConfig.getRestEffectiveTime());
        overTimeConfigDO.setRestSubsidyType(overTimeConfig.getRestSubsidyType());
        overTimeConfigDO.setHolidayEffectiveTime(overTimeConfig.getHolidayEffectiveTime());
        overTimeConfigDO.setHolidaySubsidyType(overTimeConfig.getHolidaySubsidyType());
        return overTimeConfigDO;
    }

    default OverTimeConfigDetailDTO toDetailDTO(OverTimeConfigDO overTimeConfigDO) {
        OverTimeConfigDetailDTO detailDTO = new OverTimeConfigDetailDTO();
        detailDTO.setId(overTimeConfigDO.getId());
        detailDTO.setConfigName(overTimeConfigDO.getConfigName());
        detailDTO.setConfigNo(overTimeConfigDO.getConfigNo());
        detailDTO.setIsCountryLevel(overTimeConfigDO.getIsCountryLevel());
        detailDTO.setCountry(overTimeConfigDO.getCountry());
        detailDTO.setWorkingOutStartTime(overTimeConfigDO.getWorkingOutStartTime());
        detailDTO.setWorkingEffectiveTime(overTimeConfigDO.getWorkingEffectiveTime());
        detailDTO.setWorkingSubsidyType(overTimeConfigDO.getWorkingSubsidyType());
        detailDTO.setRestEffectiveTime(overTimeConfigDO.getRestEffectiveTime());
        detailDTO.setRestSubsidyType(overTimeConfigDO.getRestSubsidyType());
        detailDTO.setHolidayEffectiveTime(overTimeConfigDO.getHolidayEffectiveTime());
        detailDTO.setHolidaySubsidyType(overTimeConfigDO.getHolidaySubsidyType());
        return detailDTO;
    }

    @Mapping(target = "rangeRecords", ignore = true)
    @Mapping(target = "employeeCount", ignore = true)
    OverTimeConfigPageDTO toPageDTO(OverTimeConfigDO overTimeConfigDO);
}
