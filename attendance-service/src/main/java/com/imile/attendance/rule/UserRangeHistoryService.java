package com.imile.attendance.rule;

import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.rule.dto.UserRangeHistoryDTO;
import com.imile.attendance.rule.mapstruct.PunchConfigMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/11
 * @Description 用户打卡规则历史服务
 */
@Slf4j
@Service
public class UserRangeHistoryService {

    @Resource
    private PunchConfigDao punchConfigDao;

    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;


    /**
     * 查询用户的打卡规则历史记录
     *
     * @param userId 用户ID，不能为空
     * @return 用户打卡规则历史记录列表
     */
    public List<UserRangeHistoryDTO> listUserRangeHistory(Long userId) {
        log.info("listUserRangeHistory | userId:{}", userId);
        // 参数校验，如果用户ID为空则返回空列表
        if (null == userId) {
            return Collections.emptyList();
        }

        // 查询用户关联的所有打卡规则范围记录
        List<PunchConfigRangeDO> configRangeDOList = punchConfigRangeDao.listAllRangeByUserIds(Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(configRangeDOList)) {
            return Collections.emptyList();
        }

        // 提取规则配置ID列表
        List<Long> ruleConfigIdList = configRangeDOList.stream()
                .map(PunchConfigRangeDO::getRuleConfigId)
                .collect(Collectors.toList());

        // 查询打卡规则配置信息
        List<PunchConfigDO> punchConfigDOList = punchConfigDao.listByConfigIds(ruleConfigIdList);

        return PunchConfigMapstruct.INSTANCE.toUserRangeHistoryDTOList(punchConfigDOList, configRangeDOList);
    }
}
