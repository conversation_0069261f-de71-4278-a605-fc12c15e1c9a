package com.imile.attendance.rule.dto;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> chen
 * @Date 2025/4/11 
 * @Description 用户规则范围历史记录DTO
 */
@Data
public class UserRangeHistoryDTO {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 规则编码
     */
    private String ruleNo;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则绑定开始日期
     */
    private String ruleBindStartDate;

    /**
     * 规则绑定结束日期
     */
    private String ruleBindEndDate;

    /**
     * 规则绑定日期格式
     */
    private String ruleBindDateFormat;

    /**
     * 操作人用户名
     */
    private String operateUserName;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 生成规则绑定日期格式
     * @return
     */
    public String generateRuleBindDateFormat() {
        String startDate = StringUtils.isEmpty(ruleBindStartDate) ? "--" : ruleBindStartDate;
        String endDate = StringUtils.isEmpty(ruleBindEndDate) ? "--" : ruleBindEndDate;

        if (StringUtils.equals(endDate, BusinessConstant.DEFAULT_END_TIME_STRING)) {
            endDate = RequestInfoHolder.isChinese() ? "至今" : "Forever";
        }

        return startDate + " ~ " + endDate;
    }


}
