package com.imile.attendance.rule.dto;

import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigAddDTO implements Serializable {

    /**
     * 班次ID
     */
    private Long id;

    /**
     * 班次性质（FIXED_CLASS,MULTIPLE_CLASS）
     */
    private String classNature;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次类型
     */
    private Integer classType;

    /**
     * 时段数
     */
    private Integer itemNum;

    /**
     * 法定工作时长（不包含休息时间）
     */
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    private BigDecimal attendanceHours;

    /**
     * 国家
     */
    private String country;

    /**
     * 适用部门
     */
    private List<Long> deptIds;

    /**
     * 适用用户
     */
    private List<Long> userIdList;

    /**
     * 自动更新
     */
    private Boolean autoScheduling = Boolean.FALSE;

    /**
     * 自动清空
     */
    private Boolean clearSchedule = Boolean.FALSE;


    /**
     * 班次时段信息
     */
    private List<PunchClassItemConfigDTO> classItemConfigList;

    /**
     * 判断是否国家级适用范围
     */
    public boolean countryLevel() {
        return CollectionUtils.isEmpty(getUserIdList()) && CollectionUtils.isEmpty(getDeptIds());
    }

}
