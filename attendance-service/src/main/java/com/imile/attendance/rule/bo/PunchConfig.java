package com.imile.attendance.rule.bo;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.imile.attendance.dto.DateAndTimeZoneDate;
import org.apache.commons.lang3.StringUtils;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7 
 * @Description
 */
@Setter
@Getter
public class PunchConfig {

    public static final List<BigDecimal> allowedTimeInterval = Arrays.asList(
            new BigDecimal("1"), new BigDecimal("1.5"), new BigDecimal("2"), new BigDecimal("2.5"),
            new BigDecimal("3"), new BigDecimal("3.5"), new BigDecimal("4"), new BigDecimal("4.5"),
            new BigDecimal("5"), new BigDecimal("5.5"), new BigDecimal("6"), new BigDecimal("6.5"),
            new BigDecimal("7"), new BigDecimal("7.5"), new BigDecimal("8"), new BigDecimal("8.5"),
            new BigDecimal("9"), new BigDecimal("9.5"), new BigDecimal("10"), new BigDecimal("10.5"),
            new BigDecimal("11"), new BigDecimal("11.5"), new BigDecimal("12")
    );

    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 打卡规则编码
     */
    private String configNo;

    /**
     * 打卡规则名称
     */
    private String configName;

    /**
     * 打卡规则类型：1.免打卡 2.班次固定打卡 3.灵活打卡一次 4.灵活打卡两次
     */
    private String configType;

    /**
     * 上下班打卡时间间隔 单位：小时（类型为灵活打卡两次时需设置）
     */
    private BigDecimal punchTimeInterval;

    /**
     * 是否国家级打卡规则
     */
    private Integer isCountryLevel;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 适用部门
     */
    private String deptIds;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 生效时间戳
     */
    private Long effectTimestamp;

    /**
     * 失效时间戳
     */
    private Long expireTimestamp;


    private Date createDate;

    private String createUserCode;

    private String createUserName;

    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;

    /**
     * 是否删除
     */
    private Integer isDelete;

    @Builder
    public PunchConfig(Long id, String country, String configNo, String configName,
                       String configType, BigDecimal punchTimeInterval, Integer isCountryLevel, String status,
                       String deptIds, Integer isLatest, DateAndTimeZoneDate effectDateAndTimeZoneDate, DateAndTimeZoneDate expireDateAndTimeZoneDate,
                       Date createDate, String createUserCode, String createUserName, Date lastUpdDate,
                       String lastUpdUserCode, String lastUpdUserName, Integer isDelete) {
        this.id = id;
        setCountry(country);
        this.configNo = configNo;
        setConfigName(configName);
        setConfigType(configType);
        setPunchTimeInterval(punchTimeInterval);
        this.isCountryLevel = isCountryLevel;
        this.status = status;
        this.deptIds = deptIds;
        this.isLatest = isLatest;
        setEffectTime(effectDateAndTimeZoneDate);
        setExpireTime(expireDateAndTimeZoneDate);
        this.createDate = createDate;
        this.createUserCode = createUserCode;
        this.createUserName = createUserName;
        this.lastUpdDate = lastUpdDate;
        this.lastUpdUserCode = lastUpdUserCode;
        this.lastUpdUserName = lastUpdUserName;
        this.isDelete = isDelete;
    }


    public void setCountry(String country) {
        if (StringUtils.isEmpty(country)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "country can not be null");
        }
        this.country = country;
    }

    public void setConfigName(String configName) {
        if (StringUtils.isEmpty(configName)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "configName can not be null");
        }
        if (StringUtils.length(configName) > 100) {
            //todo
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "configName length can not be more than 100");
        }
        this.configName = configName;
    }

    public void setConfigType(String configType) {
        if (StringUtils.isEmpty(configType)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "configType can not be null");
        }
        PunchConfigTypeEnum punchConfigTypeEnum = PunchConfigTypeEnum.getInstance(configType);
        if (null == punchConfigTypeEnum) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "configType=" + configType + " not support");
        }
        this.configType = configType;
    }

    public void setPunchTimeInterval(BigDecimal punchTimeInterval) {
        //仅打卡规则类型=灵活打卡两次时，才需要上下班打卡间隔时长
        if (StringUtils.equals(this.getConfigType(), PunchConfigTypeEnum.FLEXIBLE_WORK_TWICE.getCode())) {
            if (null == punchTimeInterval) {
                throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                        "punchTimeInterval can not be null");
            }
            //必须是1、1.5、2、2.5、3、3.5、4、4.5、5、5.5、6、6.5、7、7.5、8、8.5、9、9.5、10、10.5、11、11.5、12
            if (!allowedTimeInterval.contains(punchTimeInterval)) {
                throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                        "punchTimeInterval=" + punchTimeInterval + " not support");
            }
            this.punchTimeInterval = punchTimeInterval;
        }
    }

    public void setEffectTime(DateAndTimeZoneDate effectDateAndTimeZoneDate) {
        if (null == effectDateAndTimeZoneDate) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "effectTime can not be null");
        }
        this.effectTime = effectDateAndTimeZoneDate.getTimeZoneDate();
        this.effectTimestamp = effectDateAndTimeZoneDate.getDateTimeStamp();
    }

    public void setExpireTime(DateAndTimeZoneDate expireDateAndTimeZoneDate) {
        if (null == expireDateAndTimeZoneDate) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "expireTime can not be null");
        }
        this.expireTime = expireDateAndTimeZoneDate.getTimeZoneDate();
        this.expireTimestamp = expireDateAndTimeZoneDate.getDateTimeStamp();
    }

}
