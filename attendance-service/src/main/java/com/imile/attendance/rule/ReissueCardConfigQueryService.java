package com.imile.attendance.rule;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.mapstruct.CommonMapstruct;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.rule.bo.ReissueCardConfigBO;
import com.imile.attendance.rule.dto.ConfigRangeDTO;
import com.imile.attendance.rule.dto.ReissueCardConfigDetailDTO;
import com.imile.attendance.rule.dto.RuleConfigApplyUserCountDTO;
import com.imile.attendance.rule.dto.RuleConfigApplyUserDTO;
import com.imile.attendance.rule.mapstruct.ReissueCardConfigMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description 补卡规则查询服务
 */
@Slf4j
@Component
public class ReissueCardConfigQueryService {

    @Resource
    private ReissueCardConfigDao reissueCardConfigDao;
    @Resource
    private ReissueCardConfigRangeDao reissueCardConfigRangeDao;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;

    /**
     * 查询补卡配置的适用用户列表
     * 支持三种场景：
     * 1. 国家级别配置：返回该国家下所有在职非司机且未配置规则的用户
     * 2. 部门级别配置：返回配置的部门下的所有在职用户
     * 3. 用户级别配置：返回直接配置的在职用户列表
     *
     * @param configNo 补卡配置编号
     * @return 适用用户列表，包含：
     *         - userLevelUserList: 用户级别配置的用户列表
     *         - deptLevelUserList: 部门级别配置的用户列表
     *         - countryLevelUserList: 国家级别配置的用户列表
     * @throws BusinessLogicException 当配置不存在时抛出异常
     */
    public RuleConfigApplyUserDTO queryApplyUser(String configNo) {
        // 获取打卡配置和范围
        ReissueCardConfigBO configBO = reissueCardConfigManage.getConfigBO(configNo);
        if (null == configBO) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        RuleConfigApplyUserDTO ruleConfigApplyUserDTO = RuleConfigApplyUserDTO.init();
        List<Long> countryRangeIds = configBO.queryCountryRangeIds();
        if (CollectionUtils.isNotEmpty(countryRangeIds)) {
            ruleConfigApplyUserDTO.setUserLevelUserList(userService.listOnJobUsers(countryRangeIds));
        }
        List<Long> userRangeIds = configBO.queryUserRangeIds();
        if (CollectionUtils.isNotEmpty(userRangeIds)) {
            ruleConfigApplyUserDTO.setUserLevelUserList(userService.listOnJobUsers(userRangeIds));
        }
        List<Long> deptRangeIds = configBO.queryDeptRangeIds();
        if (CollectionUtils.isNotEmpty(deptRangeIds)) {
            ruleConfigApplyUserDTO.setDeptLevelUserList(userService.listOnJobUsers(deptRangeIds));
        }
        return ruleConfigApplyUserDTO;
    }

    /**
     * 查询打卡规则适用用户数量统计
     * 根据配置编号统计不同级别下的适用用户数量
     *
     * 支持三种统计场景：
     * 1. 国家级别：统计指定国家下所有在职非司机且未配置规则的用户数量
     * 2. 部门级别：统计配置的部门下所有在职用户数量
     * 3. 用户级别：统计直接配置的在职用户数量
     *
     * @param configNo 打卡配置编码
     * @return 用户数量统计结果
     * @throws BusinessLogicException 当配置不存在时抛出异常
     */
    public RuleConfigApplyUserCountDTO queryApplyUserCount(String configNo) {
        // 获取打卡配置和范围
        ReissueCardConfigBO configBO = reissueCardConfigManage.getConfigBO(configNo);
        if (null == configBO) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        RuleConfigApplyUserCountDTO ruleConfigApplyUserDTO = RuleConfigApplyUserCountDTO.init();
        List<Long> userRangeIds = configBO.queryUserRangeIds();
        if (CollectionUtils.isNotEmpty(userRangeIds)) {
            ruleConfigApplyUserDTO.setUserLevelUserCount(userService.listOnJobUsers(userRangeIds).size());
        }
        List<Long> deptRangeIds = configBO.queryDeptRangeIds();
        if (CollectionUtils.isNotEmpty(deptRangeIds)) {
            ruleConfigApplyUserDTO.setDeptLevelUserCount(userService.listOnJobUsers(deptRangeIds).size());
        }
        List<Long> countryRangeIds = configBO.queryCountryRangeIds();
        if (CollectionUtils.isNotEmpty(countryRangeIds)) {
            ruleConfigApplyUserDTO.setCountryLevelUserCount(userService.listOnJobUsers(countryRangeIds).size());
        }
        return ruleConfigApplyUserDTO;
    }

    /**
     * 查询补卡配置详情(不区分是否启用)
     * @param configNo 补卡配置编码
     * @return 补卡配置详情
     * @throws BusinessLogicException 当配置不存在时抛出异常
     */
    public ReissueCardConfigDetailDTO queryConfigDetail(String configNo) {
        // 获取补卡配置和范围(不需要最新启用的)
        List<ReissueCardConfigDO> reissueCardConfigDOList = reissueCardConfigDao.listByConfigNo(configNo);
        if (CollectionUtils.isEmpty(reissueCardConfigDOList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        ReissueCardConfigDO configDO = reissueCardConfigDOList.get(0);
        ReissueCardConfigDetailDTO detailDTO = ReissueCardConfigMapstruct.INSTANCE.toDetailDTO(configDO);

        List<ReissueCardConfigRangeDO> reissueCardConfigRangeDOList = reissueCardConfigRangeDao.listNotDeletedByConfigId(configDO.getId());

        List<Long> userIdList = reissueCardConfigRangeDOList.stream()
                .filter(ReissueCardConfigRangeDO::areUserRange)
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(userIdList)) {
            detailDTO.setApplyUserList(userService.listOnJobUsers(userIdList)
                    .stream()
                    .map(ConfigRangeDTO::buildUserRangeDTO)
                    .collect(Collectors.toList()));
        }
        // 设置 deptRecords
        if (StringUtils.isNotBlank(configDO.getDeptIds())) {
            List<Long> deptIds = configDO.listDeptIds();
            detailDTO.setApplyDeptList(deptService.listByDeptIds(deptIds)
                    .stream()
                    .map(ConfigRangeDTO::buildDeptRangeDTO)
                    .collect(Collectors.toList()));
        }
        return detailDTO;
    }

}
