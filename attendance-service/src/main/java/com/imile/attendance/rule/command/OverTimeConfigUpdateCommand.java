package com.imile.attendance.rule.command;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OverTimeConfigUpdateCommand extends OverTimeConfigAddCommand{

    /**
     * 加班规则编码
     */
    @NotBlank(message = "configNo can not be null")
    private String configNo;
}
