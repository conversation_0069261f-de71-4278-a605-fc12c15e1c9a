package com.imile.attendance.rule.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigAddVO implements Serializable {

    /**
     * 成功标识
     */
    private Boolean success = Boolean.TRUE;

    /**
     * 班次适用范围重复信息
     */
    private List<ClassRangeDuplicateInfoVO> classRangeDuplicateInfoList;


    @Data
    public static class ClassRangeDuplicateInfoVO {

        /**
         * 重复信息
         */
        private String repeatName;

        /**
         * 班次名称
         */
        private String className;

    }
}
