package com.imile.attendance.rule.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/15 
 * @Description
 */
@Data
public class PunchConfigDTO {

    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 打卡规则编码
     */
    private String configNo;

    /**
     * 打卡规则名称
     */
    private String configName;

    /**
     * 打卡规则类型：1.免打卡 2.班次固定打卡 3.灵活打卡一次 4.灵活打卡两次
     */
    private String configType;

    /**
     * 上下班打卡时间间隔 单位：小时（类型为灵活打卡两次时需设置）
     */
    private BigDecimal punchTimeInterval;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 适用部门
     */
    private String deptIds;

    /**
     * 是否最新
     */
    private Integer isLatest;


    private Date effectTime;

    private Date expireTime;
}
