package com.imile.attendance.rule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PunchClassConfigSwitchStatusDTO implements Serializable {

    /**
     * 班次ID
     */
    private Long classId;

    /**
     * 适用范围影响的用户ID集合
     */
    private Set<Long> userIds;

    /**
     * 状态
     */
    private String status;
}
