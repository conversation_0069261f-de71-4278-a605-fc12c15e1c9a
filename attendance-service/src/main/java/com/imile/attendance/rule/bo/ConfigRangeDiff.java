package com.imile.attendance.rule.bo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 配置范围差异类
 * <p>
 * 用于存储考勤规则配置更新前后的适用范围差异，主要用于跟踪用户和部门的变更。
 * 在规则变更时，系统需要知道哪些用户和部门被添加或移除，以便正确应用新规则和清理旧规则。
 * 此类通常用于考勤打卡规则、加班规则和补卡规则等配置的范围变更处理。
 * </p>
 *
 * <AUTHOR> chen
 * @Date 2025/4/16
 */
@Data
@AllArgsConstructor
public class ConfigRangeDiff {

    /**
     * 新增的用户ID列表
     * <p>
     * 存储在规则更新后新添加的用户ID集合，这些用户将开始应用新的考勤规则
     * </p>
     */
    private List<Long> addUserIds;

    /**
     * 移除的用户ID列表
     * <p>
     * 存储在规则更新后被移除的用户ID集合，这些用户将不再应用当前考勤规则
     * </p>
     */
    private List<Long> delUserIds;

    /**
     * 新增的部门ID列表
     * <p>
     * 存储在规则更新后新添加的部门ID集合，这些部门的所有成员将开始应用新的考勤规则
     * </p>
     */
    private List<Long> addDeptIds;

    /**
     * 移除的部门ID列表
     * <p>
     * 存储在规则更新后被移除的部门ID集合，这些部门的所有成员将不再应用当前考勤规则
     * </p>
     */
    private List<Long> delDeptIds;
}
