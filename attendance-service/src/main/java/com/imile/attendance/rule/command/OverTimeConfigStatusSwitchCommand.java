package com.imile.attendance.rule.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17 
 * @Description
 */
@Data
public class OverTimeConfigStatusSwitchCommand {

    /**
     * 加班规则编码
     */
    @NotBlank(message = "configNo can not be null")
    private String configNo;

    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    @NotNull(message = "status cannot be empty")
    private String status;
}
