package com.imile.attendance.rule.mapstruct;

import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigQuery;
import com.imile.attendance.rule.bo.PunchConfig;
import com.imile.attendance.rule.dto.PunchConfigDTO;
import com.imile.attendance.rule.dto.PunchConfigDetailDTO;
import com.imile.attendance.rule.dto.PunchConfigPageDTO;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.dto.UserRangeHistoryDTO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigPageQuery;
import com.imile.attendance.util.DateHelper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7 
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface PunchConfigMapstruct {

    PunchConfigMapstruct INSTANCE = Mappers.getMapper(PunchConfigMapstruct.class);


    PunchConfigDO copy(PunchConfigDO punchConfigDO);


    @Mapping(target = "recordVersion", ignore = true)
    PunchConfigDO toModel(PunchConfig punchConfig);

    List<PunchConfigDO> toModel(List<PunchConfig> punchConfigs);


    PunchConfigDTO toPunchConfigDTO(PunchConfigDO punchConfigDO);

    List<PunchConfigDTO> toPunchConfigDTO(List<PunchConfigDO> punchConfigDOList);


    @Mapping(target = "isCoverOld", ignore = true)
    @Mapping(target = "applyUserList", ignore = true)
    @Mapping(target = "applyDeptList", ignore = true)
    PunchConfigDetailDTO toDetailDTO(PunchConfigDO punchConfigDO);


    @Mapping(target = "rangeRecords", ignore = true)
    @Mapping(target = "userRangeStr", ignore = true)
    @Mapping(target = "punchConfigTypeDesc", ignore = true)
    @Mapping(target = "lastUpdDateStr", ignore = true)
    @Mapping(target = "deptRangeStr", ignore = true)
    @Mapping(target = "createDateStr", ignore = true)
    @Mapping(target = "countryRangeStr", ignore = true)
    @Mapping(target = "employeeCount", ignore = true)
    @Mapping(target = "punchConfigType", source = "configType")
    @Mapping(target = "punchConfigNo", source = "configNo")
    @Mapping(target = "punchConfigName", source = "configName")
    PunchConfigPageDTO toPunchConfigPageDTO(PunchConfigDO punchConfigDO);


    @Mapping(target = "employeeTypeDesc", ignore = true)
    @Mapping(target = "deptName", ignore = true)
    @Mapping(target = "deptCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    @Mapping(target = "country", ignore = true)
    RuleConfigUserInfoDTO toRuleConfigUserInfoDTO(UserInfoDO userInfoDO);

    List<RuleConfigUserInfoDTO> toRuleConfigUserInfoDTO(List<UserInfoDO> userInfoDOList);

    @Mapping(target = "country", source = "country")
    @Mapping(target = "punchConfigName", source = "punchConfigName")
    @Mapping(target = "deptIds", source = "deptIds")
    @Mapping(target = "status", source = "status")
    @Mapping(target = "userIdList", source = "userIdList")
    @Mapping(target = "arePageExport", source = "arePageExport")
    PunchConfigQuery toPunchConfigQuery(PunchConfigPageQuery punchConfigPageQuery);


    default List<UserRangeHistoryDTO> toUserRangeHistoryDTOList(List<PunchConfigDO> punchConfigDOList,
                                                                List<PunchConfigRangeDO> punchConfigRangeDOList) {
        Map<Long, PunchConfigDO> punchConfigDOMap = punchConfigDOList.stream()
                .collect(Collectors.toMap(PunchConfigDO::getId, punchConfigDO -> punchConfigDO,
                        (existing, replacement) -> existing));
        return punchConfigRangeDOList.stream()
                .map(configRangeDO -> {
                    UserRangeHistoryDTO userRangeHistoryDTO = new UserRangeHistoryDTO();

                    PunchConfigDO punchConfigDO = punchConfigDOMap.get(configRangeDO.getRuleConfigId());
                    if (null != punchConfigDO) {
                        userRangeHistoryDTO.setRuleNo(punchConfigDO.getConfigNo());
                        userRangeHistoryDTO.setRuleName(punchConfigDO.getConfigName());
                    }
                    userRangeHistoryDTO.setUserId(String.valueOf(configRangeDO.getBizId()));
                    userRangeHistoryDTO.setRuleBindStartDate(DateHelper.formatYYYYMMDDHHMMSS(configRangeDO.getEffectTime()));
                    userRangeHistoryDTO.setRuleBindEndDate(DateHelper.formatYYYYMMDDHHMMSS(configRangeDO.getExpireTime()));
                    userRangeHistoryDTO.setOperateUserName(configRangeDO.getLastUpdUserName());
                    userRangeHistoryDTO.setOperateTime(DateHelper.formatYYYYMMDDHHMMSS(configRangeDO.getLastUpdDate()));
                    userRangeHistoryDTO.setRuleBindDateFormat(userRangeHistoryDTO.generateRuleBindDateFormat());
                    return userRangeHistoryDTO;

                })
                .collect(Collectors.toList());
    }


}
