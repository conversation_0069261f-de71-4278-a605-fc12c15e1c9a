package com.imile.attendance.rule.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/15
 * @Description 打卡规则用户查询
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PunchConfigUserQuery extends ResourceQuery {

    /**
     * 打卡规则编码
     */
    private String punchConfigNo;

    /**
     * 员工常驻地国家
     */
    private String locationCountry;

    /**
     * 员工姓名/工号
     */
    private String userCodeOrName;

    /**
     * 员工部门
     */
    private Long deptId;

    /**
     * 员工部门集合
     */
    private List<Long> deptIds;

}
