package com.imile.attendance.rule.command;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7 
 * @Description
 */
@Data
public class PunchConfigAddCommand {

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡规则类型
     */
    private String punchConfigType;

    /**
     * 上下班打卡时间间隔 单位：小时
     */
    private BigDecimal punchTimeInterval;

    /**
     * 国家
     */
    private String country;

    /**
     * 适用部门
     */
    private List<Long> deptIds;

    /**
     * 适用用户
     */
    private List<Long> userIds;


    public List<Long> getDeptIds() {
        return Optional.ofNullable(deptIds)
                .map(ids -> ids.stream()
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }



    public static void main(String[] args) {
        PunchConfigAddCommand punchConfigAddCommand = new PunchConfigAddCommand();
        punchConfigAddCommand.setDeptIds(Arrays.asList(1L,null));
        System.out.println(punchConfigAddCommand.getDeptIds());
    }


}
