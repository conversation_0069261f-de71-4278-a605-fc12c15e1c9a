package com.imile.attendance.rule.command;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description
 */
@Data
public class ReissueCardConfigAddCommand {

    /**
     * 补卡规则名称
     */
    private String configName;

    /**
     * 每月最大补卡次数
     */
    private Integer maxRepunchNumber;


    /**
     * 适用国家
     */
    private String country;

    /**
     * 适用部门
     */
    private List<Long> deptIds;

    /**
     * 适用用户
     */
    private List<Long> userIds;

    public List<Long> getDeptIds() {
        return Optional.ofNullable(deptIds)
                .map(ids -> ids.stream()
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    public Boolean isCountryLevelRangeFlag(){
        return CollectionUtils.isEmpty(deptIds) &&
                CollectionUtils.isEmpty(userIds);
    }


}
