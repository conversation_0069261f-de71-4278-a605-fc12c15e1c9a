package com.imile.attendance.rule.permission;

import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/21 
 * @Description 规则权限统一查询类
 */
@Service
public class RuleConfigPermissionService {

    @Resource
    private AttendanceDeptService attendanceDeptService;

    public PermissionCountryDeptVO getPermissionCountryDeptVO(List<Long> deptIds, List<String> countryList) {
        PermissionCountryDeptVO permissionDept = this.getPermissionCountryDeptVO();

        // 部门
        if (CollectionUtils.isNotEmpty(deptIds)) {
            if (permissionDept.getIsSysAdmin()) {
                permissionDept.setDeptIdList(deptIds);
            } else {
                List<Long> deptIdList = permissionDept.getDeptIdList();
                Collection<Long> intersectionDept = CollectionUtils.intersection(deptIds, deptIdList);
                if (CollectionUtils.isNotEmpty(intersectionDept)) {
                    permissionDept.setDeptIdList(new ArrayList<>(intersectionDept));
                } else {
                    permissionDept.setHasDeptPermission(Boolean.FALSE);
                    permissionDept.setDeptIdList(new ArrayList<>());
                }
            }
        }

        // 国家
        if (CollectionUtils.isNotEmpty(countryList)) {
            if (permissionDept.getIsSysAdmin()) {
                permissionDept.setCountryList(countryList);
            } else {
                Collection<String> intersectionCountry = CollectionUtils.intersection(countryList, permissionDept.getCountryList());
                if (CollectionUtils.isNotEmpty(intersectionCountry)) {
                    permissionDept.setCountryList(new ArrayList<>(intersectionCountry));
                } else {
                    permissionDept.setHasCountryPermission(Boolean.FALSE);
                    permissionDept.setCountryList(new ArrayList<>());
                }
            }
        }

        // 非系统管理员
        if (!permissionDept.getIsSysAdmin()) {
            // 部门国家权限
            if (CollectionUtils.isNotEmpty(permissionDept.getDeptIdList()) &&
                    CollectionUtils.isNotEmpty(permissionDept.getCountryList())) {
                permissionDept
                        .setHasAndDeptAndCountryPermission(Boolean.TRUE)
                        .setHasOrDeptAndCountryPermission(Boolean.TRUE);
            } else {
                if (CollectionUtils.isNotEmpty(permissionDept.getDeptIdList()) ||
                        CollectionUtils.isNotEmpty(permissionDept.getCountryList())) {
                    permissionDept
                            .setHasOrDeptAndCountryPermission(Boolean.TRUE);
                } else {
                    permissionDept
                            .setHasOrDeptAndCountryPermission(Boolean.FALSE);
                }
                permissionDept
                        .setHasAndDeptAndCountryPermission(Boolean.FALSE);
            }
        }

        return permissionDept;
    }

    public PermissionCountryDeptVO getPermissionCountryDeptVO() {
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        PermissionCountryDeptVO permissionCountryDeptVO = new PermissionCountryDeptVO();
        // 系统管理员
        if (userContext.isSystem()) {
            return permissionCountryDeptVO
                    .setIsSysAdmin(Boolean.TRUE)
                    .setHasDeptPermission(Boolean.TRUE)
                    .setHasCountryPermission(Boolean.TRUE)
                    .setHasAndDeptAndCountryPermission(Boolean.TRUE)
                    .setHasOrDeptAndCountryPermission(Boolean.TRUE)
                    .setDeptIdList(new ArrayList<>())
                    .setCountryList(new ArrayList<>());
        } else {
            permissionCountryDeptVO
                    .setIsSysAdmin(Boolean.FALSE);
        }

        Set<String> allCountryList = new HashSet<>();

        // 部门
        List<Long> organizationIds = userContext.getOrganizationIds();
        if (CollectionUtils.isNotEmpty(organizationIds)) {
            permissionCountryDeptVO
                    .setHasDeptPermission(Boolean.TRUE)
                    .setDeptIdList(organizationIds);
            //部门地理国
            Set<String> countrySet = attendanceDeptService.listByDeptIds(organizationIds)
                    .stream().map(AttendanceDept::getCountry).collect(Collectors.toSet());
            allCountryList.addAll(countrySet);
        } else {
            permissionCountryDeptVO
                    .setHasDeptPermission(Boolean.FALSE)
                    .setDeptIdList(new ArrayList<>());
        }

        // 国家 用户常驻国+部门地理国
        List<String> countryList = userContext.getCountryList();
        if (CollectionUtils.isNotEmpty(countryList)) {
            allCountryList.addAll(countryList);
            permissionCountryDeptVO
                    .setHasCountryPermission(Boolean.TRUE)
                    .setCountryList(new ArrayList<>(allCountryList));
        } else {
            if (CollectionUtils.isNotEmpty(allCountryList)) {
                permissionCountryDeptVO
                        .setHasCountryPermission(Boolean.TRUE)
                        .setCountryList(new ArrayList<>(allCountryList));
            } else {
                permissionCountryDeptVO
                        .setHasCountryPermission(Boolean.FALSE)
                        .setCountryList(new ArrayList<>());
            }
        }

        // 部门国家权限
        if (CollectionUtils.isNotEmpty(permissionCountryDeptVO.getDeptIdList()) &&
                CollectionUtils.isNotEmpty(permissionCountryDeptVO.getCountryList())) {
            permissionCountryDeptVO
                    .setHasAndDeptAndCountryPermission(Boolean.TRUE)
                    .setHasOrDeptAndCountryPermission(Boolean.TRUE);
        } else {
            if (CollectionUtils.isNotEmpty(permissionCountryDeptVO.getDeptIdList()) ||
                    CollectionUtils.isNotEmpty(permissionCountryDeptVO.getCountryList())) {
                permissionCountryDeptVO
                        .setHasOrDeptAndCountryPermission(Boolean.TRUE);
            } else {
                permissionCountryDeptVO
                        .setHasOrDeptAndCountryPermission(Boolean.FALSE);
            }
            permissionCountryDeptVO
                    .setHasAndDeptAndCountryPermission(Boolean.FALSE);
        }

        return permissionCountryDeptVO;
    }
}
