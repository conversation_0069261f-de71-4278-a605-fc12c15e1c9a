package com.imile.attendance.rule.dto;

import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/15 
 * @Description 规则配置用户信息DTO
 */
@Data
public class RuleConfigUserInfoDTO {

    /**
     * 用户主键
     */
    private Long id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型描述
     */
    private String employeeTypeDesc;

    /**
     * 核算国
     */
    private String originCountry;

    /**
     * 部门主键
     */
    private Long deptId;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 地理国
     */
    private String country;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 规则绑定时间
     */
    private Date createDate;
}
