package com.imile.attendance.common;

import com.imile.attendance.cycleConfig.dto.AttendanceDayCycleDTO;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
public interface AttendanceBaseService {


    /**
     * 获取考勤周期配置
     *
     * @param userId 用户ID
     * @return HrmsAttendanceCycleConfigDO
     */
    AttendanceCycleConfigDO getUserAttendanceCycleConfig(Long userId);

    /**
     * 当前时间所属考勤周期的时间点
     *
     * @param nowDayId                    当前时间
     * @param attendanceCycleConfigDO 考勤周期配置
     * @return AttendanceDayCycleDTO
     */
    AttendanceDayCycleDTO getUserAttendanceCycleConfigDay(Long nowDayId, AttendanceCycleConfigDO attendanceCycleConfigDO);

    /**
     * 发放补卡次数逻辑：获取考勤周期
     *
     * @param userId 用户id
     * @return HrmsAttendanceCycleConfigDO
     */
    AttendanceCycleConfigDO getUserAttendanceCycleConfigUserCard(Long userId);
}
