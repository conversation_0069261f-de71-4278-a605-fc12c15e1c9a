package com.imile.attendance.vacation.factory;

import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveStageDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Slf4j
@Service
public class UserLeaveStageDetailFactory {
    @Resource
    private UserLeaveStageDetailDao userLeaveStageDetailDao;


    /**
     * 批量保存余额信息
     *
     * @param stageDetailDOList
     * @return
     */
    public boolean batchSaveStageDetail(List<UserLeaveStageDetailDO> stageDetailDOList) {
        return userLeaveStageDetailDao.saveBatch(stageDetailDOList);
    }

    /**
     * 批量修改余额信息
     *
     * @param stageDetailDOList
     * @return
     */
    public boolean batchUpdateStageDetail(List<UserLeaveStageDetailDO> stageDetailDOList) {
        return userLeaveStageDetailDao.updateBatchById(stageDetailDOList);
    }
}
