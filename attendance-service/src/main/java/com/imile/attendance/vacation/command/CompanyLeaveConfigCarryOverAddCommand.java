package com.imile.attendance.vacation.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
@ApiModel(description = "国家福利假期：结转规则保存入参")
public class CompanyLeaveConfigCarryOverAddCommand {

    /**
     * 国家假期配置id
     */
    @ApiModelProperty(value = "国家假期配置id")
    private Long leaveId;

    /**
     * 是否永久有效：1：是 2：否
     */
    @ApiModelProperty(value = "是否永久有效：1：是 2：否")
    private Integer isInvalid;

    /**
     * 是否可结转：1：可结转 2：不可结转
     */
    @ApiModelProperty(value = "是否可结转：1：可结转 2：不可结转")
    private Integer isCarryOver;

    /**
     * 最大结转天数
     */
    @ApiModelProperty(value = "最大结转天数")
    private Integer maxCarryOverDay;

    /**
     * 结转失效类型：0：固定日期 1：按年失效 2：按入职日期设置
     */
    @ApiModelProperty(value = "结转失效类型：0：固定日期 1：按年失效 2：按入职日期设置")
    private Integer invalidType;

    /**
     * 结转失效年：0：次年 1：第三年 2：第四年
     */
    @ApiModelProperty(value = "结转失效年：0：次年 1：第三年 2：第四年")
    private Integer invalidYear;

    /**
     * 失效日期：invalid_date 示例：0124
     */
    @ApiModelProperty(value = "失效日期：invalid_date 示例：0124")
    private Long invalidDate;

    /**
     * 假期失效类型是按入职日期设置的时候，范围数据必填，其他发放类型不需要该字段
     */
    @ApiModelProperty(value = "假期失效类型按入职日期范围")
    private List<CompanyLeaveConfigCarryOverRangeAddCommand> leaveConfigCarryOverRangeList;
}
