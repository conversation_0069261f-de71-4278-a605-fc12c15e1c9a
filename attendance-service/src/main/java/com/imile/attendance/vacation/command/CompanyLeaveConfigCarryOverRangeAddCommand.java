package com.imile.attendance.vacation.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
@ApiModel(description = "国家结转失效假期范围入参")
public class CompanyLeaveConfigCarryOverRangeAddCommand {

    /**
     * 国家假期规则结转表主键id
     */
    @ApiModelProperty(value = "国家假期规则结转表主键id")
    private Long carryOverId;

    /**
     * 左边符号：1：表示大于 2：表示大于等于
     */
    @ApiModelProperty(value = "左边符号：1：表示大于 2：表示大于等于")
    private Integer symbolLeft;

    /**
     * 左边日期(Mdd)：示例：0124
     */
    @ApiModelProperty(value = "左边日期(Mdd)：示例：0124")
    private Integer entryDateLeft;

    /**
     * 右边符号：1：表示小于 2：表示小于等于
     */
    @ApiModelProperty(value = "右边符号：1：表示小于 2：表示小于等于")
    private Integer symbolRight;

    /**
     * 右边日期(Mdd)：示例：0124
     */
    @ApiModelProperty(value = "右边日期(Mdd)：示例：0124")
    private Integer entryDateRight;

    /**
     * 结转失效年：0：次年 1：第三年 2：第四年
     */
    @ApiModelProperty(value = "结转失效年：0：次年 1：第三年 2：第四年")
    private Integer invalidYear;

    /**
     * 失效日期(Mdd)：示例：0124
     */
    @ApiModelProperty(value = "失效日期(Mdd)：示例：0124")
    private Integer invalidDate;
}
