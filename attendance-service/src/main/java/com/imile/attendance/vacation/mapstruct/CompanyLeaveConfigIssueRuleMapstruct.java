package com.imile.attendance.vacation.mapstruct;


import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveJourneyConfigDO;
import com.imile.attendance.vacation.command.CompanyLeaveConfigIssueRuleAddCommand;
import com.imile.attendance.vacation.command.CompanyLeaveConfigIssueRuleRangeAddCommand;
import com.imile.attendance.vacation.command.CompanyLeaveConfigJourneyAddCommand;
import com.imile.attendance.vacation.vo.LeaveConfigIssueRuleRangeVO;
import com.imile.attendance.vacation.vo.LeaveConfigIssueRuleVO;
import com.imile.attendance.vacation.vo.LeaveJourneyConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface CompanyLeaveConfigIssueRuleMapstruct {

    CompanyLeaveConfigIssueRuleMapstruct INSTANCE = Mappers.getMapper(CompanyLeaveConfigIssueRuleMapstruct.class);


    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    CompanyLeaveConfigIssueRuleDO mapToCompanyLeaveIssueRuleConfigDO(CompanyLeaveConfigIssueRuleAddCommand addCommand);

    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    List<CompanyLeaveConfigIssueRuleRangeDO> mapToCompanyLeaveIssueRuleRangeConfigDO(List<CompanyLeaveConfigIssueRuleRangeAddCommand> addCommand);

    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    List<CompanyLeaveJourneyConfigDO> mapToCompanyLeaveJourneyConfigDO(List<CompanyLeaveConfigJourneyAddCommand> addCommand);

    // VO转换
    LeaveConfigIssueRuleVO mapToCompanyLeaveIssueRuleConfigVO(CompanyLeaveConfigIssueRuleDO companyLeaveConfigIssueRuleDO);

    List<LeaveConfigIssueRuleRangeVO> mapToCompanyLeaveConfigIssueRuleRangeVO(List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeList);

    List<LeaveJourneyConfigVO> mapToCompanyLeaveJourneyConfigVO(List<CompanyLeaveJourneyConfigDO> companyLeaveJourneyConfigList);
}
