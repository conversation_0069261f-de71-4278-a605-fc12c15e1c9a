package com.imile.attendance.vacation.application;

import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.attendance.vacation.command.CompanyLeaveConfigAddCommand;
import com.imile.attendance.vacation.command.CompanyLeaveConfigStatusUpdateCommand;
import com.imile.attendance.vacation.command.CompanyLeaveConfigUpdateCommand;
import com.imile.attendance.vacation.dto.CompanyLeaveDetailDTO;
import com.imile.attendance.vacation.factory.CompanyLeaveConfigFactory;
import com.imile.attendance.vacation.query.CompanyLeaveConfigDetailQuery;
import com.imile.attendance.vacation.query.CompanyLeaveConfigQuery;
import com.imile.attendance.vacation.vo.CompanyLeaveConfigVO;
import com.imile.common.page.PaginationResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Service
public class CompanyLeaveConfigApplicationService {

    @Resource
    private CompanyLeaveConfigFactory companyLeaveConfigFactory;
    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;


    public PaginationResult<CompanyLeaveDetailDTO> list(CompanyLeaveConfigQuery query) {
        return companyLeaveConfigService.list(query);
    }

    public Boolean add(CompanyLeaveConfigAddCommand addCommand) {
        return companyLeaveConfigFactory.add(addCommand);
    }

    public CompanyLeaveConfigVO detail(CompanyLeaveConfigDetailQuery query) {
        return companyLeaveConfigService.detail(query);
    }

    public Boolean update(CompanyLeaveConfigUpdateCommand updateCommand) {
        return companyLeaveConfigFactory.update(updateCommand);
    }

    public Boolean updateStatus(CompanyLeaveConfigStatusUpdateCommand updateCommand) {
        return companyLeaveConfigFactory.updateStatus(updateCommand);
    }

}
