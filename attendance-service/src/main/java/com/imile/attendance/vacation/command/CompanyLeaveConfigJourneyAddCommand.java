package com.imile.attendance.vacation.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
@ApiModel(description = "国家福利假期：路途假保存入参")
public class CompanyLeaveConfigJourneyAddCommand {

    /**
     * 国家发放规则表主键id
     */
    @ApiModelProperty(value = "国家发放规则表主键id")
    private Long issueRuleId;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 路程假天数
     */
    @ApiModelProperty(value = "路程假天数")
    private BigDecimal journeyDays;

}
