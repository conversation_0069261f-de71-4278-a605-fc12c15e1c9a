package com.imile.attendance.vacation.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/27
 * @Description 用户假期发放处理参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLeaveInspectionParam {
    /**
     * 所属国
     */
    private String countryList;
    /**
     * 用户编码
     */
    private String userCodes;
    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 假期名称
     */
    private String leaveName;


    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 每月的几号发放假期：针对HQ、UAE国家【他们是每月一号（这个值默认等于1），但是测试不好测试，这边写一个变量，方便测试】哪一天
     */
    private Integer whichDay = 1;

    /**
     * 派遣假期拦截时间
     */
    private String dispatchLeaveInterceptTime = "2025-01-01 00:00:00";

    /**
     * 每满时间
     */
    private String everyFullCurrentDate;
}