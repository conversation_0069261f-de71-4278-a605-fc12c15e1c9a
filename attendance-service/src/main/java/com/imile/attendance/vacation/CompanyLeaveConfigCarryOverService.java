package com.imile.attendance.vacation;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.enums.vacation.LeaveConfigInvalidTypeEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIsCarryOverEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIsInvalidEnum;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigCarryOverDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigCarryOverRangeDao;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.util.date.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * {@code @author:} allen
 * {@code @className:} CompanyLeaveConfigCarryOverService
 * {@code @since:} 2024-04-10 14:51
 * {@code @description:}
 */
@Service
public class CompanyLeaveConfigCarryOverService {

    @Resource
    private CompanyLeaveConfigCarryOverDao companyLeaveConfigCarryOverDao;
    @Resource
    private CompanyLeaveConfigCarryOverRangeDao companyLeaveConfigCarryOverRangeDao;
    @Resource
    private CountryService countryService;

    private static final String defaultInvalidDate = "99991231";

    /**
     * 根据假期方案id查询假期结转配置
     *
     * @param leaveId 假期方案id
     * @return 假期结转配置
     */
    public List<CompanyLeaveConfigCarryOverDO> getCarryOverByConfigId(Long leaveId) {
        if (Objects.isNull(leaveId)) {
            return Lists.newArrayList();
        }
        return companyLeaveConfigCarryOverDao.getCarryOverByConfigId(leaveId);
    }

    /**
     * 获取假期结转失效规则范围
     *
     * @param carryOverId 假期结转失效规则id
     * @return 假期结转失效规则范围
     */
    public List<CompanyLeaveConfigCarryOverRangeDO> getLeaveConfigCarryOverRangeList(Long carryOverId) {
        if (Objects.isNull(carryOverId)) {
            return Lists.newArrayList();
        }
        return companyLeaveConfigCarryOverRangeDao.getLeaveConfigCarryOverRangeList(carryOverId);
    }

    /**
     * 判断请假期间是否发生假期结转和失效
     *
     * @param leaveId
     * @param leaveCreateDate
     * @param country
     * @return
     */
    public Integer checkIfCarryOver(Long leaveId,
                                    Date leaveCreateDate,
                                    String country,
                                    List<UserLeaveStageDetailDO> userLeaveStageDetailList) {
        List<CompanyLeaveConfigCarryOverDO> companyLeaveConfigCarryOverList = companyLeaveConfigCarryOverDao.selectByLeaveId(Arrays.asList(leaveId));
        if (CollectionUtils.isEmpty(companyLeaveConfigCarryOverList)) {
            return BusinessConstant.ZERO;
        }
        // 永久有效不做结转判断
        CompanyLeaveConfigCarryOverDO carryOverDO = companyLeaveConfigCarryOverList.get(0);
        if (LeaveConfigIsInvalidEnum.YES.getType().equals(carryOverDO.getIsInvalid())) {
            return BusinessConstant.ZERO;
        }
        // 非永久有效查看假期是否可结转
        if (LeaveConfigIsCarryOverEnum.NO.getType().equals(carryOverDO.getIsCarryOver())) {
            return BusinessConstant.ZERO;
        }
        // 非永久有效并且可结转查询假期配置结转失效时间
        Long invalidConfigDate = carryOverDO.getInvalidDate();
        if (Objects.isNull(invalidConfigDate) || invalidConfigDate == 0) {
            return BusinessConstant.ZERO;
        }
        Date currentDate = getCountryCurrentDate(country);
        if (ObjectUtil.isNull(currentDate)) {
            return BusinessConstant.ZERO;
        }
        // 结转时间为每年的一月一号
        DateTime carryOverDate = DateUtil.beginOfYear(currentDate);
        // 如果当前日期大于结转日期且请假开始日期小于结转日期，则审批过程中已经结转数据，不再返还假期
        if (currentDate.after(carryOverDate) && leaveCreateDate.before(carryOverDate)) {
            return BusinessConstant.ONE;
        }
        // 如果当前日期大于失效日期且请假创建日期小于失效日期，则审批过程中已经失效数据，不再返还结转假期
        if (CollectionUtils.isEmpty(userLeaveStageDetailList)) {
            return BusinessConstant.ONE;
        }
        // 只要存在一条余额(不管是结转还是非结转数据) 满足失效条件，则就存在部分假期不返还
        for (UserLeaveStageDetailDO userLeaveStageDetail : userLeaveStageDetailList) {
            if (Objects.isNull(userLeaveStageDetail) || Objects.isNull(userLeaveStageDetail.getInvalidDate())) {
                return BusinessConstant.ONE;
            }
            Date invalidDate = DateUtils.str2Date(userLeaveStageDetail.getInvalidDate(), DateFormatterUtil.FORMAT_YYYYMMDD);
            if (currentDate.compareTo(invalidDate) >= 0 && leaveCreateDate.compareTo(invalidDate) < 0) {
                return BusinessConstant.TWO;
            }
        }

        return BusinessConstant.ZERO;
    }

    /**
     * 查询假期结转规则
     *
     * @param leaveIds
     * @return
     */
    public List<CompanyLeaveConfigCarryOverDO> selectByLeaveId(List<Long> leaveIds) {
        return companyLeaveConfigCarryOverDao.selectByLeaveId(leaveIds);
    }

    /**
     * 获取假期结转失效规则范围
     *
     * @param carryOverIdList 假期结转失效规则id
     * @return 假期结转失效规则范围
     */
    public List<CompanyLeaveConfigCarryOverRangeDO> selectRangeByCarryOverId(List<Long> carryOverIdList) {
        return companyLeaveConfigCarryOverRangeDao.selectByCarryOverId(carryOverIdList);
    }

    /**
     * 通过结转规则计算失效日期
     *
     * @return
     */
    public String getUserInvalidDate(Date localDate,
                                     AttendanceUserEntryRecord userEntryRecord,
                                     CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                     List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRange) {
        if (Objects.isNull(localDate) || Objects.isNull(leaveConfigCarryOver)) {
            return null;
        }
        // 永久有效 默认失效日期为 9999-12-31
        if (WhetherEnum.YES.getKey().equals(leaveConfigCarryOver.getIsInvalid())) {
            return defaultInvalidDate;
        }
        // 不可结转，默认有效日期为该年年底1231
        if (LeaveConfigIsCarryOverEnum.NO.getType().equals(leaveConfigCarryOver.getIsCarryOver())) {
            return DateUtils.date2Str(localDate, "yyyy") + "1231";
        }
        // 计算有效日期 - 固定日期
        if (LeaveConfigInvalidTypeEnum.FIXED_DATE.getType().equals(leaveConfigCarryOver.getInvalidType())) {
            // 根据配置结转余额失效年类型获取失效年份
            String invalidYear = this.getCarryOverYear(leaveConfigCarryOver.getInvalidYear(), localDate);
            Long invalidDate = leaveConfigCarryOver.getInvalidDate();
            if (Objects.isNull(invalidYear) || Objects.isNull(invalidDate)) {
                return null;
            }
            if (invalidDate < 1000) {
                return invalidYear + "0" + invalidDate;
            }
            return invalidYear + invalidDate;
        }
        // 计算有效日期 - 按年失效
        if (LeaveConfigInvalidTypeEnum.ANNUAL_LAPSE.getType().equals(leaveConfigCarryOver.getInvalidType())) {
            // 根据配置结转余额失效年类型获取失效年份
            String invalidYear = this.getCarryOverYear(leaveConfigCarryOver.getInvalidYear(), localDate);
            return invalidYear + DateUtils.date2Str(localDate, "MMdd");
        }
        // 计算有效日期 - 按入职日期设置
        if (LeaveConfigInvalidTypeEnum.EXPIRES_ON_THE_DATE_OF_ENTRY.getType().equals(leaveConfigCarryOver.getInvalidType())
                && CollectionUtils.isNotEmpty(leaveConfigCarryOverRange)
                && Objects.nonNull(userEntryRecord)) {
            Long confirmDate = Long.valueOf(DateUtils.date2Str(userEntryRecord.getConfirmDate(), "Mdd"));
            // 根据配置结转失效范围获取失效日期
            for (CompanyLeaveConfigCarryOverRangeDO range : leaveConfigCarryOverRange) {
                Integer symbolLeft = range.getSymbolLeft();
                Integer entryDateLeft = range.getEntryDateLeft();
                Integer symbolRight = range.getSymbolRight();
                Integer entryDateRight = range.getEntryDateRight();
                Integer invalidYear = range.getInvalidYear();
                Integer invalidDate = range.getInvalidDate();
                String invalidDateStr = String.valueOf(invalidDate);
                if (invalidDate < 1000) {
                    invalidDateStr = "0" + invalidDate;
                }
                String carryOverYear = this.getCarryOverYear(invalidYear, localDate);
                // 左边是大于，右边是小于
                if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 1)
                        && confirmDate > entryDateLeft && confirmDate < entryDateRight) {
                    return carryOverYear + invalidDateStr;
                }
                // 左边是大于，右边是小于等于
                if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 2)
                        && confirmDate > entryDateLeft && confirmDate <= entryDateRight) {
                    return carryOverYear + invalidDateStr;
                }
                // 左边是大于等于，右边是小于
                if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 1)
                        && confirmDate >= entryDateLeft && confirmDate < entryDateRight) {
                    return carryOverYear + invalidDateStr;
                }
                // 左边是大于等于，右边是小于等于
                if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 2)
                        && confirmDate >= entryDateLeft && confirmDate <= entryDateRight) {
                    return carryOverYear + invalidDateStr;
                }
            }
        }
        return null;
    }

    /**
     * 获取结转年份
     *
     * @param invalidYear
     * @param localDate
     * @return
     */
    private String getCarryOverYear(Integer invalidYear,
                                    Date localDate) {
        Date yearDate = new Date();
        switch (invalidYear) {
            case 0:
                yearDate = DateUtils.dateAddYears(localDate, 1);
                break;
            case 1:
                yearDate = DateUtils.dateAddYears(localDate, 2);
                break;
            case 2:
                yearDate = DateUtils.dateAddYears(localDate, 3);
                break;
            default:
                break;
        }
        return DateUtils.date2Str(yearDate, "yyyy");
    }

    private Date getCountryCurrentDate(String country) {
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryName(country);
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
        Map<String, String> countryConfigMap = countryConfigList.stream()
                .collect(Collectors.toMap(CountryConfigDTO::getCountryName, CountryConfigDTO::getTimeZone));

        // 获取国家对应时区
        String timeZone = countryConfigMap.getOrDefault(country, "");
        if (ObjectUtil.equal(timeZone, "")) {
            XxlJobLogger.log("该国家:{},不存在国家时区", country);
            return null;
        }
        Date now = new Date();
        // 获取国家当地时间
        Date dateTime = DateHelper.convertDateByTimeZonePlus(timeZone, now);
        XxlJobLogger.log("北京时间:{},当前国家:{},当地时间:{}", now, country, dateTime);
        return dateTime;
    }
}
