package com.imile.attendance.vacation.dto;

import lombok.Data;

import java.util.Date;

/**
 * 假期类型DTO
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/2/17
 */
@Data
public class CompanyLeaveTypeDTO {

    private Long id;
    /**
     * 所属国
     */
    private String country;
    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 假期类型(英文)
     */
    private String leaveTypeEn;
    /**
     * 假期类型(中文)
     */
    private String leaveTypeCn;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建日期
     */
    private Date createDate;
    /**
     * 修改人
     */
    private String lastUpdateUser;
    /**
     * 修改日期
     */
    private Date lastUpdateDate;
}
