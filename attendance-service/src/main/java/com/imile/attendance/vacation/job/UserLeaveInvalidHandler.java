package com.imile.attendance.vacation.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.vacation.job.service.UserLeaveInvalidService;
import com.imile.attendance.vacation.param.UserLeaveInvalidParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * {@code @author:} allen
 * {@code @className:} UserLeaveCarryOverHandler
 * {@code @since:} 2024-04-29 16:22
 * {@code @description:} 用户假期失效处理
 */
@Slf4j
@Component
public class UserLeaveInvalidHandler {
    @Resource
    private UserLeaveInvalidService userLeaveInvalidService;

    @XxlJob(BusinessConstant.JobHandler.USER_LEAVE_INVALID_HANDLER)
    public ReturnT<String> userLeaveInvalidHandler(String content) {
        UserLeaveInvalidParam param = StringUtils.isNotBlank(content)
                ? JSON.parseObject(content, UserLeaveInvalidParam.class)
                : new UserLeaveInvalidParam();
        if (ObjectUtil.isNull(param)) {
            XxlJobLogger.log("xxl-job【userLeaveInvalidHandler】参数为空");
            return ReturnT.SUCCESS;
        }
        this.buildDefaultParam(param);
        XxlJobLogger.log("xxl-job【userLeaveInvalidHandler】开始执行，参数为：{}", JSON.toJSONString(param));
        userLeaveInvalidService.userLeaveInvalidHandler(param);
        return ReturnT.SUCCESS;
    }

    /**
     * 参数默认值设置
     *
     * @param param
     */
    private void buildDefaultParam(UserLeaveInvalidParam param) {
        if (ObjectUtil.isNull(param.getLocalYear())) {
            XxlJobLogger.log("xxl-job【userLeaveInspectionNewHandler】年份为空，设置默认值为当年");
            // 获取当前年份
            int year = DateUtil.year(DateUtil.date());
            param.setLocalYear(year);
        }
        if (StringUtils.isNotBlank(param.getEmployeeType())) {
            param.setEmployeeTypeList(Arrays.asList(param.getEmployeeType().split(",")));
        }
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            param.setUserCodeList(Arrays.asList(param.getUserCodes().split(",")));
        }
        if (StringUtils.isNotBlank(param.getCountryList())) {
            param.setCountryArrayList(Arrays.asList(param.getCountryList().split(",")));
        }
        if (StringUtils.isNotBlank(param.getLeaveName())) {
            param.setLeaveNameList(Arrays.asList(param.getLeaveName().split(",")));
        }
    }

}
