package com.imile.attendance.vacation.factory;

import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Slf4j
@Service
public class UserLeaveDetailFactory {
    @Resource
    private UserLeaveDetailDao userLeaveDetailDao;


    /**
     * 保存或修改
     *
     * @param detailDO
     * @return
     */
    public boolean saveOrUpdate(UserLeaveDetailDO detailDO) {
        return userLeaveDetailDao.saveOrUpdate(detailDO);
    }

    /**
     * 批量保存
     *
     * @param detailDOList
     * @return
     */
    public boolean batchSave(List<UserLeaveDetailDO> detailDOList) {
        if (CollectionUtils.isEmpty(detailDOList)) {
            return true;
        }
        return userLeaveDetailDao.saveBatch(detailDOList);
    }

    /**
     * 批量修改数据
     *
     * @param detailDOList
     * @return
     */
    public boolean batchUpdate(List<UserLeaveDetailDO> detailDOList) {
        if (CollectionUtils.isEmpty(detailDOList)) {
            return true;
        }
        return userLeaveDetailDao.updateBatchById(detailDOList);
    }
}
