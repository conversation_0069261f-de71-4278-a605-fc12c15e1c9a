package com.imile.attendance.vacation.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 假期类型VO
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/2/17
 */
@Data
public class CompanyLeaveTypeVO implements Serializable {

    private static final long serialVersionUID = -4679458894408655371L;

    private Long id;
    /**
     * 所属国
     */
    private String country;
    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 假期类型(英文)
     */
    private String leaveTypeEn;
    /**
     * 假期类型(中文)
     */
    private String leaveTypeCn;
}
