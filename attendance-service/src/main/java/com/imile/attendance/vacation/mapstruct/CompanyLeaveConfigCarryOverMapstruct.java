package com.imile.attendance.vacation.mapstruct;


import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.vacation.command.CompanyLeaveConfigCarryOverAddCommand;
import com.imile.attendance.vacation.command.CompanyLeaveConfigCarryOverRangeAddCommand;
import com.imile.attendance.vacation.vo.LeaveConfigCarryOverRangeVO;
import com.imile.attendance.vacation.vo.LeaveConfigCarryOverVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface CompanyLeaveConfigCarryOverMapstruct {

    CompanyLeaveConfigCarryOverMapstruct INSTANCE = Mappers.getMapper(CompanyLeaveConfigCarryOverMapstruct.class);


    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    CompanyLeaveConfigCarryOverDO mapToCompanyLeaveConfigCarryOverDO(CompanyLeaveConfigCarryOverAddCommand addCommand);

    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    List<CompanyLeaveConfigCarryOverRangeDO> mapToCompanyLeaveCarryOverRangeConfigDO(List<CompanyLeaveConfigCarryOverRangeAddCommand> addCommand);

    // VO转换
    LeaveConfigCarryOverVO mapToCompanyLeaveConfigCarryOverVO(CompanyLeaveConfigCarryOverDO companyLeaveConfigCarryOverDO);

    List<LeaveConfigCarryOverRangeVO> mapToCompanyLeaveConfigCarryOverRangeVO(List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeList);
}
