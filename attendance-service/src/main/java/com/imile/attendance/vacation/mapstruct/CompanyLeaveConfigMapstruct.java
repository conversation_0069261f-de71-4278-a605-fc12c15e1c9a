package com.imile.attendance.vacation.mapstruct;


import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.vacation.command.CompanyLeaveConfigAddCommand;
import com.imile.attendance.vacation.dto.CompanyLeaveDetailDTO;
import com.imile.attendance.vacation.vo.CompanyLeaveConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface CompanyLeaveConfigMapstruct {

    CompanyLeaveConfigMapstruct INSTANCE = Mappers.getMapper(CompanyLeaveConfigMapstruct.class);


    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    @Mapping(target = "dispatchCountry", ignore = true)
    CompanyLeaveConfigDO mapToCompanyLeaveConfigDO(CompanyLeaveConfigAddCommand addCommand);

    List<CompanyLeaveDetailDTO> toConfigDetailDTO(List<CompanyLeaveConfigDO> configDO);

    // VO转换
    CompanyLeaveConfigVO mapToCompanyLeaveConfigVO(CompanyLeaveConfigDO companyLeaveConfigDO);

}
