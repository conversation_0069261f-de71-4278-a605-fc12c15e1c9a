package com.imile.attendance.vacation.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-6-15
 * @version: 1.0
 */
@Data
public class CompanyLeaveConfigDTO implements Serializable {
    private static final long serialVersionUID = -1757248873857487030L;

    /**
     * 所属国
     */
    private String country;
    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 是否派遣假
     */
    private Integer isDispatch;
    /**
     * 派遣地国家
     */
    private String dispatchCountry;
    /**
     * 适用性别
     */
    private Integer useSex;
    /**
     * 更新周期
     */
    private String useCycle;
    /**
     * 假期可用的开始时间
     */
    private String useStartDate;
    /**
     * 假期可用的结束时间
     */
    private String useEndDate;
    /**
     * 状态 ACTIVE/DISABLED
     */
    private String status;
    /**
     * 阶段信息
     */
    List<CompanyLeaveItemConfigDTO> itemList;

    /**
     * 假期简称
     */
    private String leaveShortName;

    /**
     * 节假日是否消耗假期 (LeaveConsumeTypeEnum)
     */
    private String consumeLeaveType;

    /**
     * 是否上传附件
     */
    private Integer isUploadAttachment;

    /**
     * 请假单位
     */
    private String leaveUnit;

    /**
     * 最小请假时长
     */
    private Integer miniLeaveDuration;
}
