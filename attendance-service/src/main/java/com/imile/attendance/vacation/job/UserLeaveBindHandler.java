package com.imile.attendance.vacation.job;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.vacation.job.service.UserLeaveBindService;
import com.imile.attendance.vacation.param.UserLeaveBindParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * {@code @author:} allen
 * {@code @className:} UserLeaveBindHandler
 * {@code @since:} 2024-07-10 20:23
 * {@code @description:} 给用户绑定假期范围
 */
@Slf4j
@Component
public class UserLeaveBindHandler {

    @Resource
    UserLeaveBindService userLeaveBindService;

    @XxlJob(BusinessConstant.JobHandler.USER_LEAVE_BIND_HANDLER)
    public ReturnT<String> userLeaveBindHandler(String content) {
        UserLeaveBindParam param = StringUtils.isNotBlank(content) ?
                JSON.parseObject(content, UserLeaveBindParam.class)
                : new UserLeaveBindParam();
        userLeaveBindService.userLeaveBindHandler(param);
        return ReturnT.SUCCESS;
    }

}
