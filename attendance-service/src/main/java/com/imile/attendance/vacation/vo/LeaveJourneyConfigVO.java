package com.imile.attendance.vacation.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * {@code @author:} han.wang
 * {@code @className:} LeaveJourneyConfigSaveParam
 * {@code @since:} 2024-12-12 20:52
 * {@code @description:}
 */
@Data
public class LeaveJourneyConfigVO implements Serializable {
    private static final long serialVersionUID = 1505347024173785186L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 国家发放规则表主键id
     */
    private Long issueRuleId;

    /**
     * 国家
     */
    private String country;

    /**
     * 路程假天数
     */
    private BigDecimal journeyDays;
}
