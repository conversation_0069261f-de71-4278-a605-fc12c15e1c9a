package com.imile.attendance.vacation.query;

import com.imile.common.query.BaseQuery;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-6-15
 * @version: 1.0
 */
@Data
public class CompanyLeaveConfigQuery extends BaseQuery implements Serializable {
    private static final long serialVersionUID = -4592374632357103390L;

    /**
     * 所属国
     */
    private String country;
    /**
     * 假期名称
     */
    private String leaveName;
    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 是否派遣假
     */
    private Integer isDispatch;
    /**
     * 状态
     */
    private String status;
}
