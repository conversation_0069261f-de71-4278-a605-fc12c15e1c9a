package com.imile.attendance.vacation;

import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.vacation.dao.UserLeaveConfigHistoryDao;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.model.UserLeaveConfigHistoryDO;
import com.imile.attendance.util.BaseDOUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户假期记录业务层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/27
 */
@Service
@Slf4j
public class UserLeaveConfigHistoryService {

    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private UserLeaveConfigHistoryDao userLeaveConfigHistoryDao;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    /**
     * 获取当前用户需要删除的历史假期范围
     *
     * @param userId
     * @return
     */
    public List<UserLeaveConfigHistoryDO> getUserLeaveHistoryByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            return Lists.newArrayList();
        }
        List<UserLeaveConfigHistoryDO> userLeaveConfigHistoryList = userLeaveConfigHistoryDao.selectLeaveHistoryInfoByUserId(userId);
        for (UserLeaveConfigHistoryDO userLeaveConfigHistoryDO : userLeaveConfigHistoryList) {
            userLeaveConfigHistoryDO.setIsDelete(BusinessConstant.Y);
            BaseDOUtil.fillDOUpdate(userLeaveConfigHistoryDO);
        }
        return userLeaveConfigHistoryList;
    }

    /**
     * 获取当前用户需要新增的历史假期范围
     *
     * @param updateConfigRang
     * @return
     */
    public List<UserLeaveConfigHistoryDO> getAddUserLeaveHistoryByRange(List<CompanyLeaveConfigRangDO> updateConfigRang,
                                                                        AttendanceUser userInfo) {
        if (CollectionUtils.isEmpty(updateConfigRang) || Objects.isNull(userInfo)) {
            return Lists.newArrayList();
        }
        List<Long> leaveIds = updateConfigRang.stream()
                .map(CompanyLeaveConfigRangDO::getLeaveId)
                .collect(Collectors.toList());
        List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigService.getByIdList(leaveIds);
        if (CollectionUtils.isEmpty(companyLeaveConfigList)) {
            return Lists.newArrayList();
        }
        List<UserLeaveConfigHistoryDO> addUserLeaveConfigHistoryList = Lists.newArrayList();
        for (CompanyLeaveConfigDO companyLeaveConfig : companyLeaveConfigList) {
            // 设置新增的历史假期范围
            UserLeaveConfigHistoryDO userLeaveConfigHistory = new UserLeaveConfigHistoryDO();
            userLeaveConfigHistory.setId(defaultIdWorker.nextId());
            userLeaveConfigHistory.setLeaveId(companyLeaveConfig.getId());
            userLeaveConfigHistory.setUserId(userInfo.getId());
            userLeaveConfigHistory.setUserCode(userInfo.getUserCode());
            userLeaveConfigHistory.setLeaveType(companyLeaveConfig.getLeaveType());
            userLeaveConfigHistory.setLeaveCountry(companyLeaveConfig.getCountry());
            BaseDOUtil.fillDOInsert(userLeaveConfigHistory);

            addUserLeaveConfigHistoryList.add(userLeaveConfigHistory);
        }
        return addUserLeaveConfigHistoryList;
    }

    /**
     * 查询人员历史假期范围
     *
     * @param userId
     * @return
     */
    public List<CompanyLeaveConfigDO> selectHistoryLeaveConfigByUser(Long userId) {
        if (Objects.isNull(userId)) {
            return Lists.newArrayList();
        }
        List<UserLeaveConfigHistoryDO> userLeaveConfigHistory = userLeaveConfigHistoryDao.selectLeaveHistoryInfoByUserId(userId);
        if (CollectionUtils.isEmpty(userLeaveConfigHistory)) {
            log.info("selectHistoryLeaveConfigByUser | userLeaveConfigHistory is empty, userId{} ", userId);
            return Lists.newArrayList();
        }
        List<Long> LeaveIds = userLeaveConfigHistory.stream().map(item -> item.getLeaveId()).collect(Collectors.toList());
        List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigService.getByIdList(LeaveIds);
        if (CollectionUtils.isEmpty(companyLeaveConfigList)) {
            log.info("selectHistoryLeaveConfigByUser | companyLeaveConfigList is empty, userId{} ", userId);
            return Lists.newArrayList();
        }
        return companyLeaveConfigList;
    }


}
