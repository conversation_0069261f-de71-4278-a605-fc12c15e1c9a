package com.imile.attendance.vacation.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/2/17
 * @Description 用户假期失效处理参数
 */
@Data
public class UserLeaveInvalidParam {
    /**
     * 所属国
     */
    private String countryList;
    /**
     * 用户编码
     */
    private String userCodes;
    /**
     * 假期类型
     */
    private String leaveName;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 定义当地时间年份
     */
    private Integer localYear;

    /**
     * 定义当地时间的月份
     */
    private Integer localMonth = 12;

    /**
     * 定义当地时间的日
     */
    private Integer localDay = 31;

    /**
     * 当地时间的小时
     */
    private Integer localHour = 23;

    /**
     * 是否启用自定义当前时间：true使用自定义时间。false使用当前时间
     */
    private Boolean isUseCustomLocalTime = false;

    /**
     * 程序逻辑转换
     */
    @NotNull
    private List<String> employeeTypeList = new ArrayList<>();
    @NotNull
    private List<String> userCodeList = new ArrayList<>();
    @NotNull
    private List<String> countryArrayList = new ArrayList<>();
    @NotNull
    List<String> leaveNameList = new ArrayList<>();
}