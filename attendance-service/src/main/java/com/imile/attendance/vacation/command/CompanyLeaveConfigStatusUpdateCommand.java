package com.imile.attendance.vacation.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
public class CompanyLeaveConfigStatusUpdateCommand {

    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;
    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private String status;
}
