package com.imile.attendance.vacation.factory;

import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveRecordDao;
import com.imile.attendance.infrastructure.repository.employee.mapper.UserLeaveRecordMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Slf4j
@Service
public class UserLeaveRecordFactory {
    @Resource
    private UserLeaveRecordDao userLeaveRecordDao;
    @Resource
    private UserLeaveRecordMapper userLeaveRecordMapper;


    /**
     * 保存员工请假、销假流水信息
     *
     * @param recordDO
     * @return
     */
    public boolean save(UserLeaveRecordDO recordDO) {
        return userLeaveRecordDao.save(recordDO);
    }

    public boolean batchSave(List<UserLeaveRecordDO> recordDOList) {
        return userLeaveRecordDao.saveBatch(recordDOList);
    }

    public Integer batchInsert(List<UserLeaveRecordDO> recordDOList) {
        return userLeaveRecordMapper.insertBatchSomeColumn(recordDOList);
    }

    public boolean batchUpdate(List<UserLeaveRecordDO> recordDOList) {
        if (CollectionUtils.isEmpty(recordDOList)) {
            return true;
        }
        return userLeaveRecordDao.updateBatchById(recordDOList);
    }
}
