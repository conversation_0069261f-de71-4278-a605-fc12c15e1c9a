package com.imile.attendance.vacation;

import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户假期详情业务层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/27
 */
@Service
@Slf4j
public class UserLeaveDetailService {

    @Resource
    private UserLeaveDetailDao userLeaveDetailDao;

    /**
     * 查询用户拥有的假期信息
     *
     * @param query
     * @return
     */
    public List<UserLeaveDetailDO> selectUserLeaveDetail(UserLeaveDetailQuery query) {
        return userLeaveDetailDao.selectUserLeaveDetail(query);
    }

    /**
     * 根据用户主键查询用户假期信息
     *
     * @param userId
     * @return
     */
    public List<UserLeaveDetailDO> selectUserLeaveByUserId(Long userId) {
        return userLeaveDetailDao.selectUserLeaveByUserId(userId);
    }

}
