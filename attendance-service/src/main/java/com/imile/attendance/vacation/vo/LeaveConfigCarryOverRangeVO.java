package com.imile.attendance.vacation.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * {@code @author:} han.wang
 * {@code @className:} LeaveConfigCarryOverRangeVO
 * {@code @since:} 2025-04-02 20:55
 * {@code @description:}
 */
@Data
public class LeaveConfigCarryOverRangeVO implements Serializable {

    private static final long serialVersionUID = -3401524790631229006L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 国家假期规则结转表主键id
     */
    private Long carryOverId;

    /**
     * 左边符号：1：表示大于 2：表示大于等于
     */
    private Integer symbolLeft;

    /**
     * 左边日期(Mdd)：示例：0124
     */
    private Integer entryDateLeft;

    /**
     * 右边符号：1：表示小于 2：表示小于等于
     */
    private Integer symbolRight;

    /**
     * 右边日期(Mdd)：示例：0124
     */
    private Integer entryDateRight;

    /**
     * 结转失效年：0：次年 1：第三年 2：第四年
     */
    private Integer invalidYear;

    /**
     * 失效日期(Mdd)：示例：0124
     */
    private Integer invalidDate;
}
