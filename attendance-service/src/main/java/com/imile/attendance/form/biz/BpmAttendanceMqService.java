package com.imile.attendance.form.biz;

import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;

/**
 * <AUTHOR> chen
 * @Date 2025/3/23 
 * @Description
 */
public interface BpmAttendanceMqService {

    /**
     * 监听考勤(请假/外勤/补卡)返回结果处理
     */
    void attendanceMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO, String operateTags);

    /**
     * 监听考勤(请假-撤销/外勤-撤销/补卡-撤销)返回结果处理
     */
    void attendanceRevokeMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO);

    /**
     * 监听考勤(加班)返回结果处理
     */
    void attendanceOvertimeMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO);
}
