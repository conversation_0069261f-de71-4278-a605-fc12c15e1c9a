package com.imile.attendance.form.biz.overtime.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * {@code @author:} allen
 * {@code @className:} OverTimeDetailParam
 * {@code @since:} 2024-06-13 20:56
 * {@code @description:}
 */
@Data
public class OverTimeDetailParam {
    /**
     * 加班申请单据ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long approvalFormId;

}
