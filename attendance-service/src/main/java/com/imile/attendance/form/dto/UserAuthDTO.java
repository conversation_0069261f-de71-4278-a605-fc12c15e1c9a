package com.imile.attendance.form.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: han.wang
 * @createDate: 2025-01-13
 * @version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserAuthDTO {

    /**
     * 是否系统管理员
     */
    private Boolean isAdmin;


    /**
     * 部门Id权限集合
     */
    private List<Long> deptIds;
}
