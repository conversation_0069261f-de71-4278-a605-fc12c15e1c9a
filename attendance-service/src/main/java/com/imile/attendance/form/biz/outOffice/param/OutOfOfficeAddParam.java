package com.imile.attendance.form.biz.outOffice.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.form.param.BaseFormParam;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-26
 * @version: 1.0
 */
@Data
public class OutOfOfficeAddParam extends BaseFormParam {

    /**
     * 异常考勤ID
     */
    private Long abnormalId;

    /**
     * 外勤开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outOfOfficeStartDate;

    /**
     * 外勤结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outOfOfficeEndDate;

}
