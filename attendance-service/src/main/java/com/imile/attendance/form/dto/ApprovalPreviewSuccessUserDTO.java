package com.imile.attendance.form.dto;

import lombok.Data;

/**
 * @description: 节点找到的人员信息
 * @author: taokang
 * @createDate: 2023-7-28
 * @version: 1.0
 */
@Data
public class ApprovalPreviewSuccessUserDTO {
    /**
     * 用户信息
     */
    private String userMessage;

    /**
     * 用户对应HR信息
     */
    private String userHrMessage;

    /**
     * 是指定人员还是角色人员   1指定人员  2  角色人员
     */
    private Integer temp;

    /**
     * 角色人员找到的员工
     */
    private String userCode;

    /**
     * 取值对象
     */
    private String fetchObject;

    /**
     * 取值对象对应字段值
     */
    private String fetchObjectValue;

    /**
     * 审批角色
     */
    private String approvalRole;
}
