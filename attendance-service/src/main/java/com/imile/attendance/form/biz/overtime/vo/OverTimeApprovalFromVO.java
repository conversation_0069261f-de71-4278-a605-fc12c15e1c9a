package com.imile.attendance.form.biz.overtime.vo;

import lombok.Data;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} OverTimeApprovalFromlVO
 * {@code @since:} 2024-06-14 10:21
 * {@code @description:}
 */
@Data
public class OverTimeApprovalFromVO {

    /**
     * 申请单据id
     */
    private Long id;

    /**
     * 提交人编码
     */
    private String applyUserCode;

    /**
     * 申请单号
     */
    private String applicationCode;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 审批单ID
     */
    private Long approvalId;

    /**
     * 审批节点信息
     */
    private String approvalProcessInfo;

    /**
     * 单据来源: 0:手动创建（就一条数据） 1:导入（多条数据） 【两个的展示样式不一样，但是结果同一返回，前端取对应字段即可】
     */
    private Integer dataSource;

    /**
     * 申请单据详情信息
     */
    private List<OverTimeApprovalFromDetailVO> detail;

    /**
     * 申请理由
     */
    private String remark;
}
