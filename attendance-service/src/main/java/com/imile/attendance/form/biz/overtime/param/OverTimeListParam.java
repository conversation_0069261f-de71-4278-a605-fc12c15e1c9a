package com.imile.attendance.form.biz.overtime.param;

import com.imile.attendance.query.ResourceQuery;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} OverTimeListParam
 * {@code @since:} 2024-06-14 11:03
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OverTimeListParam extends ResourceQuery {

    /**
     * 申请人编码或姓名
     */
    private String userCodeOrName;

    /**
     * 单据编码
     */
    private String applicationCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 审批类型（现在是加班：OVER_TIME，后面可能有其他的）
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<String> formTypeList;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 部门集合
     */
    private List<Long> deptIdList;

    /**
     * 岗位集合
     */
    private List<Long> postIdList;

    /**
     * 数据来源：0：手动添加 1:导入
     */
    private Integer dataSource;

    /**
     * 提交开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startDate;

    /**
     * 提交结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endDate;

    /**
     * 页面操作来源  HRMS  CLOVER
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String systemSource;

}
