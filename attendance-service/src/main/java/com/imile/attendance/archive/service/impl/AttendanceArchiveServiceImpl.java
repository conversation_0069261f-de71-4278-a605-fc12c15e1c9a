package com.imile.attendance.archive.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.imile.attendance.archive.command.AttendanceArchiveUpdateCommand;
import com.imile.attendance.archive.mapstruct.AttendanceArchiveMapstruct;
import com.imile.attendance.archive.query.AttendanceArchiveListQuery;
import com.imile.attendance.archive.query.RuleModifyRecordQuery;
import com.imile.attendance.archive.service.AttendanceArchiveService;
import com.imile.attendance.archive.vo.AttendanceArchiveDetailVO;
import com.imile.attendance.archive.vo.AttendanceArchiveUpdateConfirmVO;
import com.imile.attendance.archive.vo.AttendanceArchiveVO;
import com.imile.attendance.archive.vo.ModifyRecordModuleVO;
import com.imile.attendance.archive.vo.RuleModifyRecordVO;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.calendar.application.CalendarConfigApplicationService;
import com.imile.attendance.calendar.dto.AttendanceArchiveCalendarUpdateDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.DimissionStatusEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.EntryStatusEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.ModifyRecordModuleEnum;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.enums.punch.PunchCardTypeSearchEnum;
import com.imile.attendance.enums.shift.ShiftTypeEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserClassNatureModifyRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.dto.UserArchiveDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserClassNatureModifyRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserArchiveQuery;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.RuleConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.shift.dao.UserCycleShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.dto.ShiftConfigUpdateToOldDTO;
import com.imile.attendance.infrastructure.repository.shift.model.UserCycleShiftConfigDO;
import com.imile.attendance.rule.OverTimeConfigManage;
import com.imile.attendance.rule.OverTimeConfigService;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.PunchConfigService;
import com.imile.attendance.rule.ReissueCardConfigManage;
import com.imile.attendance.rule.ReissueCardConfigService;
import com.imile.attendance.rule.RuleConfigQueryService;
import com.imile.attendance.rule.application.PunchClassConfigApplicationService;
import com.imile.attendance.rule.dto.AttendanceArchiveRuleConfigUpdateDTO;
import com.imile.attendance.rule.permission.RuleConfigPermissionService;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.factory.AutoShiftConfigFactory;
import com.imile.attendance.shift.param.UserAutoShiftParam;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PageUtil;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.util.lang.I18nUtils;
import com.imile.util.user.UserEvnHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Slf4j
@Service
public class AttendanceArchiveServiceImpl implements AttendanceArchiveService {
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private CountryService countryService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService attendancePostService;
    @Resource
    private RuleConfigQueryService ruleConfigQueryService;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private CalendarConfigRangeDao calendarConfigRangeDao;
    @Resource
    private CalendarConfigDao calendarConfigDao;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;
    @Resource
    private OverTimeConfigManage overTimeConfigManage;
    @Resource
    private UserCycleShiftConfigDao userCycleShiftConfigDao;
    @Resource
    private PunchClassConfigApplicationService punchClassConfigApplicationService;
    @Resource
    private CalendarConfigApplicationService calendarConfigApplicationService;
    @Resource
    private AutoShiftConfigFactory autoShiftConfigFactory;
    @Resource
    private UserShiftConfigDao userShiftConfigDao;
    @Resource
    private PunchConfigService punchConfigService;
    @Resource
    private ReissueCardConfigService reissueCardConfigService;
    @Resource
    private OverTimeConfigService overTimeConfigService;
    @Resource
    private UserEntryRecordDao userEntryRecordDao;
    @Resource
    private UserDimissionRecordDao userDimissionRecordDao;
    @Resource
    private LogRecordService logRecordService;
    @Resource
    private UserClassNatureModifyRecordDao userClassNatureModifyRecordDao;
    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Resource
    private RuleConfigPermissionService ruleConfigPermissionService;


    @Override
    public PageInfo<UserArchiveDTO> selectArchiveListPage(AttendanceArchiveListQuery query) {
        // 校验、构建查询入参
        if (!buildAttendanceArchivePageQuery(query)) {
            return null;
        }
        // 分页查询员工档案
        UserArchiveQuery userArchiveQuery = AttendanceArchiveMapstruct.INSTANCE.toQuery(query);
        PageInfo<UserArchiveDTO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> userInfoDao.userAttendanceArchive(userArchiveQuery));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return null;
        }
        return pageInfo;
    }

    @Override
    public PaginationResult<AttendanceArchiveVO> page(AttendanceArchiveListQuery query) {
        // 查询员工档案列表DTO
        PageInfo<UserArchiveDTO> pageInfo = selectArchiveListPage(query);
        if (Objects.isNull(pageInfo)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // VO实体转换
        List<AttendanceArchiveVO> result = convertAttendanceArchiveVOS(pageInfo.getList(), query.getArePageExport());
        return PageUtil.getPageResult(result, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }


    @Override
    public AttendanceArchiveDetailVO detail(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            throw BusinessException.get(ErrorCodeEnum.PARAM_NOT_NULL.getCode(), I18nUtils.getMessage(ErrorCodeEnum.PARAM_NOT_NULL.getDesc()));
        }
        UserDTO userDTO = Optional.ofNullable(userInfoDao.getUserByCode(userCode))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc())));

        AttendanceDept attendanceDept = Optional.ofNullable(deptService.getByDeptId(userDTO.getDeptId())).orElse(new AttendanceDept());
        AttendancePost attendancePost = Optional.ofNullable(attendancePostService.getByPostId(userDTO.getPostId())).orElse(new AttendancePost());

        AttendanceArchiveDetailVO result = new AttendanceArchiveDetailVO();
        //员工基本信息组装
        result.setEmployeeBaseInfo(buildEmployeeBaseInfo(userDTO, attendanceDept, attendancePost));

        //考勤配置组装
        result.setAttendanceRuleConfig(buildAttendanceRuleConfigInfo(userDTO));
        return result;
    }

    @Override
    public AttendanceArchiveUpdateConfirmVO updatePreProcessor(AttendanceArchiveUpdateCommand command) {
        UserDTO userDTO = Optional.ofNullable(userInfoDao.getUserByCode(command.getUserCode()))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc())));
        if (Objects.equals(BusinessConstant.Y, userDTO.getIsDriver())) {
            throw BusinessException.get(ErrorCodeEnum.DRIVER_PROHIBIT_UPDATE_ARCHIVE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.DRIVER_PROHIBIT_UPDATE_ARCHIVE.getDesc()));
        }
        AttendanceArchiveDetailVO.AttendanceRuleConfigVO attendanceRuleConfigVO = buildAttendanceRuleConfigInfo(userDTO);

        AttendanceArchiveUpdateConfirmVO result = new AttendanceArchiveUpdateConfirmVO();
        result.setClassNature(command.getClassNature());

        if (!Objects.equals(command.getCalendarId(), attendanceRuleConfigVO.getCalendarId())) {
            result.setCalendarChange(Boolean.TRUE);
        }
        if (Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), command.getClassNature())) {
            List<UserCycleShiftConfigDO> userCycleShiftConfigDOS = userCycleShiftConfigDao.selectByUserIdList(Collections.singletonList(userDTO.getId()));
            if (CollectionUtils.isNotEmpty(userCycleShiftConfigDOS)) {
                result.setIsCycleShift(Boolean.TRUE);
            }
        }
        return result;
    }

    @Override
    public void update(AttendanceArchiveUpdateCommand command) {
        UserDTO userDTO = Optional.ofNullable(userInfoDao.getUserByCode(command.getUserCode()))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc())));
        if (Objects.equals(BusinessConstant.Y, userDTO.getIsDriver())) {
            throw BusinessException.get(ErrorCodeEnum.DRIVER_PROHIBIT_UPDATE_ARCHIVE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.DRIVER_PROHIBIT_UPDATE_ARCHIVE.getDesc()));
        }
        AttendanceArchiveDetailVO.AttendanceRuleConfigVO attendanceRuleConfigVO = buildAttendanceRuleConfigInfo(userDTO);

        CountryDTO countryDTO = countryService.queryCountry(userDTO.getLocationCountry());
        DateAndTimeZoneDate currentDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(new Date());

        //日历是否变更
        boolean calendarChange = !Objects.equals(command.getCalendarId(), attendanceRuleConfigVO.getCalendarId());

        //班次性质变更
        if (!Objects.equals(command.getClassNature(), attendanceRuleConfigVO.getClassNature())) {
            classNatureChangeHandler(command, userDTO, attendanceRuleConfigVO, currentDateAndTimeZoneDate, calendarChange);
        } else {
            //班次性质没变 日历变
            calendarChangeHandler(command, userDTO, attendanceRuleConfigVO, currentDateAndTimeZoneDate, calendarChange);
        }

        //打卡规则变更
        if (!Objects.equals(command.getPunchConfigId(), attendanceRuleConfigVO.getPunchConfigId())) {
            DateAndTimeZoneDate punchConfigDate = currentDateAndTimeZoneDate;
            if (!Objects.equals(userDTO.getLocationCountry(), command.getPunchConfigCountry())) {
                CountryDTO punchConfigCountry = countryService.queryCountry(command.getPunchConfigCountry());
                punchConfigDate = punchConfigCountry.getDateAndTimeZoneDate(new Date());
            }
            AttendanceArchiveRuleConfigUpdateDTO ruleConfigUpdateDTO = AttendanceArchiveRuleConfigUpdateDTO.builder()
                    .userId(userDTO.getId())
                    .newConfigId(command.getPunchConfigId())
                    .oldConfigId(attendanceRuleConfigVO.getPunchConfigId())
                    .startDateAndTimeZoneDate(punchConfigDate)
                    .build();
            punchConfigService.userPunchConfigRangeUpdate(ruleConfigUpdateDTO);
        }

        //补卡规则变更
        if (!Objects.equals(command.getReissueCardConfigId(), attendanceRuleConfigVO.getReissueCardConfigId())) {
            DateAndTimeZoneDate reissueCardConfigDate = currentDateAndTimeZoneDate;
            if (!Objects.equals(userDTO.getLocationCountry(), command.getReissueCardConfigCountry())) {
                CountryDTO reissueCardConfigCountry = countryService.queryCountry(command.getReissueCardConfigCountry());
                reissueCardConfigDate = reissueCardConfigCountry.getDateAndTimeZoneDate(new Date());
            }
            AttendanceArchiveRuleConfigUpdateDTO ruleConfigUpdateDTO = AttendanceArchiveRuleConfigUpdateDTO.builder()
                    .userId(userDTO.getId())
                    .newConfigId(command.getReissueCardConfigId())
                    .oldConfigId(attendanceRuleConfigVO.getReissueCardConfigId())
                    .startDateAndTimeZoneDate(reissueCardConfigDate)
                    .build();
            reissueCardConfigService.userReissueCardConfigRangeUpdate(ruleConfigUpdateDTO);
        }

        //加班规则变更
        if (!Objects.equals(command.getOverTimeConfigId(), attendanceRuleConfigVO.getOverTimeConfigId())) {
            DateAndTimeZoneDate overTimeConfigDate = currentDateAndTimeZoneDate;
            if (!Objects.equals(userDTO.getLocationCountry(), command.getOverTimeConfigCountry())) {
                CountryDTO overTimeConfigCountry = countryService.queryCountry(command.getOverTimeConfigCountry());
                overTimeConfigDate = overTimeConfigCountry.getDateAndTimeZoneDate(new Date());
            }
            AttendanceArchiveRuleConfigUpdateDTO ruleConfigUpdateDTO = AttendanceArchiveRuleConfigUpdateDTO.builder()
                    .userId(userDTO.getId())
                    .newConfigId(command.getOverTimeConfigId())
                    .oldConfigId(attendanceRuleConfigVO.getOverTimeConfigId())
                    .startDateAndTimeZoneDate(overTimeConfigDate)
                    .build();
            overTimeConfigService.userOverTimeConfigRangeUpdate(ruleConfigUpdateDTO);
        }
    }

    @Override
    public List<ModifyRecordModuleVO> moduleSelect() {
        List<ModifyRecordModuleVO> result = new ArrayList<>();
        for (ModifyRecordModuleEnum value : ModifyRecordModuleEnum.values()) {
            ModifyRecordModuleVO modifyRecordModuleVO = new ModifyRecordModuleVO();
            modifyRecordModuleVO.setCode(value.getCode());
            if (Locale.US.equals(UserEvnHolder.getLocal())) {
                modifyRecordModuleVO.setDesc(value.getDescEn());
            } else {
                modifyRecordModuleVO.setDesc(value.getDesc());
            }
            result.add(modifyRecordModuleVO);
        }
        return result;
    }

    @Override
    public List<RuleModifyRecordVO> ruleModifyList(RuleModifyRecordQuery query) {
        ModifyRecordModuleEnum moduleEnum = ModifyRecordModuleEnum.getInstance(query.getModule());
        if (Objects.isNull(moduleEnum)) {
            throw BusinessException.get(ErrorCodeEnum.MODULE_INVALID.getCode(), I18nUtils.getMessage(ErrorCodeEnum.MODULE_INVALID.getDesc()));
        }
        switch (moduleEnum) {
            case CLASS_NATURE:
                List<UserClassNatureModifyRecordDO> modifyRecordDOList = userClassNatureModifyRecordDao.selectByUserId(query.getUserId());
                if (CollectionUtils.isEmpty(modifyRecordDOList)) {
                    return Collections.emptyList();
                }
                UserInfoDO userInfoDO = userInfoDao.getByUserId(modifyRecordDOList.get(0).getUserId());
                if (Objects.isNull(userInfoDO)) {
                    return Collections.emptyList();
                }
                return modifyRecordDOList.stream().map(record -> {
                    RuleModifyRecordVO recordVO = new RuleModifyRecordVO();
                    recordVO.setCountry(userInfoDO.getLocationCountry());
                    recordVO.setRuleName(ClassNatureEnum.getLocalizedDesc(record.getClassNature()));
                    recordVO.setEffectTime(record.getEffectTime());
                    recordVO.setExpireTime(record.getExpireTime());
                    recordVO.setOperator(record.getCreateUserName());
                    return recordVO;
                }).collect(Collectors.toList());
            case CALENDAR:
                List<CalendarConfigModifyDTO> modifyDTOList = calendarManage.selectAllByBizId(query.getUserId());
                if (CollectionUtils.isEmpty(modifyDTOList)) {
                    return Collections.emptyList();
                }
                return modifyDTOList.stream().map(record -> {
                    RuleModifyRecordVO recordVO = new RuleModifyRecordVO();
                    recordVO.setCountry(record.getCountry());
                    recordVO.setRuleName(record.getCalendarName());
                    recordVO.setEffectTime(record.getStartDate());
                    recordVO.setExpireTime(record.getEndDate());
                    recordVO.setOperator(record.getCreateUserName());
                    return recordVO;
                }).collect(Collectors.toList());
            case CLASS:
                List<RuleConfigModifyDTO> classModifyDTOList = punchClassConfigManage.selectAllByBizId(query.getUserId());
                if (CollectionUtils.isEmpty(classModifyDTOList)) {
                    return Collections.emptyList();
                }
                return classModifyDTOList.stream().map(record -> {
                    RuleModifyRecordVO recordVO = new RuleModifyRecordVO();
                    recordVO.setCountry(record.getCountry());
                    recordVO.setRuleName(record.getRuleName());
                    recordVO.setEffectTime(record.getStartDate());
                    recordVO.setExpireTime(record.getEndDate());
                    recordVO.setOperator(record.getCreateUserName());
                    return recordVO;
                }).collect(Collectors.toList());
            case PUNCH_CONFIG:
                List<RuleConfigModifyDTO> punchConfigModifyDTOList = punchConfigManage.selectAllByBizId(query.getUserId());
                if (CollectionUtils.isEmpty(punchConfigModifyDTOList)) {
                    return Collections.emptyList();
                }
                return punchConfigModifyDTOList.stream().map(record -> {
                    RuleModifyRecordVO recordVO = new RuleModifyRecordVO();
                    recordVO.setCountry(record.getCountry());
                    recordVO.setRuleName(record.getRuleName());
                    recordVO.setEffectTime(record.getStartDate());
                    recordVO.setExpireTime(record.getEndDate());
                    recordVO.setOperator(record.getCreateUserName());
                    return recordVO;
                }).collect(Collectors.toList());
            case REISSUE_CARD_CONFIG:
                List<RuleConfigModifyDTO> reissueCardModifyDTOList = reissueCardConfigManage.selectAllByBizId(query.getUserId());
                if (CollectionUtils.isEmpty(reissueCardModifyDTOList)) {
                    return Collections.emptyList();
                }
                return reissueCardModifyDTOList.stream().map(record -> {
                    RuleModifyRecordVO recordVO = new RuleModifyRecordVO();
                    recordVO.setCountry(record.getCountry());
                    recordVO.setRuleName(record.getRuleName());
                    recordVO.setEffectTime(record.getStartDate());
                    recordVO.setExpireTime(record.getEndDate());
                    recordVO.setOperator(record.getCreateUserName());
                    return recordVO;
                }).collect(Collectors.toList());
            case OVERTIME_CONFIG:
                List<RuleConfigModifyDTO> overTimeModifyDTOList = overTimeConfigManage.selectAllByBizId(query.getUserId());
                if (CollectionUtils.isEmpty(overTimeModifyDTOList)) {
                    return Collections.emptyList();
                }
                return overTimeModifyDTOList.stream().map(record -> {
                    RuleModifyRecordVO recordVO = new RuleModifyRecordVO();
                    recordVO.setCountry(record.getCountry());
                    recordVO.setRuleName(record.getRuleName());
                    recordVO.setEffectTime(record.getStartDate());
                    recordVO.setExpireTime(record.getEndDate());
                    recordVO.setOperator(record.getCreateUserName());
                    return recordVO;
                }).collect(Collectors.toList());
            default:
                log.info("module is invalid");
        }
        return Collections.emptyList();
    }

    private void calendarChangeHandler(AttendanceArchiveUpdateCommand command,
                                       UserDTO userDTO,
                                       AttendanceArchiveDetailVO.AttendanceRuleConfigVO attendanceRuleConfigVO,
                                       DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                       boolean calendarChange) {
        if (!calendarChange) {
            return;
        }

        DateAndTimeZoneDate calendarDate = currentDateAndTimeZoneDate;
        if (!Objects.equals(userDTO.getLocationCountry(), command.getCalendarCountry())) {
            CountryDTO calendarCountry = countryService.queryCountry(command.getCalendarCountry());
            calendarDate = calendarCountry.getDateAndTimeZoneDate(new Date());
        }
        Long startDayId = DateHelper.getDayId(calendarDate.getTimeZoneDate());

        AttendanceArchiveCalendarUpdateDTO calendarUpdateDTO = AttendanceArchiveCalendarUpdateDTO.builder()
                .userId(userDTO.getId())
                .oldCalendarId(attendanceRuleConfigVO.getCalendarId())
                .newCalendarId(command.getCalendarId())
                .startDate(calendarDate.getTimeZoneDate())
                .build();
        calendarConfigApplicationService.userCalendarRangeUpdate(calendarUpdateDTO);

        //清理用户旧日历之前的排班计划,多班次场景日历会作用用户多个自定义排班 循环排班除外
        if (Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), userDTO.getClassNature())) {
            ShiftConfigUpdateToOldDTO updateToOldDTO = ShiftConfigUpdateToOldDTO.builder()
                    .userId(userDTO.getId())
                    .attendanceConfigId(attendanceRuleConfigVO.getCalendarId())
                    .startDayId(startDayId)
                    .conditionShiftTypeList(Lists.newArrayList(ShiftTypeEnum.CUSTOM_SHIFT.getCode()))
                    .build();
            userShiftConfigDao.updateToOld(updateToOldDTO);
            return;
        }

        //固定班次会按照最高优先级班次自动排班，给新日历自动排班的过程会清理旧日历的排班计划
        Map<Long, PunchClassConfigDO> punchClassConfigMap = punchClassConfigManage.selectTopPriorityByUserIds(Collections.singletonList(userDTO.getId()));
        if (MapUtils.isNotEmpty(punchClassConfigMap)) {
            PunchClassConfigDO punchClassConfigDO = punchClassConfigMap.get(userDTO.getId());
            UserAutoShiftParam userAutoShiftParam = new UserAutoShiftParam();
            userAutoShiftParam.setClassNatureEnum(ClassNatureEnum.FIXED_CLASS);
            UserAutoShiftParam.PunchClassAddUserParam punchClassAddUserParam = new UserAutoShiftParam.PunchClassAddUserParam();
            punchClassAddUserParam.setUserIdList(Collections.singletonList(userDTO.getId()));
            punchClassAddUserParam.setTargetClassId(punchClassConfigDO.getId());
            punchClassAddUserParam.setShiftStartDayId(startDayId);
            userAutoShiftParam.setPunchClassAddUserParam(punchClassAddUserParam);
            autoShiftConfigFactory.autoShift(userAutoShiftParam);
        }
    }

    private void classNatureChangeHandler(AttendanceArchiveUpdateCommand command,
                                          UserDTO userDTO,
                                          AttendanceArchiveDetailVO.AttendanceRuleConfigVO attendanceRuleConfigVO,
                                          DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                          boolean calendarChange) {
        if (calendarChange) {
            DateAndTimeZoneDate calendarDate = currentDateAndTimeZoneDate;
            if (!Objects.equals(userDTO.getLocationCountry(), command.getCalendarCountry())) {
                CountryDTO calendarCountry = countryService.queryCountry(command.getCalendarCountry());
                calendarDate = calendarCountry.getDateAndTimeZoneDate(new Date());
            }
            //日历变了 解绑旧日历 绑定新日历
            AttendanceArchiveCalendarUpdateDTO calendarUpdateDTO = AttendanceArchiveCalendarUpdateDTO.builder()
                    .userId(userDTO.getId())
                    .oldCalendarId(attendanceRuleConfigVO.getCalendarId())
                    .newCalendarId(command.getCalendarId())
                    .startDate(calendarDate.getTimeZoneDate())
                    .build();
            calendarConfigApplicationService.userCalendarRangeUpdate(calendarUpdateDTO);
        }
        punchClassConfigApplicationService.classNatureSwitchHandler(userDTO.getId(), attendanceRuleConfigVO.getClassNature(), command.getClassNature());
    }


    @Nullable
    private Boolean buildAttendanceArchivePageQuery(AttendanceArchiveListQuery query) {
        List<String> locationCountryList = new ArrayList<>();
        if (StringUtils.isNotBlank(query.getLocationCountry())) {
            locationCountryList.add(query.getLocationCountry());
        }
        Boolean isChooseDept = Boolean.FALSE;
        PermissionCountryDeptVO permissionDept = ruleConfigPermissionService.getPermissionCountryDeptVO(query.getDeptIdList(), locationCountryList);
        log.info("AttendanceArchiveService.page permissionDept:{}", JSON.toJSONString(query));

        // 如果传入部门，就按照部门查询
        if (CollUtil.isNotEmpty(query.getDeptIdList())) {
            // 设置标志
            isChooseDept = Boolean.TRUE;
        }

        // 如果传入国家，就按照国家查询
        if (StringUtils.isNotBlank(query.getLocationCountry()) && !isChooseDept) {
            permissionDept.setDeptIdList(new ArrayList<>());
            permissionDept.setHasDeptPermission(false);
            if (CollectionUtils.isEmpty(permissionDept.getCountryList())) {
                return false;
            }
        }
        query.setAuthLocationCountryList(permissionDept.getCountryList());
        query.setDeptIdList(permissionDept.getDeptIdList());
        query.setHasDeptPermission(permissionDept.getHasDeptPermission());
        query.setHasCountryPermission(permissionDept.getHasCountryPermission());
        query.setHasOrDeptAndCountryPermission(permissionDept.getHasOrDeptAndCountryPermission());
        query.setHasAndDeptAndCountryPermission(permissionDept.getHasAndDeptAndCountryPermission());
        query.setIsChooseDept(isChooseDept);

        dealWithQueryCountryAndEmployeeType(query, permissionDept);

        if (CollectionUtils.isEmpty(query.getNormalCountryList()) && CollectionUtils.isEmpty(query.getSpecialCountryList())) {
            return false;
        }

        Set<Long> conditionUserIds = new HashSet<>();
        boolean flag = false;
        if (CollectionUtils.isNotEmpty(query.getUserIdList())) {
            conditionUserIds.addAll(query.getUserIdList());
            flag = true;
        }

        if (StringUtils.isNotEmpty(query.getPunchCardType())) {
            PunchCardTypeSearchEnum cardTypeSearchEnum = PunchCardTypeSearchEnum.getInstance(query.getPunchCardType());
            switch (Objects.requireNonNull(cardTypeSearchEnum)) {
                case FACE:
                    query.setPunchCardTypeList(PunchCardTypeSearchEnum.getFaceList());
                    break;
                case PALM:
                    query.setPunchCardTypeList(PunchCardTypeSearchEnum.getPalmList());
                    break;
                case FINGER:
                    query.setPunchCardTypeList(PunchCardTypeSearchEnum.getFingerList());
                    break;
                case PASSWORD:
                    query.setPunchCardTypeList(PunchCardTypeSearchEnum.getPasswordList());
                    break;
                default:
                    query.setPunchCardTypeList(Collections.singletonList(query.getPunchCardType()));
            }
            //关联查询打卡记录表近一个月的数据
            Date date = new Date();
            Long startDayId = DateHelper.getDayId(date);
            Long endDayId = DateHelper.getDayId(DateUtil.offsetMonth(date, -1));
            List<Long> punchCardUserIds = employeePunchRecordDao.selectUserIdByPunchCardTypes(startDayId, endDayId, query.getPunchCardTypeList());
            if (CollectionUtils.isEmpty(punchCardUserIds)) {
                return false;
            }
            if (!flag) {
                conditionUserIds.addAll(punchCardUserIds);
                flag = true;
            } else {
                conditionUserIds = Sets.intersection(conditionUserIds, new HashSet<>(punchCardUserIds));
                if (CollectionUtils.isEmpty(conditionUserIds)) {
                    return false;
                }
            }
        }

        if (Objects.nonNull(query.getCalendarSelect())) {
            List<Long> calendarUserIds = ruleConfigQueryService.queryRuleConfigUsers(query.getCalendarSelect());
            if (CollectionUtils.isEmpty(calendarUserIds)) {
                return false;
            }
            if (!flag) {
                conditionUserIds.addAll(calendarUserIds);
                flag = true;
            } else {
                conditionUserIds = Sets.intersection(conditionUserIds, new HashSet<>(calendarUserIds));
                if (CollectionUtils.isEmpty(conditionUserIds)) {
                    return false;
                }
            }
        }

        if (Objects.nonNull(query.getPunchConfigSelect())) {
            List<Long> punchConfigUserIds = ruleConfigQueryService.queryRuleConfigUsers(query.getPunchConfigSelect());
            if (CollectionUtils.isEmpty(punchConfigUserIds)) {
                return false;
            }
            if (!flag) {
                conditionUserIds.addAll(punchConfigUserIds);
                flag = true;
            } else {
                conditionUserIds = Sets.intersection(conditionUserIds, new HashSet<>(punchConfigUserIds));
                if (CollectionUtils.isEmpty(conditionUserIds)) {
                    return false;
                }
            }
        }
        if (Objects.nonNull(query.getReissueCardConfigSelect())) {
            List<Long> reissueCardConfigUserIds = ruleConfigQueryService.queryRuleConfigUsers(query.getReissueCardConfigSelect());
            if (CollectionUtils.isEmpty(reissueCardConfigUserIds)) {
                return false;
            }
            if (!flag) {
                conditionUserIds.addAll(reissueCardConfigUserIds);
                flag = true;
            } else {
                conditionUserIds = Sets.intersection(conditionUserIds, new HashSet<>(reissueCardConfigUserIds));
                if (CollectionUtils.isEmpty(conditionUserIds)) {
                    return false;
                }
            }
        }
        if (Objects.nonNull(query.getOverTimeConfigSelect())) {
            List<Long> overTimeConfigUserIds = ruleConfigQueryService.queryRuleConfigUsers(query.getOverTimeConfigSelect());
            if (CollectionUtils.isEmpty(overTimeConfigUserIds)) {
                return false;
            }
            if (!flag) {
                conditionUserIds.addAll(overTimeConfigUserIds);
            } else {
                conditionUserIds = Sets.intersection(conditionUserIds, new HashSet<>(overTimeConfigUserIds));
                if (CollectionUtils.isEmpty(conditionUserIds)) {
                    return false;
                }
            }
        }

        if (conditionUserIds.size() > 0) {
            List<Long> userIdList = new ArrayList<>(conditionUserIds);
            if (conditionUserIds.size() > 1000) {
                // 将ID列表分批，每批1000个
                List<List<Long>> batchedIds = Lists.partition(userIdList, 1000);
                query.setBatchUserIdList(batchedIds);
            }
            query.setUserIdList(userIdList);
        }

        log.info("attendance archive page query:{}", JSON.toJSONString(query));
        return true;
    }

    @NotNull
    private List<AttendanceArchiveVO> convertAttendanceArchiveVOS(List<UserArchiveDTO> userArchiveDTOList, boolean arePageExport) {
        List<Long> deptIds = userArchiveDTOList.stream().map(UserArchiveDTO::getDeptId).distinct().collect(Collectors.toList());
        List<Long> userIds = userArchiveDTOList.stream().map(UserArchiveDTO::getId).distinct().collect(Collectors.toList());
        Map<Long, AttendanceDept> attendanceDeptMap = deptService.listByDeptIds(deptIds).stream().collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));

        Map<Long, PunchConfigDO> punchConfigDOMap = punchConfigManage.getConfigMapByUserIdList(userIds);
        Map<Long, ReissueCardConfigDO> reissueCardConfigDOMap = reissueCardConfigManage.getConfigMapByUserIdList(userIds);
        Map<Long, OverTimeConfigDO> overTimeConfigDOMap = overTimeConfigManage.getConfigMapByUserIdList(userIds);
        List<CalendarConfigRangeDO> calendarConfigRangeDOList = calendarConfigRangeDao.selectConfigRange(userIds);
        Map<Long, Long> calendarConfigRangeMap = calendarConfigRangeDOList
                .stream().collect(Collectors.toMap(CalendarConfigRangeDO::getBizId, CalendarConfigRangeDO::getAttendanceConfigId, (oldValue, newValue) -> newValue));
        List<Long> calendarIds = calendarConfigRangeDOList.stream().map(CalendarConfigRangeDO::getAttendanceConfigId).distinct().collect(Collectors.toList());
        Map<Long, CalendarConfigDO> calendarConfigDOMap = calendarConfigDao.getByCalendarConfigIds(calendarIds)
                .stream().collect(Collectors.toMap(CalendarConfigDO::getId, Function.identity()));

        Map<Long, UserClassConfigDTO> userClassConfigDTOMap;
        Map<Long, AttendancePost> attendancePostMap;
        Map<Long, UserEntryRecordDO> userEntryRecordMap;
        Map<Long, UserDimissionRecordDO> userDimissionRecordMap;
        if (arePageExport) {
            userClassConfigDTOMap = punchClassConfigQueryService.selectUserClassConfigList(userIds)
                    .stream().collect(Collectors.toMap(UserClassConfigDTO::getUserId, Function.identity()));

            List<Long> postIds = userArchiveDTOList.stream().map(UserArchiveDTO::getPostId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            attendancePostMap = attendancePostService.listByPostList(postIds).stream().collect(Collectors.toMap(AttendancePost::getId, Function.identity()));

            userEntryRecordMap = userEntryRecordDao.listByUserIds(userIds).stream()
                    .filter(item -> Objects.equals(EntryStatusEnum.ENTRY.getCode(), item.getEntryStatus()))
                    .collect(Collectors.toMap(UserEntryRecordDO::getUserId, v -> v, (v1, v2) -> v1));

            userDimissionRecordMap = userDimissionRecordDao.listByUserIds(userIds).stream()
                    .filter(item -> Objects.equals(DimissionStatusEnum.DIMISSION.getCode(), item.getDimissionStatus()))
                    .collect(Collectors.toMap(UserDimissionRecordDO::getUserId, v -> v, (v1, v2) -> v1));

            // 添加导出的操作日志
            logRecordService.recordOperation(new UserInfoDO(), LogRecordOptions.builder()
                    .operationType(OperationTypeEnum.EXPORT_ARCHIVE.getCode())
                    .remark(OperationTypeEnum.EXPORT_ARCHIVE.getDesc())
                    .build());

        } else {
            userClassConfigDTOMap = new HashMap<>();
            attendancePostMap = new HashMap<>();
            userEntryRecordMap = new HashMap<>();
            userDimissionRecordMap = new HashMap<>();
        }

        Date date = new Date();
        Long endDayId = DateHelper.getDayId(date);
        Long startDayId = DateHelper.getDayId(DateUtil.offsetMonth(date, -1));

        List<AttendanceArchiveVO> result = userArchiveDTOList.stream().map(item -> {
            AttendanceArchiveVO attendanceArchiveVO = new AttendanceArchiveVO();
            attendanceArchiveVO.setId(item.getId());
            attendanceArchiveVO.setUserCode(item.getUserCode());
            attendanceArchiveVO.setUserName(item.getUserName());
            attendanceArchiveVO.setUserNameEn(item.getUserNameEn());
            attendanceArchiveVO.setDeptId(item.getDeptId());
            attendanceArchiveVO.setLocationCountry(item.getLocationCountry());
            attendanceArchiveVO.setLocationProvince(item.getLocationProvince());
            attendanceArchiveVO.setLocationCity(item.getLocationCity());
            attendanceArchiveVO.setClassNature(item.getClassNature());
            attendanceArchiveVO.setEmployeeType(item.getEmployeeType());
            attendanceArchiveVO.setIsDriver(item.getIsDriver());
            attendanceArchiveVO.setIsGlobalRelocation(item.getIsGlobalRelocation());
            attendanceArchiveVO.setWorkStatus(item.getWorkStatus());
            attendanceArchiveVO.setStatus(item.getStatus());
            attendanceArchiveVO.setLastUpdDate(item.getLastUpdDate());
            attendanceArchiveVO.setLastUpdUserName(item.getLastUpdUserName());
            String deptName;
            String postName;
            if (Locale.US.equals(UserEvnHolder.getLocal())) {
                attendanceArchiveVO.setIsDriverDesc(Objects.equals(BusinessConstant.Y, item.getIsDriver()) ? WhetherEnum.YES.getDescEn() : WhetherEnum.NO.getDescEn());
                attendanceArchiveVO.setIsGlobalRelocationDesc(Objects.equals(BusinessConstant.Y, item.getIsGlobalRelocation()) ? WhetherEnum.YES.getDescEn() : WhetherEnum.NO.getDescEn());
                deptName = attendanceDeptMap.getOrDefault(item.getDeptId(), new AttendanceDept()).getDeptNameEn();
                postName = attendancePostMap.getOrDefault(item.getPostId(), new AttendancePost()).getPostNameEn();
            } else {
                attendanceArchiveVO.setIsDriverDesc(Objects.equals(BusinessConstant.Y, item.getIsDriver()) ? WhetherEnum.YES.getDescCn() : WhetherEnum.NO.getDescCn());
                attendanceArchiveVO.setIsGlobalRelocationDesc(Objects.equals(BusinessConstant.Y, item.getIsGlobalRelocation()) ? WhetherEnum.YES.getDescCn() : WhetherEnum.NO.getDescCn());
                deptName = attendanceDeptMap.getOrDefault(item.getDeptId(), new AttendanceDept()).getDeptNameCn();
                postName = attendancePostMap.getOrDefault(item.getPostId(), new AttendancePost()).getPostNameCn();
            }
            attendanceArchiveVO.setDeptName(deptName);
            attendanceArchiveVO.setPostName(postName);

            UserEntryRecordDO userEntryRecordDO = userEntryRecordMap.get(item.getId());
            if (Objects.nonNull(userEntryRecordDO)) {
                attendanceArchiveVO.setEntryTimeStr(DateHelper.formatYYYYMMDD(userEntryRecordDO.getConfirmDate()));
            }

            UserDimissionRecordDO userDimissionRecordDO = userDimissionRecordMap.get(item.getId());
            if (Objects.nonNull(userDimissionRecordDO)) {
                attendanceArchiveVO.setDimissionTimeStr(DateHelper.formatYYYYMMDD(userDimissionRecordDO.getActualDimissionDate()));
            }

            PunchConfigDO punchConfigDO = punchConfigDOMap.get(item.getId());
            if (Objects.nonNull(punchConfigDO)) {
                attendanceArchiveVO.setPunchConfigId(punchConfigDO.getId());
                attendanceArchiveVO.setPunchConfigName(punchConfigDO.getConfigName());
            }
            ReissueCardConfigDO reissueCardConfigDO = reissueCardConfigDOMap.get(item.getId());
            if (Objects.nonNull(reissueCardConfigDO)) {
                attendanceArchiveVO.setReissueCardConfigId(reissueCardConfigDO.getId());
                attendanceArchiveVO.setReissueCardConfigName(reissueCardConfigDO.getConfigName());
            }
            OverTimeConfigDO overTimeConfigDO = overTimeConfigDOMap.get(item.getId());
            if (Objects.nonNull(overTimeConfigDO)) {
                attendanceArchiveVO.setOverTimeConfigId(overTimeConfigDO.getId());
                attendanceArchiveVO.setOverTimeConfigName(overTimeConfigDO.getConfigName());
            }
            Long calendarId = calendarConfigRangeMap.get(item.getId());
            if (Objects.nonNull(calendarId)) {
                CalendarConfigDO calendarConfigDO = calendarConfigDOMap.getOrDefault(calendarId, new CalendarConfigDO());
                attendanceArchiveVO.setCalendarId(calendarConfigDO.getId());
                attendanceArchiveVO.setCalendarName(calendarConfigDO.getAttendanceConfigName());
            }
            UserClassConfigDTO userClassConfigDTO = userClassConfigDTOMap.get(item.getId());
            if (Objects.nonNull(userClassConfigDTO)) {
                List<String> classNameList = userClassConfigDTO.getClassConfigSelectList().stream().map(PunchClassConfigSelectDTO::getClassName).collect(Collectors.toList());
                attendanceArchiveVO.setClassNameStr(classNameList.toString());
            }

            List<String> punchCardTypeList = employeePunchRecordDao.selectPunchCardTypeByUserCode(item.getUserCode(), startDayId, endDayId);
            if (CollectionUtils.isNotEmpty(punchCardTypeList)) {
                attendanceArchiveVO.setPunchCardTypeStr(StringUtils.join(punchCardTypeList, BusinessConstant.DEFAULT_DELIMITER));
            }
            return attendanceArchiveVO;
        }).collect(Collectors.toList());
        return result;
    }


    private void dealWithQueryCountryAndEmployeeType(AttendanceArchiveListQuery query, PermissionCountryDeptVO permissionDept) {
        //管理员权限
        if (permissionDept.getIsSysAdmin()) {
            handleSysAdminQuery(query);
        } else {
            //非管理员权限
            processCountryAndEmployeeType(query, query.getAuthLocationCountryList(), query.getEmployeeTypeList());
        }
        //如果正常国家或正常国家的用工类型为空，则将特殊国家和其用工类型调换（去除union的条件）
        if (CollectionUtils.isEmpty(query.getNormalCountryList()) || CollectionUtils.isEmpty(query.getNormalEmployeeTypeList())) {
            query.setIsNeedQuerySpecialCountry(false);
            query.setNormalCountryList(query.getSpecialCountryList());
            query.setNormalEmployeeTypeList(query.getSpecialEmployeeTypeList());
        }
    }


    /**
     * 处理系统管理员的查询
     */
    private void handleSysAdminQuery(AttendanceArchiveListQuery query) {
        List<String> authLocationCountryList = query.getAuthLocationCountryList();
        if (CollectionUtils.isNotEmpty(authLocationCountryList)) {
            processCountryAndEmployeeType(query, new ArrayList<>(authLocationCountryList), query.getEmployeeTypeList());
            return;
        }

        // 获取所有国家列表
        List<CountryDTO> allCountries = countryService.listAllCountry();

        // 分离普通国家和特殊国家
        List<String> normalCountryList = allCountries.stream()
                .map(CountryDTO::getCountryShort)
                .filter(countryShort -> !CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(countryShort))
                .distinct()
                .collect(Collectors.toList());

        // 设置查询参数
        query.setIsNeedQuerySpecialCountry(true);
        query.setSpecialCountryList(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT);
        query.setNormalCountryList(normalCountryList);

        // 处理用工类型
        List<String> employeeTypeList = query.getEmployeeTypeList();
        if (CollectionUtils.isEmpty(employeeTypeList)) {
            // 未指定用工类型时使用默认值
            query.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            query.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            return;
        }

        // 指定用工类型时取交集
        Set<String> employeeTypeSet = new HashSet<>(employeeTypeList);
        query.setSpecialEmployeeTypeList(
                new ArrayList<>(Sets.intersection(
                        employeeTypeSet,
                        new HashSet<>(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                ))
        );
        query.setNormalEmployeeTypeList(
                new ArrayList<>(Sets.intersection(
                        employeeTypeSet,
                        new HashSet<>(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                ))
        );
    }

    private void processCountryAndEmployeeType(AttendanceArchiveListQuery query,
                                               List<String> countryList,
                                               List<String> employeeTypeList) {
        // 检查是否包含特殊国家
        boolean hasSpecialCountry = countryList.stream()
                .anyMatch(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT::contains);

        if (hasSpecialCountry) {
            // 特殊国家和普通国家分组
            Map<Boolean, List<String>> countryMap = countryList.stream()
                    .collect(Collectors.groupingBy(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT::contains));

            query.setIsNeedQuerySpecialCountry(true);
            query.setSpecialCountryList(countryMap.get(true));
            query.setNormalCountryList(countryMap.get(false));

            // 处理用工类型
            if (CollectionUtils.isEmpty(employeeTypeList)) {
                // 未指定用工类型时使用默认值
                query.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
                query.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            } else {
                // 指定用工类型时取交集
                query.setSpecialEmployeeTypeList(
                        new ArrayList<>(Sets.intersection(
                                new HashSet<>(employeeTypeList),
                                new HashSet<>(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                        ))
                );
                query.setNormalEmployeeTypeList(
                        new ArrayList<>(Sets.intersection(
                                new HashSet<>(employeeTypeList),
                                new HashSet<>(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                        ))
                );
            }
        } else {
            // 只有普通国家
            query.setIsNeedQuerySpecialCountry(false);
            query.setNormalCountryList(countryList);
            query.setSpecialCountryList(Collections.emptyList());

            if (CollectionUtils.isEmpty(employeeTypeList)) {
                query.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            } else {
                query.setNormalEmployeeTypeList(
                        new ArrayList<>(Sets.intersection(
                                new HashSet<>(employeeTypeList),
                                new HashSet<>(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                        ))
                );
            }
        }
    }


    private void setEmptyResult(AttendanceArchiveListQuery query) {
        query.setIsNeedQuerySpecialCountry(false);
        query.setNormalCountryList(Collections.emptyList());
        query.setSpecialCountryList(Collections.emptyList());
        query.setNormalEmployeeTypeList(Collections.emptyList());
        query.setSpecialEmployeeTypeList(Collections.emptyList());
    }

    private AttendanceArchiveDetailVO.AttendanceRuleConfigVO buildAttendanceRuleConfigInfo(UserDTO userDTO) {
        AttendanceArchiveDetailVO.AttendanceRuleConfigVO attendanceRuleConfig = new AttendanceArchiveDetailVO.AttendanceRuleConfigVO();
        if (Objects.equals(BusinessConstant.Y, userDTO.getIsDriver())) {
            return attendanceRuleConfig;
        }

        attendanceRuleConfig.setClassNature(userDTO.getClassNature());

        //班次名称
        List<UserClassConfigDTO> userClassConfigDTOList = punchClassConfigQueryService.selectUserClassConfigList(Collections.singletonList(userDTO.getId()));
        if (CollectionUtils.isNotEmpty(userClassConfigDTOList)) {
            List<AttendanceArchiveDetailVO.ClassVO> classVOList = userClassConfigDTOList.get(0).getClassConfigSelectList()
                    .stream().map(punchClass -> {
                        AttendanceArchiveDetailVO.ClassVO classVO = new AttendanceArchiveDetailVO.ClassVO();
                        classVO.setClassId(punchClass.getId());
                        classVO.setClassName(punchClass.getClassName());
                        return classVO;
                    })
                    .collect(Collectors.toList());
            attendanceRuleConfig.setClassVOList(classVOList);
        }

        //日历名称
        List<Long> calendarConfigIdList = calendarConfigRangeDao.selectConfigRange(Collections.singletonList(userDTO.getId()))
                .stream().map(CalendarConfigRangeDO::getAttendanceConfigId).collect(Collectors.toList());
        List<CalendarConfigDO> calendarConfigDOList = calendarConfigDao.getByCalendarConfigIds(calendarConfigIdList)
                .stream().filter(calendarConfigDO -> Objects.equals(BusinessConstant.Y, calendarConfigDO.getIsLatest()) && Objects.equals(StatusEnum.ACTIVE.getCode(), calendarConfigDO.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(calendarConfigDOList)) {
            attendanceRuleConfig.setCalendarId(calendarConfigDOList.get(0).getId());
            attendanceRuleConfig.setCalendarName(calendarConfigDOList.get(0).getAttendanceConfigName());
            attendanceRuleConfig.setCalendarCountry(calendarConfigDOList.get(0).getCountry());
        }

        //打卡规则
        Map<Long, PunchConfigDO> punchConfigMap = punchConfigManage.getConfigMapByUserIdList(Collections.singletonList(userDTO.getId()));
        if (MapUtils.isNotEmpty(punchConfigMap)) {
            PunchConfigDO punchConfigDO = punchConfigMap.getOrDefault(userDTO.getId(), new PunchConfigDO());
            attendanceRuleConfig.setPunchConfigId(punchConfigDO.getId());
            attendanceRuleConfig.setPunchConfigNo(punchConfigDO.getConfigNo());
            attendanceRuleConfig.setPunchConfigName(punchConfigDO.getConfigName());
            attendanceRuleConfig.setPunchConfigCountry(punchConfigDO.getCountry());
        }

        //加班规则
        Map<Long, OverTimeConfigDO> overTimeConfigMap = overTimeConfigManage.getConfigMapByUserIdList(Collections.singletonList(userDTO.getId()));
        if (MapUtils.isNotEmpty(overTimeConfigMap)) {
            OverTimeConfigDO overTimeConfigDO = overTimeConfigMap.getOrDefault(userDTO.getId(), new OverTimeConfigDO());
            attendanceRuleConfig.setOverTimeConfigId(overTimeConfigDO.getId());
            attendanceRuleConfig.setOverTimeConfigNo(overTimeConfigDO.getConfigNo());
            attendanceRuleConfig.setOverTimeConfigName(overTimeConfigDO.getConfigName());
            attendanceRuleConfig.setOverTimeConfigCountry(overTimeConfigDO.getCountry());
        }

        //补卡规则
        Map<Long, ReissueCardConfigDO> reissueCardConfigMap = reissueCardConfigManage.getConfigMapByUserIdList(Collections.singletonList(userDTO.getId()));
        if (MapUtils.isNotEmpty(reissueCardConfigMap)) {
            ReissueCardConfigDO reissueCardConfigDO = reissueCardConfigMap.getOrDefault(userDTO.getId(), new ReissueCardConfigDO());
            attendanceRuleConfig.setReissueCardConfigId(reissueCardConfigDO.getId());
            attendanceRuleConfig.setReissueCardConfigNo(reissueCardConfigDO.getConfigNo());
            attendanceRuleConfig.setReissueCardConfigName(reissueCardConfigDO.getConfigName());
            attendanceRuleConfig.setReissueCardConfigCountry(reissueCardConfigDO.getCountry());
        }
        return attendanceRuleConfig;
    }

    private static AttendanceArchiveDetailVO.EmployeeBaseInfoVO buildEmployeeBaseInfo(UserDTO userDTO, AttendanceDept attendanceDept, AttendancePost attendancePost) {
        AttendanceArchiveDetailVO.EmployeeBaseInfoVO employeeBaseInfo = new AttendanceArchiveDetailVO.EmployeeBaseInfoVO();
        employeeBaseInfo.setUserCode(userDTO.getUserCode());
        employeeBaseInfo.setUserName(userDTO.getUserName());
        employeeBaseInfo.setEmployeeType(userDTO.getEmployeeType());
        employeeBaseInfo.setWorkStatus(userDTO.getWorkStatus());
        employeeBaseInfo.setStatus(userDTO.getStatus());
        employeeBaseInfo.setDeptId(userDTO.getDeptId());
        employeeBaseInfo.setPostId(userDTO.getPostId());
        String deptName;
        String postName;
        if (Locale.US.equals(UserEvnHolder.getLocal())) {
            deptName = attendanceDept.getDeptNameEn();
            postName = attendancePost.getPostNameEn();
        } else {
            deptName = attendanceDept.getDeptNameCn();
            postName = attendancePost.getPostNameCn();
        }
        employeeBaseInfo.setDeptName(deptName);
        employeeBaseInfo.setPostName(postName);
        employeeBaseInfo.setIsDriver(userDTO.getIsDriver());
        employeeBaseInfo.setIsGlobalRelocation(userDTO.getIsGlobalRelocation());
        employeeBaseInfo.setLocationCountry(userDTO.getLocationCountry());
        employeeBaseInfo.setLocationProvince(userDTO.getLocationProvince());
        employeeBaseInfo.setLocationCity(userDTO.getLocationCity());
        employeeBaseInfo.setEntryDate(userDTO.getConfirmDate());
        employeeBaseInfo.setDimissionDate(userDTO.getActualDimissionDate());
        return employeeBaseInfo;
    }

}
