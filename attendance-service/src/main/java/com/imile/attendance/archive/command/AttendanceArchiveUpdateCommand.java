package com.imile.attendance.archive.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2025/5/7
 */
@Data
public class AttendanceArchiveUpdateCommand {

    /**
     * 用户编码
     */
    @NotNull(message = "userCode cannot be empty")
    private String userCode;

    /**
     * 班次性质
     */
    @NotNull(message = "classNature cannot be empty")
    private String classNature;

    /**
     * 日历ID
     */
    @NotNull(message = "calendarId cannot be empty")
    private Long calendarId;

    /**
     * 日历所属国
     */
    @NotNull(message = "calendarCountry cannot be empty")
    private String calendarCountry;

    /**
     * 打卡规则ID
     */
    @NotNull(message = "punchConfigId cannot be empty")
    private Long punchConfigId;

    /**
     * 打卡规则所属国
     */
    @NotNull(message = "punchConfigCountry cannot be empty")
    private String punchConfigCountry;

    /**
     * 补卡规则ID
     */
    @NotNull(message = "reissueCardConfigId cannot be empty")
    private Long reissueCardConfigId;

    /**
     * 补卡规则所属国
     */
    @NotNull(message = "reissueCardConfigCountry cannot be empty")
    private String reissueCardConfigCountry;

    /**
     * 加班规则ID
     */
    @NotNull(message = "overTimeConfigId cannot be empty")
    private Long overTimeConfigId;

    /**
     * 加班规则所属国
     */
    @NotNull(message = "overTimeConfigCountry cannot be empty")
    private String overTimeConfigCountry;
}
