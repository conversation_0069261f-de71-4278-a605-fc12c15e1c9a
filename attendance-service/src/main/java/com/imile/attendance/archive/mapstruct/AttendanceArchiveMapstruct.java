package com.imile.attendance.archive.mapstruct;

import com.imile.attendance.archive.query.AttendanceArchiveListQuery;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.employee.query.UserArchiveQuery;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery;
import com.imile.attendance.rule.command.PunchClassConfigAddCommand;
import com.imile.attendance.rule.dto.PunchClassConfigAddDTO;
import com.imile.attendance.rule.query.PunchClassConfigListQuery;
import com.imile.attendance.rule.vo.PunchClassConfigDetailVO;
import com.imile.attendance.rule.vo.PunchClassItemConfigVO;
import com.imile.attendance.util.DateHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Mapper(config = MapperConfiguration.class)
public interface AttendanceArchiveMapstruct {

    AttendanceArchiveMapstruct INSTANCE = Mappers.getMapper(AttendanceArchiveMapstruct.class);


    UserArchiveQuery toQuery(AttendanceArchiveListQuery query);



}
