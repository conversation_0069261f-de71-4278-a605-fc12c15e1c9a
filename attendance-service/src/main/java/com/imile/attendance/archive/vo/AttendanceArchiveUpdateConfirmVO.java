package com.imile.attendance.archive.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Data
public class AttendanceArchiveUpdateConfirmVO implements Serializable {

    /**
     * 是否循环排班
     */
    private Boolean isCycleShift = Boolean.FALSE;

    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 日历是否变化
     */
    private Boolean calendarChange = Boolean.FALSE;


}
