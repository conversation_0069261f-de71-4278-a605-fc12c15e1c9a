package com.imile.attendance.driver.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.driver.DriverAttendanceManage;
import com.imile.attendance.driver.DriverPunchRecordManage;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.DriverAttendanceOperationTypeEnum;
import com.imile.attendance.enums.DriverAttendanceTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceDetailDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailInfoQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailQuery;
import com.imile.attendance.infrastructure.repository.employee.query.DriverQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description 每日生成前一日的司机考勤信息
 */
@Slf4j
@Component
public class DriverAttendanceHandler {


    @Resource
    private AttendanceUserService userService;
    @Autowired
    protected DefaultIdWorker defaultIdWorker;
    @Autowired
    private CountryService countryService;
    @Resource
    private DriverPunchRecordManage driverPunchRecordManage;
    @Resource
    private DriverAttendanceManage driverAttendanceManage;

    @XxlJob(BusinessConstant.JobHandler.DRIVER_ATTENDANCE_DETAIL_HANDLER)
    public ReturnT<String> driverAttendanceHandler(String content) {
        DriverAttendanceHandler.DriverAttendanceParam param =
                StringUtils.isNotBlank(content) ?
                        JSON.parseObject(content, DriverAttendanceHandler.DriverAttendanceParam.class) :
                        new DriverAttendanceHandler.DriverAttendanceParam();

        // 获取当前时间
        Date date = new Date();
        if (StringUtils.isNotBlank(param.getDateNow())) {
            date = DateUtil.parse(param.getDateNow(), "yyyy-MM-dd HH:mm:ss");
        }
        List<String> countryList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getCountryList())) {
            countryList = Arrays.asList(param.getCountryList().split(","));
        }
        List<String> driverCodeList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(param.getDriverCodeList())) {
            driverCodeList = param.getDriverCodeList();
        }
        List<String> employeeTypeList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(param.getEmployeeTypeList())) {
            employeeTypeList = param.getEmployeeTypeList();
        }
        XxlJobLogger.log("时间：{}，国家列表：{}",
                DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN), JSON.toJSONString(countryList));
        log.info("时间：{}，国家列表：{}", DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN), JSON.toJSONString(countryList));
        if (CollUtil.isEmpty(countryList)) {
            XxlJobLogger.log("不存在国家列表");
            return ReturnT.SUCCESS;
        }
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryNames(countryList);
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
        Map<String, String> countryConfigMap = countryConfigList.stream()
                .collect(Collectors.toMap(CountryConfigDTO::getCountryName, CountryConfigDTO::getTimeZone));

        // 获取当前时间的前一天
        DateTime dateOff = DateUtil.offsetDay(date, -1);

        // 1. 查询is_delete= 0 and is_driver = 1 and 指定国家的所有司机信息 + user_code is not null ： isDriver(BusinessConstant.Y)
        DriverQuery driverQuery = DriverQuery.builder().
                locationCountryList(countryList)
                .build();
        if (CollUtil.isNotEmpty(employeeTypeList)) {
            driverQuery.setEmployeeTypeList(employeeTypeList);
        }
        List<AttendanceUser> userList = userService.queryDriver(driverQuery);
        // 获取指定司机code的司机
        if (CollUtil.isNotEmpty(driverCodeList)) {
            List<String> finalDriverCodeList = driverCodeList;
            userList = userList.stream()
                    .filter(user -> finalDriverCodeList.contains(user.getUserCode()))
                    .collect(Collectors.toList());
        }
        // 将userList 按照locationCountry分组
        Map<String, List<AttendanceUser>> userCountryMap = userList.stream()
                .collect(Collectors.groupingBy(AttendanceUser::getLocationCountry));
        XxlJobLogger.log("查询出来的司机总数：{}", userList.size());
        log.info("查询出来的司机总数：{}", userList.size());
        if (CollUtil.isEmpty(userList)) {
            XxlJobLogger.log("不存在司机信息");
            return ReturnT.SUCCESS;
        }

        List<DriverAttendanceDetailDO> addDriverAttendanceDetailInfoList = Lists.newArrayList();
        List<DriverAttendanceDetailDO> delDriverAttendanceDetailInfoList = Lists.newArrayList();

        for (Map.Entry<String, List<AttendanceUser>> entry : userCountryMap.entrySet()) {
            String country = entry.getKey();
            List<AttendanceUser> countryUserList = entry.getValue();
            // 获取国家对应时区
            String timeZone = countryConfigMap.getOrDefault(country, "");
            if (ObjectUtil.equal(timeZone, "")) {
                XxlJobLogger.log("该国家:{},不存在国家时区", country);
                continue;
            }
            // 将当前时间减一天，转换为国家本地时间
            Date dateTime = DateHelper.convertDateByTimeZonePlus(timeZone, dateOff);
            Long dayId = Long.valueOf(DateUtil.format(dateTime, DatePattern.PURE_DATE_PATTERN));
            int year = DateUtil.year(dateTime);
            int month = DateUtil.month(dateTime) + 1;
            int day = DateUtil.dayOfMonth(dateTime);
            // 校验当前时间dateTime是不是凌晨四点钟
            int hour = DateUtil.hour(dateTime, true);
            log.info("handlerPunchRecordCalculateAttendance locationCountry：{}，localYear：{}，localMonth：{}，localDay：{}，localHour：{},serverDate：{}", country, year, month, day, hour, dateOff);
            // 如果该国家现在不是凌晨四点钟，就不执行。只有每一个国家凌晨四点钟的时候执行后续业务
            if (ObjectUtil.notEqual(hour, 4)) {
                continue;
            }
            log.info("国家：{}，时间：{}，开始计算考勤", country, dayId);
            XxlJobLogger.log("国家：{}，时间：{}，开始计算考勤", country, dayId);
            // 数据库date字段只需要保存到天，所以将dateTime转换为天
            dateTime = DateUtil.beginOfDay(dateTime);

            // 2. 查询前一天所有司机的打卡记录
            DriverPunchRecordDetailQuery driverPunchRecordDetailQuery = new DriverPunchRecordDetailQuery();
            driverPunchRecordDetailQuery.setDayId(dayId);
            // 获取司机打卡记录
            List<DriverPunchRecordDO> driverPunchRecordList = driverPunchRecordManage.selectPunchRecordDetail(driverPunchRecordDetailQuery);
            // 将司机该天打卡记录按照司机账号分组
            Map<String, List<DriverPunchRecordDO>> userCodeMap = driverPunchRecordList.stream()
                    .collect(Collectors.groupingBy(DriverPunchRecordDO::getUserCode));
            // 3. 查询该天所有司机考勤信息
            DriverAttendanceDetailInfoQuery driverAttendanceDetailQuery = DriverAttendanceDetailInfoQuery.builder()
                    .dayId(dayId)
                    .build();
            List<DriverAttendanceDetailDO> driverAttendanceDetail = driverAttendanceManage.queryDriverAttendanceByCondition(driverAttendanceDetailQuery);
            // 将司机该天考勤信息按照司机账号变为map
            Map<String, DriverAttendanceDetailDO> userCodeAttendanceDetailMap = driverAttendanceDetail.stream()
                    .collect(Collectors.toMap(DriverAttendanceDetailDO::getUserCode, v -> v));

            // 4. 生成司机考勤信息
            for (AttendanceUser userInfo : countryUserList) {
                Long locusPunchNumber = 0L;
                Date locusLocalDateTime = null;
                Long dldSignNumber = 0L;
                Date dldLocalDateTime = null;
                boolean flag = false;

                // 4.1 获取司机账号
                String userCode = userInfo.getUserCode();
                // 4.2 获取司机打卡记录
                List<DriverPunchRecordDO> punchRecordList = userCodeMap.get(userCode);
                // 没有打卡记录生成A的考勤数据
                if (CollUtil.isEmpty(punchRecordList)) {
                    XxlJobLogger.log("司机账号：{}，时间：{}，不存在司机打卡记录,所以创建缺勤打卡记录",
                            userCode, dayId);
                    // 先删除存在的考勤
                    DriverAttendanceDetailDO driverAttendanceDetailInfo = userCodeAttendanceDetailMap.get(userCode);
                    if (ObjectUtil.isNotNull(driverAttendanceDetailInfo)) {
                        // 如果存在司机考勤信息，删除存在的考勤
                        driverAttendanceDetailInfo.setIsDelete(BusinessConstant.Y);
                        driverAttendanceDetailInfo.setLastUpdDate(new Date());
                        driverAttendanceDetailInfo.setLastUpdUserCode("XXL-JOB");
                        driverAttendanceDetailInfo.setLastUpdUserName("XXL-JOB");
                        delDriverAttendanceDetailInfoList.add(driverAttendanceDetailInfo);
                    }
                    // 新增缺勤考勤数据
                    DriverPunchRecordDO driverPunchRecordInfo = new DriverPunchRecordDO();
                    driverPunchRecordInfo.setOperationType(DriverAttendanceOperationTypeEnum.DEFAULT.getType());
                    buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.ABSENT.getType(),
                            dldSignNumber, locusPunchNumber, null, null, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                    continue;
                }
                // 4.5 将司机该司机打卡记录按照操作类型分组
                Map<Integer, List<DriverPunchRecordDO>> operationTypeMap = punchRecordList.stream()
                        .collect(Collectors.groupingBy(DriverPunchRecordDO::getOperationType));

                // 4.4 将司机该司机打卡记录按照操作类型分组
                DriverAttendanceDetailDO driverAttendanceDetailInfo = userCodeAttendanceDetailMap.get(userCode);
                if (ObjectUtil.isNotNull(driverAttendanceDetailInfo)) {
                    // 如果存在司机考勤信息，删除存在的考勤
                    driverAttendanceDetailInfo.setIsDelete(BusinessConstant.Y);
                    driverAttendanceDetailInfo.setLastUpdDate(new Date());
                    driverAttendanceDetailInfo.setLastUpdUserCode("XXL-JOB");
                    driverAttendanceDetailInfo.setLastUpdUserName("XXL-JOB");
                    delDriverAttendanceDetailInfoList.add(driverAttendanceDetailInfo);
                }

                // 计算考勤之前，需要先看打卡记录里面是否存在轨迹打卡以及签收的记录，如果存在，那么就需要先暂存轨迹打卡次数以及签收次数，方便后面计算考勤的时候，存入考勤数据
                for (DriverPunchRecordDO driverPunchRecord : punchRecordList) {
                    if (ObjectUtil.equal(driverPunchRecord.getOperationType(),
                            DriverAttendanceOperationTypeEnum.DA_LOCUS_PUNCH.getType())) {
                        locusPunchNumber = driverPunchRecord.getNumber();
                    }
                    if (ObjectUtil.equal(driverPunchRecord.getOperationType(),
                            DriverAttendanceOperationTypeEnum.DLD_SIGN.getType())) {
                        dldSignNumber = driverPunchRecord.getNumber();
                    }
                }

                // 考勤优先级：修改考勤  请假  轨迹打卡  DLD签收： 按照创建时间倒序，看最近一次创建的打卡记录是什么，就生成什么考勤数据

                // 按照创建时间倒序排序，获取最后一次操作的打卡记录
                List<DriverPunchRecordDO> lastDriverRecordList = punchRecordList.stream()
                        .sorted(Comparator.comparing(DriverPunchRecordDO::getCreateDate).reversed())
                        .collect(Collectors.toList());
                // 获取最后一次创建的打卡记录
                DriverPunchRecordDO driverPunchRecordInfo = lastDriverRecordList.get(0);
                if (ObjectUtil.isNull(driverPunchRecordInfo)) {
                    XxlJobLogger.log("司机账号：{}，时间：{}，不存在司机打卡记录", userCode, dayId);
                    continue;
                }
                // 如果最新的一条数据数据是修改考勤，那么就生成修改考勤的数据
                if (ObjectUtil.equal(driverPunchRecordInfo.getOperationType(),
                        DriverAttendanceOperationTypeEnum.MODIFY_ATTENDANCE.getType())) {
                    buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, driverPunchRecordInfo.getModifyAttendanceType(),
                            dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                    continue;
                }
                // 如果最新的一条数据数据是请假，那么就生成请假的考勤数据
                if (ObjectUtil.equal(driverPunchRecordInfo.getOperationType(),
                        DriverAttendanceOperationTypeEnum.LEAVE.getType())) {
                    buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.LEAVE.getType(),
                            dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                    continue;
                }
                // 如果最新的一条数据数据是轨迹打卡或者DLD签收，那么就生成轨迹打卡 + DLD签收的考勤数据
                if (ObjectUtil.equal(driverPunchRecordInfo.getOperationType(),
                        DriverAttendanceOperationTypeEnum.DA_LOCUS_PUNCH.getType())
                        || ObjectUtil.equal(driverPunchRecordInfo.getOperationType(), DriverAttendanceOperationTypeEnum.DLD_SIGN.getType())) {
                    // 获取轨迹打卡的打卡记录
                    List<DriverPunchRecordDO> daLocusPunch =
                            operationTypeMap.get(DriverAttendanceOperationTypeEnum.DA_LOCUS_PUNCH.getType());
                    // 获取DLD签收的打卡记录
                    List<DriverPunchRecordDO> dldSign =
                            operationTypeMap.get(DriverAttendanceOperationTypeEnum.DLD_SIGN.getType());
                    // 如果轨迹打卡和DLD签收都不为空，那么就计算轨迹打卡和DLD签收的考勤数据，其他情况都是异常数据，记录考勤异常
                    if (CollUtil.isNotEmpty(daLocusPunch)) {
                        DriverPunchRecordDO daLocusRecord = daLocusPunch.get(0);
                        if (ObjectUtil.isNotNull(daLocusRecord) ) {
                            locusPunchNumber = daLocusRecord.getNumber();
                            // 这里使用创建时间来记录司机考勤里面的最近操作记录【最近操作记录其实就是最后创建的时间】operatingTime 字段暂时无用
                            locusLocalDateTime = daLocusRecord.getCreateDate();
                            flag = true;
                        }
                    }
                    if(CollUtil.isNotEmpty(dldSign)){
                        DriverPunchRecordDO dldSignRecord = dldSign.get(0);
                        if (ObjectUtil.isNotNull(dldSignRecord)) {
                            dldSignNumber = dldSignRecord.getNumber();
                            dldLocalDateTime = dldSignRecord.getCreateDate();
                            flag = true;
                        }
                    }
                }
                // 如果都不是，那么就是异常数据，记录考勤异常
                if (flag) {
                    // mex特殊逻辑
                    if (ObjectUtil.equal(country, CountryCodeEnum.MEX.getCode())) {
                        // 如果国家是mex，有轨迹打卡或签收就算满勤
                        if (locusPunchNumber.compareTo(1L) >= 0 || dldSignNumber.compareTo(1L) >= 0) {
                            buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.PRESENT.getType(),
                                    dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                        } else {
                            buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.ABSENT.getType(),
                                    dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                        }
                    }else {
                        // 其他国家
                        if (dldSignNumber.compareTo(1L) >= 0 && locusPunchNumber.compareTo(1L) >= 0) {
                            buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.PRESENT.getType(),
                                    dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                        } else {
                            buildDriverAttendanceData(userCode, year, month, day, dateTime, dayId, DriverAttendanceTypeEnum.ABSENT.getType(),
                                    dldSignNumber, locusPunchNumber, dldLocalDateTime, locusLocalDateTime, driverPunchRecordInfo, addDriverAttendanceDetailInfoList);
                        }
                    }
                }
            }

        }

        // 3.3 生成司机考勤信息
        driverAttendanceManage.batchSaveOrUpdateDriverAttendanceDetail(
                addDriverAttendanceDetailInfoList, delDriverAttendanceDetailInfoList, null);
        return ReturnT.SUCCESS;
    }



    /**
     * 生成司机考勤数据
     *
     * @param userCode                          司机账号
     * @param year                              年
     * @param month                             月
     * @param day                               日
     * @param dateTime                          日期
     * @param dayId                             day_id
     * @param attendanceType                    考勤类型
     * @param dldSignNumber                     DLD签收次数
     * @param locusPunchNumber                  轨迹打卡次数
     * @param dldLocalDateTime                  DLD签收时间
     * @param locusLocalDateTime                轨迹打卡时间
     * @param driverPunchRecordInfo             司机打卡记录
     * @param addDriverAttendanceDetailInfoList 新增司机考勤信息
     */
    private void buildDriverAttendanceData(String userCode, int year, int month, int day, Date dateTime, Long dayId,
                                           Integer attendanceType, Long dldSignNumber, Long locusPunchNumber, Date dldLocalDateTime,
                                           Date locusLocalDateTime, DriverPunchRecordDO driverPunchRecordInfo,
                                           List<DriverAttendanceDetailDO> addDriverAttendanceDetailInfoList) {

        DriverAttendanceDetailDO detail = new DriverAttendanceDetailDO();
        detail.setId(defaultIdWorker.nextId());
        detail.setUserCode(userCode);
        detail.setYear(year);
        detail.setMonth(month);
        detail.setDay(day);
        detail.setDate(dateTime);
        detail.setDayId(dayId);
        detail.setAttendanceType(attendanceType);
        detail.setDldNumber(dldSignNumber);
        detail.setLocusNumber(locusPunchNumber);
        // 如果是修改考勤或者请假，最近操作时间就是操作时间
        if (ObjectUtil.equal(driverPunchRecordInfo.getOperationType(), DriverAttendanceOperationTypeEnum.LEAVE.getType()) ||
                ObjectUtil.equal(driverPunchRecordInfo.getOperationType(), DriverAttendanceOperationTypeEnum.MODIFY_ATTENDANCE.getType())) {
            // driverPunchRecordInfo.getOperatingTime() 这个操作时间字段暂时没用
            //if (ObjectUtil.isNotNull(driverPunchRecordInfo.getOperatingTime())) {
            //    detail.setLastOperatingTime(DateUtil.parse(driverPunchRecordInfo.getOperatingTime().toString(), DatePattern.NORM_DATETIME_PATTERN));
            //}
            detail.setLastOperatingTime(driverPunchRecordInfo.getCreateDate());
        } else {
            if (ObjectUtil.isNotNull(dldLocalDateTime) && ObjectUtil.isNotNull(locusLocalDateTime)) {
                if (dldLocalDateTime.after(locusLocalDateTime)) {
                    detail.setLastOperatingTime(dldLocalDateTime);
                } else {
                    detail.setLastOperatingTime(locusLocalDateTime);
                }
            }else {
                detail.setLastOperatingTime(ObjectUtil.isNotNull(dldLocalDateTime) ?
                        dldLocalDateTime : (ObjectUtil.isNotNull(locusLocalDateTime) ? locusLocalDateTime : null));

            }
        }

        BaseDOUtil.fillDOInsert(detail);
        addDriverAttendanceDetailInfoList.add(detail);
    }

    @Data
    private static class DriverAttendanceParam {
        /**
         * 日期
         */
        private String dateNow;
        /**
         * 国家列表
         */
        private String countryList;

        /**
         * 司机code
         */
        private List<String> driverCodeList;

        /**
         * 用工类型
         */
        private List<String> employeeTypeList;

    }
}
