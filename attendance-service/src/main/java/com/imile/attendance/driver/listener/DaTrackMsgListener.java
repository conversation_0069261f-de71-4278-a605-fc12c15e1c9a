package com.imile.attendance.driver.listener;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.driver.DriverPunchRecordService;
import com.imile.attendance.form.listener.BpmApprovalMsgListener;
import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;
import com.imile.dtrack.api.dto.event.DtrackEventConst;
import com.imile.saas.tms.api.event.EventDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description DA轨迹消息
 */
@Slf4j
@Component
@RocketMQMessageListener(
        nameServer = "${rocketmq.nameServer}",
        topic = "${rocket.mq.da.locus.topic}",
        consumerGroup = "${rocket.mq.da.locus.consumer.group}",
        selectorExpression = "*",
        consumeThreadMax = 4)
public class DaTrackMsgListener implements RocketMQListener<MessageExt>, InitializingBean {

    @Resource
    private DriverPunchRecordService driverPunchRecordService;

    @Value("${rocket.mq.da.locus.topic}")
    private String topic;
    @Value("${rocket.mq.da.locus.consumer.group}")
    private String consumeGroup;

    @Override
    public void onMessage(MessageExt messageExt) {
        log.info("收到DA->Attendance的消息，msgId={},msgKey={}", messageExt.getMsgId(), messageExt.getKeys());
        String tags = messageExt.getTags();
        String body = new String(messageExt.getBody());
        log.info("DaTrackHrMsgListener 收到消息id：{},tags:{},body:{}", messageExt.getMsgId(), tags, body);
        ((DaTrackMsgListener) AopContext.currentProxy()).dealWithBpmMsg(messageExt);
    }


    @Transactional
    public void dealWithBpmMsg(MessageExt messageExt) {
        EventDTO eventDTO =  JSON.parseObject(new String(messageExt.getBody()), EventDTO.class);
        if(ObjectUtil.isNull(eventDTO)){
            log.info("DaTrackMsgListener|execute｜eventDTO为null");
            return;
        }
        log.info("DaTrackMsgListener|execute｜eventDTO:{}", JSON.toJSON(eventDTO));
        String operateTags = messageExt.getTags();
        log.info("监听DA轨迹数据tag：{}", operateTags);
        try {
            // 业务数据：DaTrackHubEventPayload
            if (ObjectUtil.equal(eventDTO.getBizType(), DtrackEventConst.BizType.EVENT) &&
                    ObjectUtil.equal(eventDTO.getOperateType(), DtrackEventConst.OperateType.EVENT_HUB_IN)) {
                log.info("EVENT_HUB_IN｜DA轨迹入口｜满足条件进入if");
                driverPunchRecordService.handleDtrackHubEventPayload(eventDTO);
            }
        } catch (Exception e) {
            log.error("接收DA->Attendance的mq消息发生异常，msgId={}", messageExt.getMsgId(), e);
            throw e;
        }
    }


    @Override
    public void afterPropertiesSet() {
        log.info("DaTrackHrMsgListener consumer for topic: {} and group: {} is started successfully.", topic, consumeGroup);
    }
}
