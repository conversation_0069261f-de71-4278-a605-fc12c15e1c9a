package com.imile.attendance.driver.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * {@code @author:} allen
 * {@code @className:} ModifyDriverDayIdToType
 * {@code @since:} 2024-01-24 16:22
 * {@code @description:}
 */
@Data
@ApiModel(description = "日历修改司机考勤：每天对应的修改的值")
public class ModifyDriverDayIdToType {
    /**
     * 哪一天
     */
    private Long dayId;
    /**
     * 修改之前的司机考勤类型
     */
    private Integer beforeDriverAttendanceType;

    /**
     * 修改之后的司机考勤类型
     */
    private Integer afterDriverAttendanceType;
}
