package com.imile.attendance.driver;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.driver.dto.DriverAttendanceOperateRecordParam;
import com.imile.attendance.driver.mapstruct.DriverPunchMapstruct;
import com.imile.attendance.driver.vo.DriverAttendanceOperateRecordVO;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverAttendanceOperateRecordDao;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceOperateRecordDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceOperateRecordQuery;
import com.imile.attendance.util.PageUtil;
import com.imile.common.page.PaginationResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description 司机考勤操作记录服务
 */
@Slf4j
@Service
public class DriverAttendanceOperateRecordService {

    @Resource
    private DriverAttendanceOperateRecordDao driverAttendanceOperateRecordDao;


    public PaginationResult<DriverAttendanceOperateRecordVO> pageAttendanceOperateRecordDetail(
            DriverAttendanceOperateRecordParam param) {
        DriverAttendanceOperateRecordQuery query =
                DriverPunchMapstruct.INSTANCE.toDriverAttendanceOperateRecordQuery(param);

        Page<DriverAttendanceOperateRecordDO> page = PageHelper.startPage(
                query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<DriverAttendanceOperateRecordDO> pageInfo = page.doSelectPageInfo(
                () -> driverAttendanceOperateRecordDao.listAttendanceOperateRecordDetail(query));

        List<DriverAttendanceOperateRecordDO> driverAttendanceOperateRecord = pageInfo.getList();
        if (CollectionUtils.isEmpty(driverAttendanceOperateRecord)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<DriverAttendanceOperateRecordVO> operateRecordVOList =
                DriverPunchMapstruct.INSTANCE.toOperateRecordVO(driverAttendanceOperateRecord);
        return PageUtil.getPageResult(operateRecordVOList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }
}
