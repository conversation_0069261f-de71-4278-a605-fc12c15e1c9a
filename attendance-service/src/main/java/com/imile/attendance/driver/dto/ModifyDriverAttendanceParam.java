package com.imile.attendance.driver.dto;

import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} ModifyDriverAttendanceParam
 * {@code @since:} 2024-01-24 10:08
 * {@code @description:}
 */
@Data
@ApiModel(description = "日历修改司机考勤详情入参")
public class ModifyDriverAttendanceParam {
    /**
     * 国家
     */
    @NotEmpty(message = ValidCodeConstant.NOT_BLANK)
    private String country;

    /**
     * 用户code
     */
    @NotEmpty(message = ValidCodeConstant.NOT_BLANK)
    private String userCode;
    /**
     * day_id 示例：20240124
     */
    private List<ModifyDriverDayIdToType> modifyDriverDayIdToTypeList;

}
