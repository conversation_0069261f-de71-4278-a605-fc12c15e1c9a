package com.imile.attendance.cycleConfig.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
@Data
public class AttendanceCycleConfigPageDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 常驻地国家
     */
    private String country;

    /**
     * 考勤周期类型：1:月，2:周，3:自定义 ：枚举：AttendanceCycleType
     */
    private Integer cycleType;

    /**
     * 翻译考勤周期类型，前端忽略该字段
     */
    private String cycleTypeString;

    /**
     * 周期开始
     */
    private String cycleStart;

    /**
     * 周期开始 描述
     */
    private String cycleStartDesc;

    /**
     * 周期结束
     */
    private String cycleEnd;

    /**
     * 周期结束 描述
     */
    private String cycleEndDesc;

    /**
     * 考勤异常过期设置：比如2，不包含本周期，往前推2个周期。
     */
    private Integer abnormalExpired;

    /**
     * 状态
     */
    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;
}
