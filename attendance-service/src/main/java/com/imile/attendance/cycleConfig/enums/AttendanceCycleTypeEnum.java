package com.imile.attendance.cycleConfig.enums;

import com.google.common.collect.Lists;
import com.imile.attendance.enums.CountryCodeEnum;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Getter
public enum AttendanceCycleTypeEnum {

    DEFAULT(0, "", ""),
    MONTH(1, "月", "month"),
    WEEK(2, "周", "week");

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, AttendanceCycleTypeEnum> CACHE_MAP = new ConcurrentHashMap<>();

    /**
     * 使用周维度的国家
     */
    private static final List<String> WEEK_COUNTRY = Lists.newArrayList(CountryCodeEnum.MEX.getCode(),
            CountryCodeEnum.BRA.getCode());

    static {
        for (AttendanceCycleTypeEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    AttendanceCycleTypeEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static AttendanceCycleTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }

    public static String getCycleTypeString(Integer cycleType) {
        AttendanceCycleTypeEnum cycleTypeEnum = getByType(cycleType);
        if (null == cycleTypeEnum) {
            return null;
        }
        return cycleTypeEnum.name();
    }

    /**
     * 根据国家获取对应的考勤周期类型
     */
    public static Integer getCycleTypeByCountry(String country) {
        if (WEEK_COUNTRY.contains(country)) {
            return AttendanceCycleTypeEnum.WEEK.getType();
        }
        return AttendanceCycleTypeEnum.MONTH.getType();
    }

    public static void main(String[] args) {
        System.out.println(AttendanceCycleTypeEnum.getCycleTypeString(AttendanceCycleTypeEnum.MONTH.getType()));
        System.out.println(AttendanceCycleTypeEnum.getCycleTypeString(AttendanceCycleTypeEnum.WEEK.getType()));
        System.out.println(AttendanceCycleTypeEnum.getCycleTypeString(AttendanceCycleTypeEnum.DEFAULT.getType()));
        System.out.println(AttendanceCycleTypeEnum.getCycleTypeByCountry(CountryCodeEnum.BRA.getCode()));
        System.out.println(AttendanceCycleTypeEnum.getCycleTypeByCountry(CountryCodeEnum.CHN.getCode()));
    }
}
