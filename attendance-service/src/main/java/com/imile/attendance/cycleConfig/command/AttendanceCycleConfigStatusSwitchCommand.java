package com.imile.attendance.cycleConfig.command;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
@Data
public class AttendanceCycleConfigStatusSwitchCommand {

    /**
     * 考勤周期id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long id;

    /**
     * 考勤周期状态
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String status;
}
