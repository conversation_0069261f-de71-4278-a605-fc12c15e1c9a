package com.imile.attendance.ipep.dto;

import java.io.Serializable;

/**
 * 文件oss操作
 *
 * <AUTHOR>
 * @company 杭州艾麦科技有限公司
 * @className:
 * @date 2020/9/7 17:13
 */
public class OssApiVo implements Serializable {

    /**
     * 存储空间类型，1私有 2公有
     */
    private Integer bucketType;

    /**
     * 文件名
     */
    private String originalFilename;

    /**
     * 文件相对路径
     */
    private String fileKey;

    /**
     * 文件完整路径
     */
    private String fileUrl;

    /**
     * 文件类型，后缀
     */
    private String fileType;


    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    public String getFileKey() {
        return fileKey;
    }

    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Integer getBucketType() {
        return bucketType;
    }

    public void setBucketType(Integer bucketType) {
        this.bucketType = bucketType;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
}
