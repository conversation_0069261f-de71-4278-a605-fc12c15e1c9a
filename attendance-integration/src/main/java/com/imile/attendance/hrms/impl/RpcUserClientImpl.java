package com.imile.attendance.hrms.impl;

import com.imile.attendance.hrms.RpcUserClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hrms.api.user.api.UserApi;
import com.imile.hrms.api.user.enums.UserDynamicFieldEnum;
import com.imile.hrms.api.user.result.UserDynamicInfoDTO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
@Component
public class RpcUserClientImpl implements RpcUserClient {


    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 15000)
    private UserApi userApi;

    @Override
    public List<UserDynamicInfoDTO> listUserDynamicInfo(List<String> userCodeList,
                                                        List<UserDynamicFieldEnum> dynamicFieldList) {
        return RpcResultProcessor.process(userApi.listUserDynamicInfo(userCodeList, dynamicFieldList));
    }
}
