package com.imile.attendance.hrms.impl;

import com.imile.attendance.hrms.RpcDeptClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hermes.enterprise.api.EntOcApi;
import com.imile.hrms.api.organization.api.DeptApi;
import com.imile.hrms.api.organization.dto.DeptChainNodeDTO;
import com.imile.hrms.api.organization.dto.DeptDTO;
import com.imile.hrms.api.organization.dto.DeptOwnerDTO;
import com.imile.hrms.api.organization.query.DeptConditionParam;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description
 */
@RequiredArgsConstructor
@Component
public class RpcDeptClientImpl implements RpcDeptClient {


    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 15000)
    private DeptApi deptApi;


    @Override
    public List<DeptDTO> listDeptByCondition(DeptConditionParam param) {
        return RpcResultProcessor.process(deptApi.listDeptByCondition(param));
    }

    @Override
    public Map<String, List<DeptChainNodeDTO>> getDeptChainNodeListMap(List<String> deptCodeList) {
        return RpcResultProcessor.process(deptApi.getDeptChainNodeListMap(deptCodeList));
    }

    @Override
    public Map<String, List<DeptOwnerDTO>> getDeptOwnerListMap(List<String> deptCodeList) {
        return RpcResultProcessor.process(deptApi.getDeptOwnerListMap(deptCodeList));
    }
}
