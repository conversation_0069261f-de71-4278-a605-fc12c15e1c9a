package com.imile.attendance.bpm.impl;

import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.bpm.api.BpmApprovalApi;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiQuery;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalUpdateContentApiDTO;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/9 
 * @Description
 */
@RequiredArgsConstructor
@Component
public class RpcBpmApprovalClientImpl implements RpcBpmApprovalClient {

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private BpmApprovalApi bpmApprovalApi;


    @Override
    public ApprovalInfoCreateResultDTO addApprovalInfo(ApprovalInitInfoApiDTO initInfoApiDTO) {
        return RpcResultProcessor.process(bpmApprovalApi.addApprovalInfoV2(initInfoApiDTO));
    }

    @Override
    public List<ApprovalEmptyRecordApiDTO> getEmptyApprovalRecords(ApprovalEmptyRecordApiQuery query) {
        return RpcResultProcessor.process(bpmApprovalApi.getEmptyApprovalRecords(query));
    }

    @Override
    public void backApply(Long approvalId) {
        RpcResultProcessor.process(bpmApprovalApi.backApply(approvalId));
    }

    @Override
    public List<String> getApprovalUserCodes(Long approvalId) {
        return RpcResultProcessor.process(bpmApprovalApi.getApprovalUserCodes(approvalId));
    }

    @Override
    public void updateApprovalContentV2(ApprovalUpdateContentApiDTO updateContentApiDTO) {
        RpcResultProcessor.process(bpmApprovalApi.updateApprovalContentV2(updateContentApiDTO));
    }
}
