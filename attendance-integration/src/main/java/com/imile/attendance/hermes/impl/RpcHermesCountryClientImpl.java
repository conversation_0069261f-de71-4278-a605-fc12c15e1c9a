package com.imile.attendance.hermes.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.hermes.RpcHermesCountryClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hermes.business.api.CountryApi;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Component
public class RpcHermesCountryClientImpl implements RpcHermesCountryClient {

    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 10000)
    private CountryApi countryApi;

    @Override
    public List<CountryConfigDTO> queryAllCountryConfigList() {
        return RpcResultProcessor.process(countryApi.queryAllCountryConfigList());
    }

    @Override
    public CountryConfigDTO queryCountryConfig(CountryApiQuery countryApiQuery) {
        return RpcResultProcessor.process(countryApi.queryCountryConfig(countryApiQuery));
    }

    @Override
    public List<CountryConfigDTO> queryCountryConfigList(CountryApiQuery countryApiQuery) {
        return RpcResultProcessor.process(countryApi.queryCountryConfigList(countryApiQuery));
    }
}
