package com.imile.attendance.hermes.impl;

import com.imile.attendance.hermes.RpcHermesEntOcClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hermes.enterprise.api.EntOcApi;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/16 
 * @Description
 */
@Component
public class RpcHermesEntOcClientImpl implements RpcHermesEntOcClient {

    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 10000)
    private EntOcApi entOcApi;

    @Override
    public EntOcApiDTO getOcByCode(Long orgId, String ocCode) {
        return RpcResultProcessor.process(entOcApi.getOcByCode(orgId, ocCode));
    }


    @Override
    public List<EntOcApiDTO> getOcByCodes(Long orgId, List<String> ocCodes) {
        return RpcResultProcessor.process(entOcApi.getOcByCodes(orgId, ocCodes));
    }
}
