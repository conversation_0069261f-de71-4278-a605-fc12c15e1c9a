package com.imile.attendance.hermes.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 数据字典值对象
 */
@Data
@Accessors(chain = true)
public class DictVO implements Serializable {

    private String typeCode;
    private String dataCode;
    /**
     * 默认值
     */
    private String dataValue;
    /**
     * 中文值
     */
    private String dataValueCn;
    /**
     * 英文值
     */
    private String dataValueEn;

    /**
     * 排序号
     */
    private Integer sort;
}
