<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.rule.mapper.ReissueCardConfigMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="config_no" property="configNo" jdbcType="VARCHAR"/>
        <result column="config_name" property="configName" jdbcType="VARCHAR"/>
        <result column="max_repunch_number" property="maxRepunchNumber" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="dept_ids" property="deptIds" jdbcType="VARCHAR"/>
        <result column="is_latest" property="isLatest" jdbcType="TINYINT"/>
        <result column="effect_time" property="effectTime" jdbcType="TIMESTAMP"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="effect_timestamp" property="effectTimestamp" jdbcType="BIGINT"/>
        <result column="expire_timestamp" property="expireTimestamp" jdbcType="BIGINT"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="record_version" property="recordVersion" jdbcType="BIGINT"/>
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="create_user_code" property="createUserCode" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="last_upd_date" property="lastUpdDate" jdbcType="TIMESTAMP"/>
        <result column="last_upd_user_code" property="lastUpdUserCode" jdbcType="VARCHAR"/>
        <result column="last_upd_user_name" property="lastUpdUserName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, config_no, config_name, max_repunch_number, `status`, country,
    dept_ids, is_latest, effect_time, expire_time, effect_timestamp, expire_timestamp,
        is_delete, record_version, create_date, create_user_code,
    create_user_name, last_upd_date, last_upd_user_code, last_upd_user_name
    </sql>

    <sql id="Join_Column_List">
        rc.id, rc.config_no, rc.config_name, rc.max_repunch_number,
    rc.status, rc.country, rc.dept_ids, rc.is_latest,rc.effect_time, rc.expire_time, rc.effect_timestamp, rc.expire_timestamp,
    rc.is_delete, rc.record_version, rc.create_date, rc.create_user_code, rc.create_user_name, rc.last_upd_date,
    rc.last_upd_user_code, rc.last_upd_user_name
    </sql>

    <select id="pageQuery"
            parameterType="com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigPageQuery"
            resultType="com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        reissue_card_config
        where is_delete = 0 and is_latest = 1
        <if test="configName!=null and configName!=''">
            and config_name = #{configName}
        </if>
        <if test="status!=null and status!=''">
            and status = #{status}
        </if>
        <if test="configIds!=null and configIds.size()>0">
            <foreach collection="configIds" item="configId" open="and id in (" close=")" separator=",">
                #{configId}
            </foreach>
        </if>
        <!-- 同时具有部门和国家权限 -->
        <if test="hasDeptPermission == true and hasCountryPermission == true">
            <if test="deptIds != null and deptIds.size() > 0">
                and (
                <foreach collection="deptIds" item="deptId" separator=" or ">
                    FIND_IN_SET(#{deptId}, dept_ids)
                </foreach>
                <if test="authLocationCountryList != null and authLocationCountryList.size() > 0">
                    or country in
                    <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                        #{country}
                    </foreach>
                </if>
                )
            </if>
            <if test="deptIds == null or deptIds.size() == 0">
                <if test="authLocationCountryList != null and authLocationCountryList.size() > 0">
                    and country in
                    <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                        #{country}
                    </foreach>
                </if>
            </if>
        </if>
        <!-- 只有部门权限 -->
        <if test="hasDeptPermission == true and hasCountryPermission == false">
            <if test="deptIds != null and deptIds.size() > 0">
                and (
                <foreach collection="deptIds" item="deptId" separator=" or ">
                    FIND_IN_SET(#{deptId}, dept_ids)
                </foreach>
                )
            </if>
        </if>
        <!-- 只有国家权限 -->
        <if test="hasDeptPermission == false and hasCountryPermission == true">
            <if test="authLocationCountryList != null and authLocationCountryList.size() > 0">
                and country in
                <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                    #{country}
                </foreach>
            </if>
        </if>
        <!-- 选择部门的特殊处理 -->
        <if test="isChooseDept == true">
            <if test="deptIds != null and deptIds.size() > 0">
                and (
                <foreach collection="deptIds" item="deptId" separator=" OR ">
                    FIND_IN_SET(#{deptId}, dept_ids)
                </foreach>
                )
            </if>
        </if>
        order by create_date desc
    </select>
    

</mapper>
