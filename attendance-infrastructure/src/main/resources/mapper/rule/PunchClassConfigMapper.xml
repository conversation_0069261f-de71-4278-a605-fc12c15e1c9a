<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.rule.mapper.PunchClassConfigMapper">

    <select id="pageQuery"
            parameterType="com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery"
            resultType="com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO">
        SELECT
        pcc.id,pcc.status,pcc.class_name,pcc.config_no,pcc.class_nature,pcc.country,pcc.legal_working_hours, pcc.attendance_hours,
        pcc.dept_ids,pcc.create_date,pcc.create_user_name,pcc.last_upd_date,pcc.last_upd_user_name
        FROM punch_class_config pcc
        <where>
            pcc.is_delete = 0 AND pcc.is_latest = 1
            <if test="classNature != null and classNature!=''">
                AND pcc.class_nature = #{classNature}
            </if>

            <if test="status != null and status !=''">
                AND pcc.status = #{status}
            </if>

            <if test="className != null and className !=''">
                AND pcc.class_name  LIKE CONCAT('%', #{className}, '%')
            </if>

            <if test="ids != null and ids.size() > 0">
                AND pcc.id IN
                <foreach item="id" collection="ids" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>

            <include refid="permissionCountryAndDeptConditions"/>
        </where>
        order by pcc.create_date desc
    </select>


    <select id="export"
            parameterType="com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery"
            resultType="com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigExportDTO">
        SELECT
        pcc.id,pcc.status,pcc.class_name,pcc.class_type,pcc.country,pcc.legal_working_hours as totalLegalWorkingHours,
        pcc.attendance_hours as totalAttendanceHours,pcc.dept_ids,pcc.create_date,pcc.create_user_name,pcc.last_upd_date,pcc.last_upd_user_name,
        pcic.id as itemClassId,pcic.sort_no, pcic.punch_in_time,pcic.punch_out_time,pcic.earliest_punch_in_time,pcic.latest_punch_in_time,
        pcic.latest_punch_out_time,pcic.elastic_time,pcic.rest_start_time,pcic.rest_end_time,pcic.legal_working_hours,pcic.attendance_hours
        FROM punch_class_config pcc
        LEFT JOIN punch_class_item_config pcic on pcc.id = pcic.punch_class_id
        <where>
            pcc.is_delete = 0 AND  pcic.is_delete = 0 AND pcc.is_latest = 1
            <if test="classNature != null and classNature!=''">
                AND pcc.class_nature = #{classNature}
            </if>

            <if test="status != null and status !=''">
                AND pcc.status = #{status}
            </if>

            <if test="className != null and className !=''">
                AND pcc.class_name = #{className}
            </if>

            <if test="ids != null and ids.size() > 0">
                AND pcc.id IN
                <foreach item="id" collection="ids" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>

            <include refid="permissionCountryAndDeptConditions"/>
        </where>
        order by pcc.create_date desc
    </select>

    <!-- 权限和部门条件片段 -->
    <sql id="permissionCountryAndDeptConditions">
        <!-- 同时具有部门和国家权限 -->
        <if test="hasDeptPermission == true and hasCountryPermission == true">
            <if test="deptIds != null and deptIds.size() > 0">
                AND (
                <if test="deptIds != null and deptIds.size() > 0">
                    <foreach collection="deptIds" item="deptId" separator=" OR ">
                        FIND_IN_SET(#{deptId}, pcc.dept_ids)
                    </foreach>
                </if>

                <if test="authLocationCountryList != null and authLocationCountryList.size() > 0">
                    OR
                    pcc.country IN
                    <foreach collection="authLocationCountryList" item="country" open="(" close=")" separator=",">
                        #{country}
                    </foreach>
                </if>
                )
            </if>

            <if test="deptIds == null or deptIds.size() == 0">
                <if test="authLocationCountryList != null and authLocationCountryList.size() > 0">
                    AND pcc.country in
                    <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                        #{country}
                    </foreach>
                </if>
            </if>
        </if>

        <!-- 只有部门权限 -->
        <if test="hasDeptPermission == true and hasCountryPermission == false">
            <if test="deptIds != null and deptIds.size() > 0">
                AND (
                <foreach collection="deptIds" item="deptId" separator=" OR ">
                    FIND_IN_SET(#{deptId}, pcc.dept_ids)
                </foreach>
                )
            </if>
        </if>

        <!-- 只有国家权限 -->
        <if test="hasDeptPermission == false and hasCountryPermission == true">
            AND pcc.country in
            <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                #{country}
            </foreach>
        </if>

        <!-- 选择部门的特殊处理 -->
        <if test="isChooseDept == true">
            <if test="deptIds != null and deptIds.size() > 0">
                AND (
                <foreach collection="deptIds" item="deptId" separator=" OR ">
                    FIND_IN_SET(#{deptId}, pcc.dept_ids)
                </foreach>
                )
            </if>
        </if>
    </sql>

</mapper>
