<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.base.mapper.MqFailRecordMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.base.model.MqFailRecordDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="topic" jdbcType="VARCHAR" property="topic" />
        <result column="tag" jdbcType="VARCHAR" property="tag" />
        <result column="msg_key" jdbcType="VARCHAR" property="msgKey" />
        <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
        <result column="msg_body" jdbcType="VARCHAR" property="msgBody" />
        <result column="msg_retry_service_bean" jdbcType="VARCHAR" property="msgRetryServiceBean" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
        <result column="error_stack" jdbcType="VARCHAR" property="errorStack" />
        <result column="last_retry_time" jdbcType="TIMESTAMP" property="lastRetryTime" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, topic, tag, `msg_key`, msg_id, msg_body, msg_retry_service_bean, status, retry_count, error_msg, error_stack,
    last_retry_time, is_delete, record_version, create_date,
    create_user_code, create_user_name, last_upd_date, last_upd_user_code, last_upd_user_name
    </sql>

    <!-- 你可以在这里添加自定义的SQL语句 -->
</mapper>
