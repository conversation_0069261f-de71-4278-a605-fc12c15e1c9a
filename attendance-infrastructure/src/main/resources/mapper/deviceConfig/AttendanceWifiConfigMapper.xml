<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.deviceConfig.mapper.AttendanceWifiConfigMapper">

    <resultMap id="BaseResultMap"
               type="com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceWifiConfigDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="record_version" jdbcType="BIGINT" property="recordVersion"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate"/>
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="location_city" jdbcType="VARCHAR" property="locationCity"/>
        <result column="wifi_name" jdbcType="VARCHAR" property="wifiName"/>
        <result column="mac_address" jdbcType="VARCHAR" property="macAddress"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        country,
        location_city,
        wifi_name,
        mac_address
    </sql>

    <select id="queryWifiCountry" resultType="java.lang.String">
        select distinct country
        from attendance_wifi_config
        where is_delete = 0
    </select>

    <select id="queryWifiCity" resultType="java.lang.String">
        select distinct location_city
        from attendance_wifi_config
        where is_delete = 0
          and country = #{country}
    </select>

</mapper>
