<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.abnormal.mapper.AttendanceEmployeeDetailSnapshotMapper">
    <resultMap id="BaseResultMap"
               type="com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO">
        <!--@mbg.generated-->
        <!--@Table hrms.hrms_attendance_employee_detail_snapshot-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="location_country" jdbcType="VARCHAR" property="locationCountry"/>
        <result column="year" jdbcType="INTEGER" property="year"/>
        <result column="month" jdbcType="INTEGER" property="month"/>
        <result column="day" jdbcType="INTEGER" property="day"/>
        <result column="date" jdbcType="TIMESTAMP" property="date"/>
        <result column="day_id" jdbcType="BIGINT" property="dayId"/>
        <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
        <result column="attendance_type" jdbcType="VARCHAR" property="attendanceType"/>
        <result column="concrete_type" jdbcType="VARCHAR" property="concreteType"/>
        <result column="is_attendance" jdbcType="INTEGER" property="isAttendance"/>
        <result column="overtime_hours" jdbcType="DECIMAL" property="overtimeHours"/>
        <result column="attendance_hours" jdbcType="DECIMAL" property="attendanceHours"/>
        <result column="attendance_start_time" jdbcType="TIMESTAMP" property="attendanceStartTime"/>
        <result column="attendance_end_time" jdbcType="TIMESTAMP" property="attendanceEndTime"/>
        <result column="delivery_count" jdbcType="INTEGER" property="deliveryCount"/>
        <result column="scan_type" jdbcType="VARCHAR" property="scanType"/>
        <result column="delivered_count" jdbcType="INTEGER" property="deliveredCount"/>
        <result column="pick_up_count" jdbcType="INTEGER" property="pickUpCount"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="record_version" jdbcType="BIGINT" property="recordVersion"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate"/>
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName"/>
        <result column="orderby" jdbcType="DECIMAL" property="orderby"/>
        <result column="attendance_rate" jdbcType="DECIMAL" property="attendanceRate"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="post_id" jdbcType="BIGINT" property="postId"/>
        <result column="leave_type" jdbcType="VARCHAR" property="leaveType"/>
        <result column="stage" jdbcType="INTEGER" property="stage"/>
        <result column="leave_percent_salary" jdbcType="DECIMAL" property="leavePercentSalary"/>
        <result column="leave_hours" jdbcType="DECIMAL" property="leaveHours"/>
        <result column="picture_path" jdbcType="VARCHAR" property="picturePath"/>
        <result column="attendance_remark" jdbcType="VARCHAR" property="attendanceRemark"/>
        <result column="leave_minutes" jdbcType="DECIMAL" property="leaveMinutes"/>
        <result column="attendance_minutes" jdbcType="DECIMAL" property="attendanceMinutes"/>
        <result column="overtime_minutes" jdbcType="DECIMAL" property="overtimeMinutes"/>
        <result column="legal_working_hours" jdbcType="DECIMAL" property="legalWorkingHours"/>
        <result column="form_id" jdbcType="BIGINT" property="formId"/>
        <result column="last_modify_time" jdbcType="TIMESTAMP" property="lastModifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, user_id, location_country, `year`, `month`, `day`, `date`, day_id, data_source, attendance_type,
        concrete_type, is_attendance, overtime_hours, attendance_hours, attendance_start_time,
        attendance_end_time, delivery_count, scan_type, delivered_count, pick_up_count, remark,
        is_delete, record_version, create_date, create_user_code, create_user_name, last_upd_date,
        last_upd_user_code, last_upd_user_name, orderby, attendance_rate, company_id, dept_id,
        post_id, leave_type, stage, leave_percent_salary, leave_hours, picture_path, attendance_remark,
        leave_minutes, attendance_minutes, overtime_minutes, legal_working_hours, form_id,
        last_modify_time
    </sql>
</mapper>