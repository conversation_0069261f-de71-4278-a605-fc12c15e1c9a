<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.zkteco.mapper.ZktecoMapper">


    <select id="selectSnByArea" resultType="com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoAreasDO">
        select pa.id, pa.area_code as areaCode, pa.area_name as areaName, it.sn
        from personnel_area pa
        inner join iclock_terminal it on pa.id = it.area_id
    </select>


    <select id="selectEmployee" resultType="com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeDO">
        select id, emp_code as empCode, first_name as firstName, last_name as lastName,
        emp_type as empType, department_id as departmentId, position_id as positionId,
        card_no as cardNo , is_active as isActive, status
        from personnel_employee
        where emp_code in
        <foreach item="item" index="index" collection="empCodes"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectAreaByEmployee" resultType="com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeAreaDO">
        select id, employee_id as employeeId, area_id as areaId
        from personnel_employee_area
        where employee_id = #{employeeId}
    </select>

    <select id="selectAreasByName" resultType="com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoAreasDO"
            parameterType="java.lang.String">
        select p1.*
        from personnel_area p1
        left join personnel_area p2
        on p1.parent_area_id = p2.id
        where p2.area_name = #{areaName}
    </select>
    <select id="selectEmployeeAreas" resultType="com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeAreaDO">
        select *
        from personnel_employee_area
        where area_id in
        <foreach item="item" index="index" collection="areaIdList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
    <select id="selectEmployeeByIds" resultType="com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeDO">
        select *
        from personnel_employee
        where id in
        <foreach item="item" index="index" collection="idList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="selectEmployeeIdByIds" resultType="java.lang.Integer">
    select employee_id
    from iclock_biodata
    where employee_id in
        <foreach item="item" index="index" collection="employeeIdList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
    <select id="selectAreasByAreaName" resultType="com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoAreasDO">
        select * from personnel_area where area_name = #{areaName}
    </select>

    <update id="updateEmployee" parameterType="com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeUpdateDO">
        update personnel_employee
        <set>
            <if test="userName != '' and userName != null">
                first_name = #{userName},
            </if>
            <if test="deptId != null">
                department_id = #{deptId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where emp_code = #{userCode}
    </update>


    <update id="updateEmployeeArea">
        update personnel_employee_area
        <set>
            <if test="areaId != null">
                area_id = #{areaId},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeArea">
        delete from personnel_employee_area where id = #{id}
    </delete>


</mapper>
