<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.calendar.mapper.CalendarConfigMapper">
  <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO">
    <!--@mbg.generated-->
    <!--@Table attendance.calendar_config-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="attendance_config_no" jdbcType="VARCHAR" property="attendanceConfigNo" />
    <result column="attendance_config_name" jdbcType="VARCHAR" property="attendanceConfigName" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="dept_ids" jdbcType="VARCHAR" property="deptIds" />
    <result column="dept_codes" jdbcType="VARCHAR" property="deptCodes" />
    <result column="is_latest" jdbcType="TINYINT" property="isLatest" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="orderby" jdbcType="DECIMAL" property="orderby" />
  </resultMap>
  <sql id="Base_Column_List">
      <!--@mbg.generated-->
      id, country, attendance_config_no, company_id, attendance_config_name, `type`, `status`,
      dept_ids, dept_codes, is_latest, is_delete, record_version, create_date, create_user_code, create_user_name,
      last_upd_date, last_upd_user_code, last_upd_user_name, orderby
  </sql>
</mapper>