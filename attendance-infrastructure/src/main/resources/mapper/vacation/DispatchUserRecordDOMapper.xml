<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.vacation.mapper.DispatchUserRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.imile.attendance.infrastructure.repository.vacation.model.DispatchUserRecordDO">
        <id column="id" property="id"/>
        <result column="is_delete" property="isDelete"/>
        <result column="record_version" property="recordVersion"/>
        <result column="create_date" property="createDate"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_upd_date" property="lastUpdDate"/>
        <result column="last_upd_user_code" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" property="lastUpdUserName"/>
        <result column="dispatch_date" property="dispatchDate"/>
        <result column="dispatch_country" property="dispatchCountry"/>
        <result column="user_code" property="userCode"/>
        <result column="country_code" property="countryCode"/>
        <result column="end_flag" property="endFlag"/>
        <result column="transform_type" property="transformType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        dispatch_date, dispatch_country, user_code, country_code, end_flag, transform_type
    </sql>

</mapper>
