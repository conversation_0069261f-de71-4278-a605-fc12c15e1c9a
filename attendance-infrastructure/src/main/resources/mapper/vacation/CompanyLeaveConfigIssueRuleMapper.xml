<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveConfigIssueRuleMapper">
  <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="leave_id" jdbcType="BIGINT" property="leaveId" />
    <result column="issue_frequency" jdbcType="TINYINT" property="issueFrequency" />
    <result column="issue_time" jdbcType="TINYINT" property="issueTime" />
    <result column="issue_month" jdbcType="INTEGER" property="issueMonth" />
    <result column="issue_day" jdbcType="INTEGER" property="issueDay" />
    <result column="issue_type" jdbcType="TINYINT" property="issueType" />
    <result column="issue_quota" jdbcType="DECIMAL" property="issueQuota" />
    <result column="cycle_unit" jdbcType="INTEGER" property="cycleUnit" />
    <result column="cycle_number" jdbcType="INTEGER" property="cycleNumber" />
    <result column="is_convert" jdbcType="BIGINT" property="isConvert" />
    <result column="is_convert_dispatch" jdbcType="INTEGER" property="isConvertDispatch" />
    <result column="issue_rounding_rule" jdbcType="TINYINT" property="issueRoundingRule" />
    <result column="is_recalculate" jdbcType="TINYINT" property="isRecalculate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_delete, record_version, create_date, create_user_code, create_user_name, last_upd_date, 
    last_upd_user_code, last_upd_user_name, leave_id, issue_frequency, issue_time, issue_month, 
    issue_day, issue_type, issue_quota, cycle_unit, cycle_number, is_convert, is_convert_dispatch,
    issue_rounding_rule, is_recalculate
  </sql>
</mapper>