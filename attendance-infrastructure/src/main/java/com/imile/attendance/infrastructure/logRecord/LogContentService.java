package com.imile.attendance.infrastructure.logRecord;

import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.logRecord.enums.OperationCodeEnum;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description 日历内容服务
 */
public interface LogContentService {

    /**
     * 通用日志内容,[国家]的[业务操作名称]
     */
    String commonLogContent(String country, String bizOperateName);

    /**
     * 生成新增，停启用日志内容
     * 新增日历: [中国]的[办公室日历]
     */
    String logContent(PageOperateType pageOperateType, String bizOperateCode, String country, String bizOperateName);

    /**
     * 生成新增，停启用日志内容
     * 新增日历: [中国]的[办公室日历]
     */
    String logContent(PageOperateType pageOperateType, OperationCodeEnum operationCodeEnum, String country, String bizName);

    /**
     * 生成页面更新日志内容
     * 修改日历: [中国]的[办公室日历]
     * 考勤配置名称:[测试日历-2]更新为[测试日历-2-update];
     */
    String pageUpdateLogContent(String bizOperate, String country, String bizOperateName, String fieldDiffJson);

    /**
     * 生成页面更新日志内容
     * 修改日历: [中国]的[办公室日历]
     * 考勤配置名称:[测试日历-2]更新为[测试日历-2-update];
     */
    String pageUpdateLogContent(OperationCodeEnum operationCodeEnum, String country, String bizOperateName, String fieldDiffJson);
}
