package com.imile.attendance.infrastructure.repository.punch.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} EmployeePunchRecordInspectionQuery
 * {@code @since:} 2025-01-14 20:26
 * {@code @description:}
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeePunchRecordInspectionQuery {
    /**
     * 打卡记录主键id集合
     */
    private List<Long> punchRecordIdList;
}
