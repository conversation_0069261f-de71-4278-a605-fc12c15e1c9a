package com.imile.attendance.infrastructure.repository.vacation.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveJourneyConfigDao;
import com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveJourneyConfigMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveJourneyConfigDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description 路途假 数据库操作实现类
 */
@Component
@RequiredArgsConstructor
public class CompanyLeaveJourneyConfigDaoImpl extends ServiceImpl<CompanyLeaveJourneyConfigMapper, CompanyLeaveJourneyConfigDO> implements CompanyLeaveJourneyConfigDao {


    @Override
    public List<CompanyLeaveJourneyConfigDO> selectByIssueRuleId(List<Long> issueRuleIdList) {

        if (CollUtil.isEmpty(issueRuleIdList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveJourneyConfigDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.in(CompanyLeaveJourneyConfigDO::getIssueRuleId, issueRuleIdList);
        lambdaQuery.eq(CompanyLeaveJourneyConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());

        return list(lambdaQuery);
    }

    @Override
    public List<CompanyLeaveJourneyConfigDO> getLeaveJourneyConfigList(Long issueRuleId) {
        if (ObjectUtil.isNull(issueRuleId)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveJourneyConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CompanyLeaveJourneyConfigDO::getIssueRuleId, issueRuleId);
        queryWrapper.eq(CompanyLeaveJourneyConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

}
