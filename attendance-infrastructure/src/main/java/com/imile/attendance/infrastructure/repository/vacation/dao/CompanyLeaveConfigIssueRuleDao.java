package com.imile.attendance.infrastructure.repository.vacation.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO;
import com.imile.attendance.infrastructure.repository.vacation.query.LeaveConfigIssueRuleQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21
 * @Description
 */
public interface CompanyLeaveConfigIssueRuleDao extends IService<CompanyLeaveConfigIssueRuleDO> {
    /**
     * 获取假期配置发放规则
     *
     * @param allCompanyLeaveConfigIdList 假期配置id
     * @return 假期配置发放规则
     */
    List<CompanyLeaveConfigIssueRuleDO> selectByLeaveId(List<Long> allCompanyLeaveConfigIdList);

    /**
     * 根据假期主表主键获取发放规则
     * @param id
     * @return
     */
    List<CompanyLeaveConfigIssueRuleDO> getLeaveConfigIssueRuleListById(Long id);

    /**
     * 根据条件获取发放规则数据
     * @param issueRuleQuery
     * @return
     */
    List<CompanyLeaveConfigIssueRuleDO> getLeaveConfigIssueRuleList(LeaveConfigIssueRuleQuery issueRuleQuery);

}
