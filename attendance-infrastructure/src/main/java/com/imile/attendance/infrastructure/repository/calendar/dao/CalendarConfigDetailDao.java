package com.imile.attendance.infrastructure.repository.calendar.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDetailQuery;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarConfigDetailDao
 * {@code @since:} 2025-01-17 15:13
 * {@code @description:}
 */
public interface CalendarConfigDetailDao extends IService<CalendarConfigDetailDO> {

    /**
     * 条件查询,查询日历配置最新的明细
     *
     * @param configDetailQuery
     * @return
     */
    List<CalendarConfigDetailDO> listLatestRecords(CalendarConfigDetailQuery configDetailQuery);

    /**
     * 将日历配置明细的指定的年份置为 非最新
     *
     * @param calendarConfigId
     * @param yearList
     */
    void updateToOld(Long calendarConfigId, List<Integer> yearList);

    /**
     * 条件查询,查询日历配置最新的明细
     *
     * @param configDetailQuery
     * @return
     */
    List<CalendarConfigDetailDO> listRecords(CalendarConfigDetailQuery configDetailQuery);

    /**
     * 根据考勤日历配置id查询日历配置明细
     */
    List<CalendarConfigDetailDO> selectListByConfigIds(List<Long> calendarConfigIds);

    /**
     * 根据日历配置ids + 年份查询数据
     */
    List<CalendarConfigDetailDO> selectListByConfigIdsAndYear(List<Long> calendarConfigIds, List<Integer> years);

    List<CalendarConfigDetailDO> listByPage(int currentPage, int pageSize);
}
