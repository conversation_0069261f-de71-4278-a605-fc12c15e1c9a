package com.imile.attendance.infrastructure.repository.employee.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
public interface UserLeaveDetailDao extends IService<UserLeaveDetailDO> {

    /**
     * 查询用户假期信息
     *
     * @param query
     * @return
     */
    List<UserLeaveDetailDO> selectUserLeaveDetail(UserLeaveDetailQuery query);

    /**
     * 根据用户主键查询用户假期信息
     *
     * @param userId
     * @return
     */
    List<UserLeaveDetailDO> selectUserLeaveByUserId(Long userId);

    /**
     * 查询假期配置绑定员工数
     * @param countryList 适用国家列表
     * @return List<CommonCountPO>
     */
//    List<CommonCountPO> selectLeaveConfigBindCount(List<String> countryList);
}
