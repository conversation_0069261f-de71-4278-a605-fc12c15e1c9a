package com.imile.attendance.infrastructure.repository.vacation.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigIssueRuleDao;
import com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveConfigIssueRuleMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO;
import com.imile.attendance.infrastructure.repository.vacation.query.LeaveConfigIssueRuleQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description 假期发放规则 数据库操作实现类
 */
@Component
@RequiredArgsConstructor
public class CompanyLeaveConfigIssueRuleDaoImpl extends ServiceImpl<CompanyLeaveConfigIssueRuleMapper, CompanyLeaveConfigIssueRuleDO> implements CompanyLeaveConfigIssueRuleDao {

    @Override
    public List<CompanyLeaveConfigIssueRuleDO> selectByLeaveId(List<Long> allCompanyLeaveConfigIdList) {
        if (CollUtil.isEmpty(allCompanyLeaveConfigIdList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigIssueRuleDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CompanyLeaveConfigIssueRuleDO::getLeaveId, allCompanyLeaveConfigIdList);
        queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<CompanyLeaveConfigIssueRuleDO> getLeaveConfigIssueRuleListById(Long leaveId) {
        if (ObjectUtil.isNull(leaveId)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigIssueRuleDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getLeaveId, leaveId);
        queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<CompanyLeaveConfigIssueRuleDO> getLeaveConfigIssueRuleList(LeaveConfigIssueRuleQuery issueRuleQuery) {
        if (ObjectUtil.isNull(issueRuleQuery)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigIssueRuleDO> queryWrapper = Wrappers.lambdaQuery();
        if (Objects.nonNull(issueRuleQuery.getLeaveId())) {
            queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getLeaveId, issueRuleQuery.getLeaveId());
        }
        if (Objects.nonNull(issueRuleQuery.getIssueFrequency())) {
            queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getIssueFrequency, issueRuleQuery.getIssueFrequency());
        }
        if (Objects.nonNull(issueRuleQuery.getIssueType())) {
            queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getIssueType, issueRuleQuery.getIssueType());
        }
        if (Objects.nonNull(issueRuleQuery.getCycleUnit())) {
            queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getCycleUnit, issueRuleQuery.getCycleUnit());
        }
        if (Objects.nonNull(issueRuleQuery.getCycleNumber())) {
            queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getCycleNumber, issueRuleQuery.getCycleNumber());
        }
        if (Objects.nonNull(issueRuleQuery.getIsConvert())) {
            queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getIsConvert, issueRuleQuery.getIsConvert());
        }
        if (Objects.nonNull(issueRuleQuery.getIsConvertDispatch())) {
            queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getIsConvertDispatch, issueRuleQuery.getIsConvertDispatch());
        }
        if (Objects.nonNull(issueRuleQuery.getIssueRoundingRule())) {
            queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getIssueRoundingRule, issueRuleQuery.getIssueRoundingRule());
        }
        if (Objects.nonNull(issueRuleQuery.getIsRecalculate())) {
            queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getIsRecalculate, issueRuleQuery.getIsRecalculate());
        }
        queryWrapper.eq(CompanyLeaveConfigIssueRuleDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
