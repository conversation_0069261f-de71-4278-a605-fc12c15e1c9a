package com.imile.attendance.infrastructure.repository.calendar.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarLegalLeaveConfigQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <p>
 * 日历法定假期配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-23
 */
@Mapper
@Repository
public interface CalendarLegalLeaveConfigMapper extends BaseMapper<CalendarLegalLeaveConfigDO> {
    List<CalendarLegalLeaveConfigDO> selectCalendarLegalLeaveConfigList(CalendarLegalLeaveConfigQuery query);

}
