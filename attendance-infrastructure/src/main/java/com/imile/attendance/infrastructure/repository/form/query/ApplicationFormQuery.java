package com.imile.attendance.infrastructure.repository.form.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationFormQuery {

    /**
     * 单据编码
     */
    private String applicationFormCode;

    /**
     * 被申请人ID
     */
    private Long userId;

    /**
     * 被申请人ID集合
     */
    private List<Long> userIdList;

    /**
     * 单据类型集合
     */
    private List<String> fromTypeList;

    /**
     * 单据状态集合
     */
    private List<String> statusList;

}
