package com.imile.attendance.infrastructure.repository.form.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@ApiModel(description = "审批单据—单据详情表（被申请人信息）")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_approval_form_user_info")
public class AttendanceApprovalFormUserInfoDO extends BaseDO {

    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 申请单ID
     */
    @ApiModelProperty(value = "申请单ID")
    private Long formId;

    /**
     * 日期：年月日
     */
    @ApiModelProperty(value = "日期：年月日")
    private Long dayId;

    /**
     * 关联表业务code（被申请人user_code）
     */
    @ApiModelProperty(value = "关联表业务code（被申请人user_code）")
    private String userCode;

    /**
     * 被申请人姓名
     */
    @ApiModelProperty(value = "被申请人姓名")
    private String userName;

    /**
     * 被申请人部门
     */
    @ApiModelProperty(value = "被申请人部门")
    private Long deptId;

    /**
     * 被申请人岗位
     */
    @ApiModelProperty(value = "被申请人岗位")
    private Long postId;

    /**
     * 被申请人所在国
     */
    @ApiModelProperty(value = "被申请人所在国")
    private String country;

    /**
     * 被申请人结算国
     */
    @ApiModelProperty(value = "被申请人结算国")
    private String originCountry;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endDate;

    /**
     * 请假时长(分钟)
     */
    @ApiModelProperty(value = "请假时长(分钟)")
    private BigDecimal estimateDuration;

    /**
     * 申请理由
     */
    @ApiModelProperty(value = "申请理由")
    private String remark;
}

