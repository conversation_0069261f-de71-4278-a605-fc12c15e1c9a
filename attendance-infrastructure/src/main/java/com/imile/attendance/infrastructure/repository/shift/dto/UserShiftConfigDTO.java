package com.imile.attendance.infrastructure.repository.shift.dto;

import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/21 
 * @Description
 */
@Data
public class UserShiftConfigDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户
     */
    private Long userId;

    /**
     * 员工账号
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 国家
     */
    private String country;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门名称（英文）
     */
    private String deptNameEn;

    /**
     * 部门名称（中文）
     */
    private String deptNameCn;

    /**
     * 岗位ID
     */
    private Long postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位名称（英文）
     */
    private String postNameEn;

    /**
     * 岗位名称（中文）
     */
    private String postNameCn;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 用工类型描述
     */
    private String employeeTypeDesc;

    /**
     * 在职状态
     */
    private String workStatus;

    /**
     * 常驻国家
     */
    private String locationCountry;

    /**
     * 常驻省份
     */
    private String locationProvince;

    /**
     * 常驻城市
     */
    private String locationCity;

    /**
     * 入职日期
     */
    private String entryDate;

    /**
     * 离职日期
     */
    private String dimissionDate;

    /**
     * 是否司机
     */
    private Integer isDriver;

    /**
     * 是否全球派遣
     */
    private Integer isGlobalRelocation;

    /**
     * 班次分类
     */
    private String classNature;

    /**
     * 班次适配数
     */
    private Integer relateClassCount;

    /**
     * 用户适配的班次
     */
    private List<PunchClassConfigSelectDTO> classConfigSelectDTOList;

    /**
     * 班次信息
     */
    private List<ShiftDayConfigDTO> classDetail;

    /**
     * 日历名称
     */
    private String calendarConfigName;

    /**
     * 是否是循环排班 1是
     */
    private Integer isCycleShift;

    /**
     * 是否排班限制 0:不限制 1:限制 (如果为1，需提示MEX仓内外包人员无需提前排班)
     */
    private Integer schedulingLimit = 0;

    /**
     * 无日历导致的排班限制 0:不限制 1:限制 (如果为1，需提示该人员无日历不可进行排班，请先配置日历)
     */
    private Integer noCalendarSchedulingLimit = 0;
}
