package com.imile.attendance.infrastructure.easylog;

import com.github.easylog.service.IOperatorService;
import com.imile.ucenter.api.authenticate.UcenterUtils;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class OperatorGetService implements IOperatorService {

    @Override
    public String getOperator() {
        return Optional.ofNullable(UcenterUtils.getUserInfo()).map(UserInfoDTO::getUserCode).orElse(Strings.EMPTY);
    }

    @Override
    public String getPlatform() {
        return "attendance";
    }
}
