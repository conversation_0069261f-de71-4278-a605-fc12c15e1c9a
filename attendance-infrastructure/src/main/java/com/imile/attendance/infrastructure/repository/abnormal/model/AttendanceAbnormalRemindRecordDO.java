package com.imile.attendance.infrastructure.repository.abnormal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 异常提醒发送记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_abnormal_remind_record")
public class AttendanceAbnormalRemindRecordDO extends BaseDO {

    private static final long serialVersionUID = -43692653024388852L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 异常日期
     */
    private Long dayId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 异常Id
     */
    private String abnormalIds;

    /**
     * 打卡班次id
     */
    private Long punchClassConfigId;

    /**
     * 打卡时段id
     */
    private String punchClassItemConfigIds;

    /**
     * 消息状态
     */
    private Integer sendStatus;

    /**
     * 消息类型(0:自动 1:手动)
     */
    private Integer sendType;

    /**
     * 失败消息
     */
    private String sendMsg;
}
