package com.imile.attendance.infrastructure.repository.employee.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.enums.vacation.AttendanceInvalidEnum;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveStageDetailDao;
import com.imile.attendance.infrastructure.repository.employee.mapper.UserLeaveStageDetailMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveStageDetailQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Component
@RequiredArgsConstructor
public class UserLeaveStageDetailDaoImpl extends ServiceImpl<UserLeaveStageDetailMapper, UserLeaveStageDetailDO> implements UserLeaveStageDetailDao {

    @Resource
    private UserLeaveStageDetailDao userLeaveStageDetailDao;

    @Override
    public List<UserLeaveStageDetailDO> selectByLeaveId(List<Long> leaveIdList) {
        if (CollectionUtils.isEmpty(leaveIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserLeaveStageDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLeaveStageDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserLeaveStageDetailDO::getIsInvalid, AttendanceInvalidEnum.NO.getCode());
        queryWrapper.in(UserLeaveStageDetailDO::getLeaveId, leaveIdList);
        return userLeaveStageDetailDao.list(queryWrapper);
    }

    @Override
    public List<UserLeaveStageDetailDO> selectById(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserLeaveStageDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLeaveStageDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(UserLeaveStageDetailDO::getId, idList);
        return userLeaveStageDetailDao.list(queryWrapper);
    }

    /**
     * 通过条件查询
     *
     * @param userLeaveStageDetailQuery 查询条件
     * @return 查询结果
     */
    @Override
    public List<UserLeaveStageDetailDO> selectByCondition(UserLeaveStageDetailQuery userLeaveStageDetailQuery) {
        if (ObjectUtil.isNull(userLeaveStageDetailQuery) || CollectionUtils.isEmpty(userLeaveStageDetailQuery.getLeaveIdList())) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserLeaveStageDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserLeaveStageDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(CollectionUtils.isNotEmpty(userLeaveStageDetailQuery.getLeaveIdList()),
                UserLeaveStageDetailDO::getLeaveId,
                userLeaveStageDetailQuery.getLeaveIdList());
        queryWrapper.eq(ObjectUtil.isNotNull(userLeaveStageDetailQuery.getLeaveMark()),
                UserLeaveStageDetailDO::getLeaveMark,
                userLeaveStageDetailQuery.getLeaveMark());
        queryWrapper.eq(ObjectUtil.isNotNull(userLeaveStageDetailQuery.getIsInvalid()),
                UserLeaveStageDetailDO::getIsInvalid,
                userLeaveStageDetailQuery.getIsInvalid());
        return userLeaveStageDetailDao.list(queryWrapper);
    }
}
