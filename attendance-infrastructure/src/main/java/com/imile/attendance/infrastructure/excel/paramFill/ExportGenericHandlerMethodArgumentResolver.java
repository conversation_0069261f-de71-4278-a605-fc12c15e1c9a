package com.imile.attendance.infrastructure.excel.paramFill;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.imile.attendance.annon.ExportParamFill;
import com.imile.common.query.BaseQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2024/12/3 
 * @Description
 */
@Slf4j
public class ExportGenericHandlerMethodArgumentResolver implements HandlerMethodArgumentResolver, Ordered {

    private final ObjectMapper objectMapper;
    private final IpepParameterFillResolver ipepParameterFillResolver;

    public ExportGenericHandlerMethodArgumentResolver(ObjectMapper objectMapper,
                                                      IpepParameterFillResolver ipepParameterFillResolver) {
        this.objectMapper = objectMapper;
        this.ipepParameterFillResolver = ipepParameterFillResolver;
    }

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        Method method = parameter.getMethod();
        if (method == null) {
            return false;
        }
        ExportParamFill annotation = AnnotationUtils.findAnnotation(method, ExportParamFill.class);
        Class<?> parameterType = parameter.getParameterType();
        return annotation != null && BaseQuery.class.isAssignableFrom(parameterType);
    }

    @Override
    public Object resolveArgument(@NotNull MethodParameter parameter, ModelAndViewContainer mavContainer, @NotNull NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        HttpServletRequest request = (HttpServletRequest) webRequest.getNativeRequest();
        Class<?> parameterType = parameter.getParameterType();
        Object instance = parameterType.getDeclaredConstructor().newInstance();
        for (Field field : FieldUtils.getAllFields(parameterType)) {
            field.setAccessible(true);
            String[] paramValue = request.getParameterValues(field.getName());
            if (paramValue != null && paramValue.length > 0) {
                Object value = parseValue(paramValue[0], field.getType(), field.getGenericType());
                field.set(instance, value);
            }
        }
        //只从IPEP获取
        ipepParameterFillResolver.resolve((BaseQuery) instance);
//        ParameterFillResolver parameterFillResolver = ExportParamFillResolverManager.get(ExportParamFill.Source.IPEP);
//        if (parameterFillResolver != null && instance instanceof BaseQuery) {
//            parameterFillResolver.resolve((BaseQuery) instance);
//        }
        return instance;
    }

    private Object parseValue(String value, Class<?> type, Type genericType) throws Exception {
        JsonNode jsonNode;
        boolean typeJSON = JSONUtil.isTypeJSON(value);
        if (typeJSON) {
            jsonNode = objectMapper.readTree(value);
        } else {
            // 非 JSON 数据，根据 genericType 处理
            if (genericType instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) genericType;
                Type rawType = parameterizedType.getRawType();

                // 判断是否为集合int类型
                if (rawType == List.class) {
                    Type itemType = parameterizedType.getActualTypeArguments()[0];
                    Class<?> itemClass = (Class<?>) itemType;
                    if (itemClass == String.class) {
                        return Arrays.stream(value.split(","))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .collect(Collectors.toList());
                    } else if (itemClass == Integer.class || itemClass == int.class) {
                        return Arrays.stream(value.split(","))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .mapToInt(Integer::parseInt)
                                .boxed()
                                .collect(Collectors.toList());
                    } else if (itemClass == Long.class || itemClass == long.class) {
                        return Arrays.stream(value.split(","))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .mapToLong(Long::parseLong)
                                .boxed()
                                .collect(Collectors.toList());
                    } else if (itemClass == Double.class || itemClass == double.class) {
                        return Arrays.stream(value.split(","))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .mapToDouble(Double::parseDouble)
                                .boxed()
                                .collect(Collectors.toList());
                    }

                }
            }
            // 默认按文本处理
            jsonNode = JsonNodeFactory.instance.textNode(value);
        }
        return objectMapper.convertValue(jsonNode, objectMapper.constructType(genericType));
    }

//    // 解析列表类型的辅助方法
//    private List<Object> parseList(String value, Class<?> itemType) throws Exception {
//        // 假设列表为 JSON 数组格式
//        JsonNode arrayNode = objectMapper.readTree(value);
//        List<Object> list = new ArrayList<>();
//        if (arrayNode.isArray()) {
//            for (JsonNode node : arrayNode) {
//                list.add(objectMapper.convertValue(node, itemType));
//            }
//        }
//        return list;
//    }

    // 解析 Map 类型的辅助方法
//    private Map<Object, Object> parseMap(String value, Class<?> keyType, Class<?> valueType) throws Exception {
//        // 假设 Map 为 JSON 对象格式
//        JsonNode objectNode = objectMapper.readTree(value);
//        Map<Object, Object> map = new HashMap<>();
//        if (objectNode.isObject()) {
//            objectNode.fields().forEachRemaining(entry -> {
//                Object key = objectMapper.convertValue(entry.getKey(), keyType);
//                Object val = objectMapper.convertValue(entry.getValue(), valueType);
//                map.put(key, val);
//            });
//        }
//        return map;
//    }


    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
}
