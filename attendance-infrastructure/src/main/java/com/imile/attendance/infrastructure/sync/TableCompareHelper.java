package com.imile.attendance.infrastructure.sync;

import com.imile.attendance.exception.DataDifferenceException;
import com.imile.attendance.infrastructure.sync.dto.TableCountCompareResult;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26 
 * @Description 表数据比对工具类
 */
@Slf4j
public class TableCompareHelper {

    public static <S, T> void compareTable(String tableName,
                                           BiFunction<Integer, Integer, List<S>> oldDataProvider,
                                           BiFunction<Integer, Integer, List<T>> newDataProvider,
                                           BiConsumer<S, T> compareFunction) {
        XxlJobLogger.log("=======开始比较{}==========", tableName);

        // 第一步：收集所有数据的ID并比较数据量
        TableCountCompareResult tableCountCompareResult = compareDataCount(tableName, oldDataProvider, newDataProvider);

        // 如果数据量不一致，直接报告差异后返回
        if (!tableCountCompareResult.isCountMatch()) {
            String summary = String.format("%s 表存在数据量差异，停止字段比对", tableName);
            XxlJobLogger.log(summary);
            sendAlertMessage(summary);
            return;
        }

        // 数据量一致才进行字段比对
        compareDataFields(tableName, oldDataProvider, newDataProvider, compareFunction);

        XxlJobLogger.log("=======比较{}结束==========", tableName);
    }

    private static <S, T> TableCountCompareResult compareDataCount(String tableName,
                                                                   BiFunction<Integer, Integer, List<S>> oldDataProvider,
                                                                   BiFunction<Integer, Integer, List<T>> newDataProvider) {
        int pageSize = 1000;
        int currentPage = 1;

        Set<Object> oldIds = new HashSet<>();
        Set<Object> newIds = new HashSet<>();

        while (true) {
            List<S> oldData = oldDataProvider.apply(currentPage, pageSize);
            List<T> newData = newDataProvider.apply(currentPage, pageSize);

            if (CollectionUtils.isEmpty(oldData) && CollectionUtils.isEmpty(newData)) {
                break;
            }

            // 收集本页数据的ID
            oldData.forEach(item -> oldIds.add(ReflectGetIdUtil.extractId(item)));
            newData.forEach(item -> newIds.add(ReflectGetIdUtil.extractId(item)));

            if (oldData.size() < pageSize && newData.size() < pageSize) {
                break;
            }
            currentPage++;
        }

        // 比较差异并记录日志
        Set<Object> onlyInOld = new HashSet<>(oldIds);
        onlyInOld.removeAll(newIds);

        Set<Object> onlyInNew = new HashSet<>(newIds);
        onlyInNew.removeAll(oldIds);

        if (!onlyInOld.isEmpty()) {
            String errorMsg = String.format("%s 表中仅在旧表存在的记录(共%d条): %s",
                    tableName, onlyInOld.size(), String.join(", ", convertToStringList(onlyInOld)));
            logError(errorMsg);
        }

        if (!onlyInNew.isEmpty()) {
            String errorMsg = String.format("%s 表中仅在新表存在的记录(共%d条): %s",
                    tableName, onlyInNew.size(), String.join(", ", convertToStringList(onlyInNew)));
            logError(errorMsg);
        }

        return new TableCountCompareResult(oldIds.size() == newIds.size(), oldIds.size(), newIds.size());
    }

    private static void logError(String message) {
        log.error(message);
        XxlJobLogger.log(message);
    }

    private static void sendAlertMessage(String message) {
        // 实现告警发送逻辑，如调用消息推送服务
    }

    private static <S, T> void compareDataFields(String tableName,
                                                BiFunction<Integer, Integer, List<S>> oldDataProvider,
                                                BiFunction<Integer, Integer, List<T>> newDataProvider,
                                                BiConsumer<S, T> compareFunction) {
        int pageSize = 1000;
        int currentPage = 1;
        int totalDiff = 0;

        while (true) {
            List<S> oldData = oldDataProvider.apply(currentPage, pageSize);
            List<T> newData = newDataProvider.apply(currentPage, pageSize);

            if (CollectionUtils.isEmpty(oldData) && CollectionUtils.isEmpty(newData)) {
                break;
            }

            // 逐条比较字段
            for (int i = 0; i < oldData.size(); i++) {
                try {
                    compareFunction.accept(oldData.get(i), newData.get(i));
                } catch (DataDifferenceException e) {
                    logError(String.format("%s 表数据字段不一致: %s", tableName, e.getMessage()));
                    totalDiff++;
                }
            }

            if (oldData.size() < pageSize) {
                break;
            }
            currentPage++;
        }

        if (totalDiff > 0) {
            String summary = String.format("%s 表共发现 %d 处字段差异", tableName, totalDiff);
            XxlJobLogger.log(summary);
            sendAlertMessage(summary);
        }
    }

    private static List<String> convertToStringList(Set<Object> set) {
        return set.stream()
                .map(Object::toString)
                .collect(Collectors.toList());
    }
}
