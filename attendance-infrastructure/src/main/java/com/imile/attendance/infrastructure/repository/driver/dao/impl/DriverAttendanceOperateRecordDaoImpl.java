package com.imile.attendance.infrastructure.repository.driver.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverAttendanceOperateRecordDao;
import com.imile.attendance.infrastructure.repository.driver.mapper.DriverAttendanceOperateRecordMapper;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceOperateRecordDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceOperateRecordQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@Component
@RequiredArgsConstructor
public class DriverAttendanceOperateRecordDaoImpl extends ServiceImpl<DriverAttendanceOperateRecordMapper, DriverAttendanceOperateRecordDO>
        implements DriverAttendanceOperateRecordDao {


    @Override
    public List<DriverAttendanceOperateRecordDO> listAttendanceOperateRecordDetail(DriverAttendanceOperateRecordQuery query) {
        if(ObjectUtil.isNull(query)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DriverAttendanceOperateRecordDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(DriverAttendanceOperateRecordDO::getCountry, query.getCountry());
        queryWrapper.ge(DriverAttendanceOperateRecordDO::getCreateDate, query.getStartTime());
        queryWrapper.lt(DriverAttendanceOperateRecordDO::getCreateDate, query.getEndTime());
        queryWrapper.eq(DriverAttendanceOperateRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(DriverAttendanceOperateRecordDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<DriverAttendanceOperateRecordDO> listByPage(int currentPage, int pageSize) {
        PageInfo<DriverAttendanceOperateRecordDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}

