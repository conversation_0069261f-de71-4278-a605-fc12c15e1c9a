package com.imile.attendance.infrastructure.repository.form.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormRelationDao;
import com.imile.attendance.infrastructure.repository.form.mapper.AttendanceFormRelationMapper;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceFormRelationDaoImpl extends ServiceImpl<AttendanceFormRelationMapper, AttendanceFormRelationDO> implements AttendanceFormRelationDao {

    @Override
    public List<AttendanceFormRelationDO> selectRelationByFormIdList(List<Long> formIdList) {
        if (CollectionUtils.isEmpty(formIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AttendanceFormRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceFormRelationDO::getIsDelete, BusinessConstant.N);
        queryWrapper.in(AttendanceFormRelationDO::getFormId, formIdList);
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceFormRelationDO> selectRelationByRelationIdList(List<Long> relationIdList) {
        if (CollectionUtils.isEmpty(relationIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AttendanceFormRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceFormRelationDO::getIsDelete, BusinessConstant.N);
        queryWrapper.in(AttendanceFormRelationDO::getRelationId, relationIdList);
        return this.list(queryWrapper);
    }
}

