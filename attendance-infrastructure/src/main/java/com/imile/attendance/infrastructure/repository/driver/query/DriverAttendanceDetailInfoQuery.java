package com.imile.attendance.infrastructure.repository.driver.query;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class DriverAttendanceDetailInfoQuery implements Serializable {

    private static final long serialVersionUID = -3580280380743195420L;
    /**
     * 司机账号
     */
    private String userCode;

    /**
     * 司机账号集合
     */
    private List<String> userCodeList;

    /**
     * day_id 示例：20240124
     */
    private Long dayId;

    /**
     * year 示例：2024
     */
    private Long year;

    /**
     * 月报：考勤范围开始时间
     */
    private Long monthStartDate;

    /**
     * 月报：考勤范围结束时间
     */
    private Long monthEndDate;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    private List<Integer> attendanceTypeList;

}
