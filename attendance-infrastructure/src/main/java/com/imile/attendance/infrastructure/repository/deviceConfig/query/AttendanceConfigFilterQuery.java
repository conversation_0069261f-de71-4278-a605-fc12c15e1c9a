package com.imile.attendance.infrastructure.repository.deviceConfig.query;


import com.imile.attendance.query.ResourceQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/4/29
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceConfigFilterQuery extends ResourceQuery {

    /**
     * 国家
     */
    private String country;

    /**
     * 查询类型(0：国家 1：城市)
     */
    private Integer type;
}
