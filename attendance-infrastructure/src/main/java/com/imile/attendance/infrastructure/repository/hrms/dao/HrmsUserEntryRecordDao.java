package com.imile.attendance.infrastructure.repository.hrms.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserEntryRecordDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20 
 * @Description
 */
public interface HrmsUserEntryRecordDao extends IService<HrmsUserEntryRecordDO> {


    HrmsUserEntryRecordDO getByUserId(Long userId);

    /**
     * 查询用户入职记录
     */
    List<HrmsUserEntryRecordDO> listByUserIds(List<Long> userIds);


    List<HrmsUserEntryRecordDO> listByPage(int currentPage, int pageSize);


}
