package com.imile.attendance.infrastructure.repository.deviceConfig.query;


import com.imile.attendance.query.ResourceQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceWifiConfigQuery extends ResourceQuery {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 主键id
     */
    private List<Long> ids;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String locationCity;

    /**
     * wifi名称
     */
    private String wifiName;

    /**
     * mac地址
     */
    private String macAddress;

    /**
     * 国家列表
     */
    private List<String> countryList;
}
