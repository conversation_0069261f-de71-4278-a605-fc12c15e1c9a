package com.imile.attendance.infrastructure.repository.shift.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR> chen
 * @Date 2025/4/21
 * @Description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShiftConfigUpdateToOldDTO {
    /**
     * 日历ID
     */
    private Long attendanceConfigId;

    /**
     * 考勤日
     */
    private Long dayId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 开始时间
     */
    private Long startDayId;

    /**
     * 排班类型,自动排班、循环排班、自定义排班 ShiftTypeEnum
     */
    private List<String> conditionShiftTypeList;
}
