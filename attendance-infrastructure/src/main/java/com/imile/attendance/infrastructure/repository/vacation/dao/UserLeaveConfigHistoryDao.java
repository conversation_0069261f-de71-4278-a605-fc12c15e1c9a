package com.imile.attendance.infrastructure.repository.vacation.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.vacation.model.UserLeaveConfigHistoryDO;
import com.imile.attendance.infrastructure.repository.vacation.query.UserLeaveConfigHistoryQuery;

import java.util.List;


/**
 * <p>
 * 人员常驻国切换历史假期范围表 数据库操作
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
public interface UserLeaveConfigHistoryDao extends IService<UserLeaveConfigHistoryDO> {

    List<UserLeaveConfigHistoryDO> selectLeaveHistoryInfoByUserId(Long userId);

    List<UserLeaveConfigHistoryDO> selectLeaveHistoryInfo(UserLeaveConfigHistoryQuery query);
}
