package com.imile.attendance.infrastructure.sync;

import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26 
 * @Description 表同步工具类
 */
@Slf4j
public class TableSyncHelper {

    /**
     * 通用的表同步方法
     *
     * @param tableName    表名，用于日志记录
     * @param dataProvider 分页查询数据的方法
     * @param converter    数据转换器
     * @param batchSaver   批量保存方法
     * @param <S>          源数据类型
     * @param <T>          目标数据类型
     */
    public static <S, T> void syncTable(String tableName,
                                        BiFunction<Integer, Integer, List<S>> dataProvider,
                                        Function<S, T> converter,
                                        Consumer<List<T>> batchSaver) {
        XxlJobLogger.log("=======全量同步{}开始==========", tableName);
        log.info("=======全量同步{}开始==========", tableName);

        int pageSize = 1000; // 每页查询的数据量
        int currentPage = 1;
        List<S> pageData;

        while (true) {
            // 分页查询数据
            pageData = dataProvider.apply(currentPage, pageSize);
            if (CollectionUtils.isEmpty(pageData)) {
                break; // 如果没有更多数据，则退出循环
            }

            // 转换数据
            List<T> convertedData = pageData.stream()
                    .map(converter)
                    .collect(Collectors.toList());
            saveData(convertedData, batchSaver);
            log.info("{}同步数量={}", tableName, convertedData.size());
            XxlJobLogger.log("{}同步数量={}", tableName, convertedData.size());

            // 如果当前页数据量小于 pageSize，说明已经处理完所有数据，退出循环
            if (pageData.size() < pageSize) {
                break;
            }

            currentPage++;
        }

        XxlJobLogger.log("=======全量同步{}结束==========", tableName);
        log.info("=======全量同步{}结束==========", tableName);
    }

    /**
     * 保存数据，支持分批处理
     *
     * @param list     数据列表
     * @param consumer 批量保存逻辑
     * @param <T>      数据类型
     */
    public static <T> void saveData(List<T> list, Consumer<List<T>> consumer) {
        int batchSize = 500; // 每次批量插入的数据量
        if (list.size() > batchSize) {
            Lists.partition(list, batchSize).forEach(consumer);
        } else {
            consumer.accept(list);
        }
    }
}
