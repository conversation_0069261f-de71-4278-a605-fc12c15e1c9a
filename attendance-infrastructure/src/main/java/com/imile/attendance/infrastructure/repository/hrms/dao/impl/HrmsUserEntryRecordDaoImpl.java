package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsUserEntryRecordMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserEntryRecordDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20 
 * @Description
 */
@Component
@DS(Constants.TableSchema.hrms)
@RequiredArgsConstructor
public class HrmsUserEntryRecordDaoImpl extends ServiceImpl<HrmsUserEntryRecordMapper, HrmsUserEntryRecordDO> implements HrmsUserEntryRecordDao {

    @Override
    public HrmsUserEntryRecordDO getByUserId(Long userId) {
        if (null == userId) {
            return null;
        }
        LambdaQueryWrapper<HrmsUserEntryRecordDO> queryWrapper = Wrappers.lambdaQuery(HrmsUserEntryRecordDO.class);
        queryWrapper.eq(HrmsUserEntryRecordDO::getUserId, userId);
        queryWrapper.eq(HrmsUserEntryRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.getOne(queryWrapper);
    }

    @Override
    public List<HrmsUserEntryRecordDO> listByUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsUserEntryRecordDO> queryWrapper = Wrappers.lambdaQuery(HrmsUserEntryRecordDO.class);
        queryWrapper.eq(HrmsUserEntryRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsUserEntryRecordDO::getUserId, userIds);
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsUserEntryRecordDO> listByPage(int currentPage, int pageSize) {
        PageInfo<HrmsUserEntryRecordDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}
