package com.imile.attendance.infrastructure.repository.rule.query;

import java.util.List;

import com.imile.attendance.base.PermissionBaseQuery;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 补卡规则分页查询
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class ReissueCardConfigPageQuery extends PermissionBaseQuery {

    /**
     * 补卡规则id列表
     */
    private List<Long> configIds;

    /**
     * 国家
     */
    private String country;


    /**
     * 补卡规则名称
     */
    private String configName;


    /**
     * 部门列表
     */
    private List<Long> deptIds;

    /**
     * 状态
     */
    private String status;

    /**
     * 用户id列表
     */
    private List<Long> userIdList;
    
}
