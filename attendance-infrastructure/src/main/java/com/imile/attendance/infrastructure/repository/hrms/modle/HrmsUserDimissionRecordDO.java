package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 用户离职记录表 系统-员工
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_user_dimission_record")
public class HrmsUserDimissionRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id 用户ID
     */
    private Long userId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 计划离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planDimissionDate;

    /**
     * 实际离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actualDimissionDate;

    /**
     * 离职状态 离职状态：待离职、已离职、取消离职、在职
     */
    private String dimissionStatus;

    /**
     * 离职原因 离职原因
     */
    private String dimissionReason;
    /**
     * 工作交接人ID
     */
    private Long transfereeId;

    /**
     * 描述
     */
    private String remark;

    /**
     * 排序
     */
    private BigDecimal orderby;

    /**
     * 扩展信息
     */
    private String extendInfo;

    /**
     * 取消离职原因
     */
    private String approvalRemark;

    /**
     * 离职辞退审批流id
     */
    private Long approvalId;

    /**
     * 清算审批流id
     */
    private Long approvalLiquidationId;

    /**
     * 离职类型:01离职 02辞退
     */
    private String resignationType;

    /**
     * hr附件
     */
    private String hrAttachments;

    /**
     * 离职渠道：01-clover离职 02-hrms直接离职 03-hrms办理离职
     */
    private String resignationChannels;
}
