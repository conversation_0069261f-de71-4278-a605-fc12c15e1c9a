package com.imile.attendance.infrastructure.repository.calendar.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.enums.AttendanceTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigQuery;


import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarConfigDao
 * {@code @since:} 2025-01-17 13:42
 * {@code @description:}
 */
public interface CalendarConfigDao extends IService<CalendarConfigDO> {

    /**
     * 查询国家下的所有日历
     */
    List<CalendarConfigDO> selectByCountryList(List<String> countryList);

    /**
     * 根据ID查询最新启用的日历
     */
    CalendarConfigDO getActiveById(Long id);

    /**
     * 统计国家下默认日历方案数量
     */
    Integer countDefaultCalendarConfig(String country);

    List<CalendarConfigDO> listByQuery(CalendarConfigQuery query);

    List<CalendarConfigDO> listByIds(Collection<? extends Serializable> idList);

    List<CalendarConfigDO> listByNos(Collection<String> attendanceConfigNos);

    /**
     * 根据国家查询最新启用的日历配置
     */
    List<CalendarConfigDO> listByCountry(String country);

    /**
     * 获取国家对应类型的日历配置
     */
    List<CalendarConfigDO> getCountryCalendarByType(AttendanceTypeEnum calendarType, String country);


    List<CalendarConfigDO> getByCalendarConfigIds(List<Long> calendarConfigIds);


    List<CalendarConfigDO> listByPage(int currentPage, int pageSize);
}
