package com.imile.attendance.infrastructure.mq;

import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.mq.constant.MqRetryStatusEnum;
import com.imile.attendance.infrastructure.mq.dto.MqRetryParam;
import com.imile.attendance.infrastructure.mq.dto.MqRetryResult;
import com.imile.attendance.infrastructure.repository.base.dao.MqFailRecordDao;
import com.imile.attendance.infrastructure.repository.base.model.MqFailRecordDO;
import com.imile.attendance.infrastructure.spring.SpringApplicationUtil;
import com.imile.attendance.util.BaseDOUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/8
 * @Description
 */
@Slf4j
@Service
public class MqFailRecordService {

    @Resource
    private MqFailRecordDao mqFailRecordDao;
    @Resource
    private DefaultIdWorker defaultIdWorker;


    public void saveOrUpdateMqRecord(MqRetryParam mqRetryParam) {
        MessageExt messageExt = mqRetryParam.getMessageExt();
        String msgId = messageExt.getMsgId();
        MqFailRecordDO mqFailRecord = mqFailRecordDao.getByMsgId(msgId);
        if (mqFailRecord == null) {
            MqFailRecordDO mqFailRecordDO = new MqFailRecordDO();
            mqFailRecordDO.setId(defaultIdWorker.nextId());
            mqFailRecordDO.setTopic(messageExt.getTopic());
            mqFailRecordDO.setTag(messageExt.getTags());
            mqFailRecordDO.setMsgKey(messageExt.getKeys());
            mqFailRecordDO.setMsgId(messageExt.getMsgId());
            mqFailRecordDO.setMsgBody(JSONObject.toJSONString(messageExt));
            // 添加重试服务Bean名称
            mqFailRecordDO.setMsgRetryServiceBean(mqRetryParam.getRetryServiceBean());
            mqFailRecordDO.setStatus(MqRetryStatusEnum.INIT.getCode());
            mqFailRecordDO.setRetryCount(0);
            mqFailRecordDO.setErrorMsg(mqRetryParam.getException().getMessage());
            mqFailRecordDO.setErrorStack(ExceptionUtils.getRootCauseMessage(mqRetryParam.getException()));
            BaseDOUtil.fillDOInsert(mqFailRecordDO);
            mqFailRecordDao.save(mqFailRecordDO);
            return;
        }
        // 更新记录
        Integer retryCount = mqFailRecord.getRetryCount() + 1;
        mqFailRecord.setRetryCount(retryCount);
        if (MqRetryResult.isReachMaxRetry(retryCount)) {
            mqFailRecord.setStatus(MqRetryStatusEnum.RETRY_FAIL.getCode());
        }
        mqFailRecord.setMsgBody(JSONObject.toJSONString(messageExt));
        mqFailRecord.setErrorMsg(mqRetryParam.getException().getMessage());
        mqFailRecord.setErrorStack(ExceptionUtils.getRootCauseMessage(mqRetryParam.getException()));
        mqFailRecord.setLastRetryTime(new Date());
        BaseDOUtil.fillDOUpdate(mqFailRecord);
        mqFailRecordDao.updateById(mqFailRecord);
    }

    /**
     * 更新重试成功的消息状态
     * @param msgId 消息ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRetrySuccessResultIfNecessary(String msgId) {
        if (StringUtils.isEmpty(msgId)) {
            return;
        }
        MqFailRecordDO mqFailRecord = mqFailRecordDao.getByMsgId(msgId);
        if (null == mqFailRecord) {
            return;
        }

        // 只有非成功状态的消息才需要更新
        if (!MqRetryStatusEnum.RETRY_SUCCESS.getCode().equals(mqFailRecord.getStatus())) {
            Integer retryCount = mqFailRecord.getRetryCount() + 1;
            mqFailRecord.setRetryCount(retryCount);
            mqFailRecord.setStatus(MqRetryStatusEnum.RETRY_SUCCESS.getCode());
            mqFailRecord.setLastRetryTime(new Date());
            mqFailRecord.setErrorMsg("处理成功");
            mqFailRecord.setErrorStack(null);
            BaseDOUtil.fillDOUpdate(mqFailRecord);
            mqFailRecordDao.updateById(mqFailRecord);
            log.info("更新消息状态为重试成功，msgId:{}, retryCount:{}", msgId, retryCount);
        }
    }

    /**
     * 根据消息ID重试消息
     * @param msgId 消息ID
     * @return 重试结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void mqRetryByMsgId(String msgId) {
        // 参数校验
        if (StringUtils.isEmpty(msgId)) {
            return;
        }

        // 查询消息记录
        MqFailRecordDO mqFailRecord = mqFailRecordDao.getByMsgId(msgId);
        if (null == mqFailRecord) {
            log.warn("消息重试失败，未找到消息记录，msgId:{}", msgId);
            return;
        }

        // 检查消息状态
        if (MqRetryStatusEnum.RETRY_SUCCESS.getCode().equals(mqFailRecord.getStatus())) {
            log.info("消息已经重试成功，无需再次重试，msgId:{}", msgId);
            return;
        }

        // 检查重试次数
        if (MqRetryResult.isReachMaxRetry(mqFailRecord.getRetryCount())) {
            log.warn("消息重试失败，已达到最大重试次数，msgId:{}, retryCount:{}",
                    msgId, mqFailRecord.getRetryCount());
            return;
        }

        // 获取重试服务Bean
        String msgRetryServiceBean = mqFailRecord.getMsgRetryServiceBean();
        if (StringUtils.isEmpty(msgRetryServiceBean)) {
            log.warn("消息重试失败，未找到重试服务Bean，msgId:{}", msgId);
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "未找到重试服务Bean");
        }

        // 解析消息体
        String msgBody = mqFailRecord.getMsgBody();
        MessageExt messageExt = JSONObject.parseObject(msgBody, MessageExt.class);

        // 获取监听器Bean
        BaseRocketMQListener baseRocketMQListener = SpringApplicationUtil.getBean(msgRetryServiceBean);
        if (baseRocketMQListener == null) {
            log.error("消息重试失败，未找到监听器Bean实例，msgId:{}, beanName:{}",
                    msgId, msgRetryServiceBean);
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "未找到监听器Bean实例");
        }

        // 执行重试
        log.info("开始重试消息，msgId:{}, retryCount:{}", msgId, mqFailRecord.getRetryCount() + 1);
        baseRocketMQListener.onMessage(messageExt);
    }
}
