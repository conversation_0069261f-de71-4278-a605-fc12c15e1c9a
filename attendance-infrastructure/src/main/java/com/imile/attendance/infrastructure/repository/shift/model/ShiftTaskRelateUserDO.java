package com.imile.attendance.infrastructure.repository.shift.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/4/19 
 * @Description
 */
@ApiModel(description = "排班任务关联用户表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("shift_task_relate_user")
public class ShiftTaskRelateUserDO extends BaseDO {

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "排班任务记录ID")
    private Long shiftTaskRecordId;

    @ApiModelProperty(value = "用户ID")
    private Long userId;
}
