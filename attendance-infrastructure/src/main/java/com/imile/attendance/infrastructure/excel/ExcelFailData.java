package com.imile.attendance.infrastructure.excel;

import com.google.common.collect.Lists;
import com.imile.common.excel.ExcelImport;
import lombok.Data;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Excel导入失败数据收集器
 *
 * <p>用于收集和管理Excel导入过程中失败的数据记录，提供以下功能：</p>
 * <ul>
 *   <li>收集导入失败的数据记录</li>
 *   <li>避免同一行数据的重复添加</li>
 *   <li>按行号排序返回失败数据列表</li>
 *   <li>检查是否存在失败数据</li>
 * </ul>
 *
 * <p>使用示例：</p>
 * <pre>{@code
 * ExcelFailData<UserShiftImportDTO> excelFailData = new ExcelFailData<>();
 *
 * // 添加失败数据
 * excelFailData.putData(importDTO, "用户编码不能为空");
 *
 * // 检查是否有失败数据
 * if (excelFailData.isNotEmpty()) {
 *     return excelFailData.getFailData();
 * }
 * }</pre>
 *
 * @param <T> 继承自ExcelImport的导入数据类型
 * <AUTHOR> chen
 * @since 2025/5/23
 */
@Data
public class ExcelFailData<T extends ExcelImport> {

    /**
     * 失败的导入数据列表
     * 存储所有导入失败的数据记录，每个记录包含错误信息和行号
     */
    private final List<T> failImportList = Lists.newArrayList();


    /**
     * 添加失败的导入数据
     *
     * <p>将导入失败的数据记录添加到失败列表中。如果同一行号的数据已经存在于失败列表中，
     * 则不会重复添加，避免同一行数据的重复记录。</p>
     *
     * <p>该方法会自动设置数据的失败状态和错误信息：</p>
     * <ul>
     *   <li>设置success为false</li>
     *   <li>设置errorMessage为传入的错误信息</li>
     *   <li>将数据添加到失败列表中</li>
     * </ul>
     *
     * @param t 导入失败的数据对象，必须包含有效的行号
     * @param message 失败原因的错误信息，用于描述具体的失败原因
     */
    public void putData(T t, String message) {
        T existFailData = failImportList.stream()
                .filter(i -> Objects.equals(i.getRowNum(), t.getRowNum()))
                .findAny()
                .orElse(null);
        if (existFailData == null) {
            t.setSuccess(false);
            t.setErrorMessage(message);
            failImportList.add(t);
        }
        // 注意：当前实现中，如果同一行号的数据已存在，不会更新错误信息
        // 如需支持错误信息累加，可启用下面的代码：
        // else {
        //     existFailData.setSuccess(false);
        //     existFailData.setErrorMessage(existFailData.getErrorMessage() + ";" + message);
        // }
    }

    /**
     * 检查是否存在失败的导入数据
     *
     * <p>用于判断当前是否有导入失败的数据记录。通常在导入流程中用于决定
     * 是否需要返回失败数据列表给调用方。</p>
     *
     * @return 如果存在失败数据则返回true，否则返回false
     */
    public Boolean isNotEmpty() {
        return !failImportList.isEmpty();
    }

    /**
     * 获取按行号排序的失败数据列表
     *
     * <p>返回所有导入失败的数据记录，按照Excel行号进行升序排序。
     * 这样可以确保返回的失败数据列表与Excel文件中的行顺序一致，
     * 便于用户定位和修正错误数据。</p>
     *
     * <p>返回的列表是一个新的集合，对其进行修改不会影响内部的失败数据列表。</p>
     *
     * @return 按行号排序的失败数据列表，如果没有失败数据则返回空列表
     */
    public List<T> getFailData() {
        return failImportList.stream()
                .sorted(Comparator.comparing(T::getRowNum))
                .collect(Collectors.toList());
    }


}
