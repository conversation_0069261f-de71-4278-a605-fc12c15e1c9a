package com.imile.attendance.infrastructure.repository.employee.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20 
 * @Description
 */
public interface UserEntryRecordDao extends IService<UserEntryRecordDO> {

    UserEntryRecordDO getById(Long userId);

    /**
     * 查询用户入职记录
     */
    List<UserEntryRecordDO> listByUserIds(List<Long> userIds);


    List<UserEntryRecordDO> listByPage(int currentPage, int pageSize);


}
