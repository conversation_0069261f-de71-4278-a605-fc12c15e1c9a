package com.imile.attendance.infrastructure.repository.common;

import com.imile.attendance.permission.RpcUserPermissionClient;
import com.imile.attendance.permission.dto.PermissionDTO;
import com.imile.permission.api.dto.RoleApiDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Service
public class UserPermissionService {

    @Resource
    private RpcUserPermissionClient rpcUserPermissionClient;


    public PermissionDTO getUserPermission(String userCode){
        return rpcUserPermissionClient.getUserPermission(userCode);
    }

    public List<String> getAdminUserCodesBySystem(String systemCode){
        return rpcUserPermissionClient.getAdminUserCodesBySystem(systemCode);
    }

    public Boolean addDataPermission(String typeCode, String parentId, String newId){
        return rpcUserPermissionClient.addDataPermission(typeCode, parentId, newId);
    }

    public Boolean changeDataPermission(String typeCode, String parentId, String currentId, List<String> subIdList){
        return rpcUserPermissionClient.changeDataPermission(typeCode, parentId, currentId, subIdList);
    }

    public List<RoleApiDTO> selectRoleByMenuId(Long menuId){
        return rpcUserPermissionClient.selectRoleByMenuId(menuId);
    }


    public Map<String, List<String>> selectUserCodeByRoleId(List<Long> roleIdList){
        return rpcUserPermissionClient.selectUserCodeByRoleId(roleIdList);
    }

    public Boolean addRoleForUser(String userCode, List<Long> list){
        return rpcUserPermissionClient.addRoleForUser(userCode, list);
    }

}
