package com.imile.attendance.infrastructure.repository.calendar.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.attendance.enums.AttendanceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarConfigDO
 * {@code @since:} 2025-01-17 10:33
 * {@code @description:} 
 */
@ApiModel(description="日历配置表")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("calendar_config")
public class CalendarConfigDO extends BaseDO {

    @ApiModelProperty(value="")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
    * 国家
    */
    @ApiModelProperty(value="国家")
    private String country;

    /**
    * 考勤方案编码
    */
    @ApiModelProperty(value="考勤方案编码")
    private String attendanceConfigNo;

    /**
    * 考勤配置名称
    */
    @ApiModelProperty(value="考勤配置名称")
    private String attendanceConfigName;

    /**
    * 配置类型 缺省方案、自定义方案
    */
    @ApiModelProperty(value="配置类型 缺省方案、自定义方案")
    private String type;

    /**
    * 状态
    */
    @ApiModelProperty(value="状态")
    private String status;

    /**
    * 适用部门
    */
    @ApiModelProperty(value="适用部门")
    private String deptIds;


    /**
     * 适用部门编码,逗号拼接
     */
    @ApiModelProperty(value="适用部门编码")
    private String deptCodes;

    /**
    * 是否最新
    */
    @ApiModelProperty(value="是否最新")
    private Integer isLatest;

    /**
    * 排序
    */
    @ApiModelProperty(value="排序")
    private BigDecimal orderby;


    public List<Long> listDeptIds() {
        if (StringUtils.isNotBlank(this.deptIds)) {
            return Arrays.asList((Long[]) ConvertUtils.convert(this.deptIds.split(","), Long.class));
        }
        return new ArrayList<>();
    }

    public boolean areCustom() {
        return AttendanceTypeEnum.DEFAULT.name().equals(this.type);
    }
}