package com.imile.attendance.infrastructure.repository.form.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormUserInfoDao;
import com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO;
import com.imile.attendance.infrastructure.repository.form.mapper.AttendanceApprovalFormUserInfoMapper;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormUserInfoQuery;
import com.imile.attendance.infrastructure.repository.form.query.OverTimeListQuery;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceApprovalFormUserInfoDaoImpl extends ServiceImpl<AttendanceApprovalFormUserInfoMapper, AttendanceApprovalFormUserInfoDO> implements AttendanceApprovalFormUserInfoDao {


    @Override
    public List<AttendanceApprovalFormUserInfoDO> selectByCondition(ApprovalFormUserInfoQuery query) {
        LambdaQueryWrapper<AttendanceApprovalFormUserInfoDO> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getApprovalFormId()), AttendanceApprovalFormUserInfoDO::getFormId, query.getApprovalFormId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getUserCode()), AttendanceApprovalFormUserInfoDO::getUserCode, query.getUserCode());
        queryWrapper.in(ObjectUtil.isNotEmpty(query.getUserCodes()), AttendanceApprovalFormUserInfoDO::getUserCode, query.getUserCodes());
        queryWrapper.eq(ObjectUtil.isNotNull(query.getDayId()), AttendanceApprovalFormUserInfoDO::getDayId, query.getDayId());
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getCountry()), AttendanceApprovalFormUserInfoDO::getCountry, query.getCountry());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getPostIdList()), AttendanceApprovalFormUserInfoDO::getPostId, query.getPostIdList());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getDeptIdList()), AttendanceApprovalFormUserInfoDO::getDeptId, query.getDeptIdList());
        if (StringUtils.isNotBlank(query.getUserCodeOrName())) {
            queryWrapper.and(wrapper -> wrapper.like(AttendanceApprovalFormUserInfoDO::getUserCode, query.getUserCodeOrName())
                    .or()
                    .like(AttendanceApprovalFormUserInfoDO::getUserName, query.getUserCodeOrName()));
        }
        queryWrapper.eq(AttendanceApprovalFormUserInfoDO::getIsDelete, BusinessConstant.N);

        return this.list(queryWrapper);
    }

    @Override
    public List<OverTimeApprovalListDTO> selectListByCondition(OverTimeListQuery overTimeListQuery) {
        return this.baseMapper.selectListByCondition(overTimeListQuery);
    }
}

