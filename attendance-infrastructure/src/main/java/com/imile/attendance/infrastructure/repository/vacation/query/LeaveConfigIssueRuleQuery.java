package com.imile.attendance.infrastructure.repository.vacation.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LeaveConfigIssueRuleQuery {

    /**
     * 假期规则主键
     */
    private Long leaveId;
    /**
     * 发放频次：1：周期性发放，2:一次性发放
     */
    private Integer issueFrequency;
    /**
     * 发放类型：1:固定额度 2:司领递增 3:不限定额度 4:无初始额度 5:按派遣国远近 6:随年龄增加 7:随工龄增加 8:按常驻省市配置
     */
    private Integer issueType;
    /**
     * 循环单位(每满xx年/月)
     */
    private Integer cycleUnit;
    /**
     * 循环数值(每满xx年/月)
     */
    private Integer cycleNumber;
    /**
     * 是否折扣：0:表示无该选项 ，1:表示折扣，2:表示无需折扣
     */
    private Long isConvert;

    /**
     * 是否按入职日到派遣日折扣：0:表示无该选项 ，1:表示折扣，2:表示无需折扣
     */
    private Integer isConvertDispatch;

    /**
     * 假期发放时取整规则：0:表示无该选项 ，1:表示不取整（四舍五入两位小数），2:表示向下取整（1.23-->1），3:表示向上取整（1.23-->2）:当is_convert == 1的时候，该字段才不为0，其余为0
     */
    private Integer issueRoundingRule;

    /**
     * 是否符合跨层级新规则时重新计算发放假期 0:否 1:是
     */
    private Integer isRecalculate;
}
