package com.imile.attendance.infrastructure.repository.form.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@ApiModel(description = "审批单据主表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_approval_form")
public class AttendanceApprovalFormDO extends BaseDO {

    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 申请人编码
     */
    @ApiModelProperty(value = "申请人编码")
    private String applyUserCode;

    /**
     * 申请单号
     */
    @ApiModelProperty(value = "申请单号")
    private String applicationCode;

    /**
     * 单据状态
     */
    @ApiModelProperty(value = "单据状态")
    private String formStatus;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private String formType;

    /**
     * 审批单ID
     */
    @ApiModelProperty(value = "审批单ID")
    private Long approvalId;

    /**
     * 审批节点信息
     */
    @ApiModelProperty(value = "审批节点信息")
    private String approvalProcessInfo;

    /**
     * 单据来源: 0:手动创建 1:导入
     */
    @ApiModelProperty(value = "单据来源: 0:手动创建 1:导入")
    private Integer dataSource;
}

