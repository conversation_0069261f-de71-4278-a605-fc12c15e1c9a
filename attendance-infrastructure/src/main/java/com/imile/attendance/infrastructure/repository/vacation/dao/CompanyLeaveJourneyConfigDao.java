package com.imile.attendance.infrastructure.repository.vacation.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveJourneyConfigDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21
 * @Description
 */
public interface CompanyLeaveJourneyConfigDao extends IService<CompanyLeaveJourneyConfigDO> {

    List<CompanyLeaveJourneyConfigDO> selectByIssueRuleId(List<Long> issueRuleIdList);

    List<CompanyLeaveJourneyConfigDO> getLeaveJourneyConfigList(Long issueRuleId);
}
