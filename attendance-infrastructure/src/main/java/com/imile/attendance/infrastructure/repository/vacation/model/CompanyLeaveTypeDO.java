package com.imile.attendance.infrastructure.repository.vacation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 国家假期类型配置表
 *
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@ApiModel(description = "国家假期类型配置表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("company_leave_type")
public class CompanyLeaveTypeDO extends BaseDO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 假期类型
     */
    @ApiModelProperty(value = "假期类型")
    private String leaveType;

    /**
     * 假期类型(中文)
     */
    @ApiModelProperty(value = "假期类型(中文)")
    private String leaveTypeCn;

    /**
     * 假期类型(英文)
     */
    @ApiModelProperty(value = "假期类型(英文)")
    private String leaveTypeEn;
}