package com.imile.attendance.infrastructure.filter;

import com.imile.ucenter.api.authenticate.UcenterUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
@Slf4j
@Order()
@Component
public class HttpAccessLogFilter extends OncePerRequestFilter {

    private static final String LOG_PATTERN = "URL: {} HTTP Method: {} Referer: {} Request Operator: {} Request Args: {} Response: {} Cost: {}";

    private static final int RESPONSE_BODY_LIMIT_SIZE = 512;

    private static final int REQUEST_BODY_LIMIT_SIZE = 1024 * 10;

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {
        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
        StopWatch stopWatch = StopWatch.createStarted();
        filterChain.doFilter(requestWrapper, responseWrapper);

        String requestBodyStr = new String(requestWrapper.getContentAsByteArray());
        String responseBodyStr = new String(responseWrapper.getContentAsByteArray());
        log.info(LOG_PATTERN,
                request.getRequestURL() + (StringUtils.isNotBlank(request.getQueryString()) ? "?" + request.getQueryString() : StringUtils.EMPTY),
                request.getMethod(),
                // 优先取自定义字段 为空则取浏览器默认referer
                StringUtils.isEmpty(request.getHeader("Page-Url")) ? request.getHeader("referer") : request.getHeader("Page-Url"),
                Objects.isNull(UcenterUtils.getUserInfo()) ? "" : UcenterUtils.getUserInfo().getUserCode(),
                requestBodyStr.length() > REQUEST_BODY_LIMIT_SIZE ?
                        requestBodyStr.substring(0, REQUEST_BODY_LIMIT_SIZE) + "..." :
                        requestBodyStr,
                responseBodyStr.length() > RESPONSE_BODY_LIMIT_SIZE ?
                        responseBodyStr.substring(0, RESPONSE_BODY_LIMIT_SIZE) + "..." :
                        responseBodyStr,
                stopWatch
        );
        responseWrapper.copyBodyToResponse();
    }
}
