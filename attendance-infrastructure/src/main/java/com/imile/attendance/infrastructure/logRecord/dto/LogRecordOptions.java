package com.imile.attendance.infrastructure.logRecord.dto;

import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31
 * @Description 日志记录参数
 */
@Builder
@Data
public class LogRecordOptions {

    /**
     * 操作类型 OperationTypeEnum
     */
    private String operationType;
    /**
     * 国家
     */
    private String country;
    /**
     * 业务名称
     */
    private String bizName;
    /**
     * 日志内容，为空则方法自动拼接日志内容
     */
    private String remark;
    /**
     * 指定字段名称做对比
     */
    private List<String> fieldNameList;
    /**
     * 页面操作类型
     */
    private PageOperateType pageOperateType;

    /**
     * 构建日志记录参数(根据remark构建日志记录参数)
     * 
     * @param operationType 操作类型
     * @param remark        日志内容
     * @return 日志记录参数
     */
    public static LogRecordOptions buildWithRemark(String operationType, String remark) {
        return LogRecordOptions.builder()
                .operationType(operationType)
                .remark(remark)
                .build();
    }
}
