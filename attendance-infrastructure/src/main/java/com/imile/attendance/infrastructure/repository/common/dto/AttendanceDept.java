package com.imile.attendance.infrastructure.repository.common.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.imile.ucenter.api.context.RequestInfoHolder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/2/11 
 * @Description todo 待确定真实需要字段，比如country国家(地理国)，废弃
 */
@Data
public class AttendanceDept implements Serializable {

    private Long id;

    /**
     * 企业id
     */
    private Long orgId;

    /**
     * 顶级部门ID
     */
    private Long topId;

    /**
     * 上级部门ID
     */
    private Long parentId;

    /**
     * 部门级别
     */
    private Integer level;

    /**
     * 部门路径
     */
    private String deptPath;

    /**
     * 部门名称（中文）
     */
    private String deptNameCn;

    /**
     * 部门名称（英文）
     */
    private String deptNameEn;

    /**
     * 网点简称
     */
    private String deptShortName;

    /**
     * 负责人编码
     */
    private Long leaderCode;

    /**
     * 负责人名称
     */
    private String leaderName;

    /**
     * 启用状态
     */
    private String status;

    /**
     * 描述
     */
    private String remark;

    /**
     * 排序
     */
    private BigDecimal orderby;


    /**
     * 网点id
     */
    private Long ocId;

    /**
     * 网点编码
     */
    private String ocCode;

    /**
     * 区域中心编码
     */
    private String ocCenterCode;

    /**
     * 运营类型（网点类型）
     */
    @TableField(value = "oc_type")
    private String ocType;

    /**
     * 所属结算企业id
     */
    private Long settleOrgId;

    /**
     * 加盟商企业id
     */
    private Long vendorOrgId;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门类型（暂保留，用于特殊逻辑判断，如国家级网点、加盟网点、直营网点的标识）
     */
    private String type;

    /**
     * 组织类型
     */
    private Integer deptOrgType;

    /**
     * 区域
     */
    private String region;

    /**
     * 城市
     */
    private String city;

    /**
     * 省份
     */
    private String province;

    /**
     * 国家(地理国)
     */
    private String country;

    /**
     * 业务国家
     */
    private String bizCountry;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 业务领域（最初为多选 现为单选）
     */
    private String bizArea;

    /**
     * 组织定位
     */
    private String deptPosition;

    /**
     * 组织职责
     */
    private String deptDuty;

    /**
     * 业务节点id列表
     */
    private String bizModelId;


    /**
     * 最近一次启用时间
     */
    private Date recentActiveTime;

    /**
     * 最近一次停用时间
     */
    private Date recentDisabledTime;


    public String getLocalizeName() {
        return RequestInfoHolder.isChinese() ? this.deptNameCn : this.deptNameEn;
    }
}
