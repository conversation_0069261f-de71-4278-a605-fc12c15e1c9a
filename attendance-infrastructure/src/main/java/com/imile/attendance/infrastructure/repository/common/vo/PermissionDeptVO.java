package com.imile.attendance.infrastructure.repository.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description 部门权限信息
 */
@Data
@AllArgsConstructor
public class PermissionDeptVO {

    /**
     * 是否系统管理员
     */
    private Boolean isSysAdmin;

    /**
     * 是否有部门权限
     */
    private Boolean hasDeptPermission;

    /**
     * 权限部门列表
     */
    private List<Long> deptIdList;
}
