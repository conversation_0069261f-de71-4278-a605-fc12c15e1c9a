package com.imile.attendance.infrastructure.repository.calendar.query;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description
 */
@Data
public class CalendarConfigDetailQuery implements Serializable {

    /**
     * 日历配置ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long calendarConfigId;
    /**
     * 年
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long year;
    /**
     * 月
     */
    private Long month;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 日期时间
     */
    private Long dayId;
    /**
     * 日期时间集合
     */
    private List<Long> dayIds;
}
