package com.imile.attendance.infrastructure.repository.deviceConfig.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceWifiConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.mapper.AttendanceWifiConfigMapper;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceWifiConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceConfigFilterQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceWifiConfigDaoImpl extends ServiceImpl<AttendanceWifiConfigMapper, AttendanceWifiConfigDO> implements AttendanceWifiConfigDao {


    @Override
    public List<AttendanceWifiConfigDO> list(AttendanceWifiConfigQuery query) {
        LambdaQueryWrapper<AttendanceWifiConfigDO> queryWrapper = Wrappers.lambdaQuery();
        if (CollectionUtils.isNotEmpty(query.getIds())) {
            queryWrapper.in(AttendanceWifiConfigDO::getId, query.getIds());
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(AttendanceWifiConfigDO::getCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(AttendanceWifiConfigDO::getCountry, query.getCountryList());
        }
        if (StringUtils.isNotBlank(query.getLocationCity())) {
            queryWrapper.eq(AttendanceWifiConfigDO::getLocationCity, query.getLocationCity());
        }
        if (StringUtils.isNotBlank(query.getWifiName())) {
            queryWrapper.like(AttendanceWifiConfigDO::getWifiName, query.getWifiName());
        }
        if (StringUtils.isNotBlank(query.getMacAddress())) {
            queryWrapper.eq(AttendanceWifiConfigDO::getMacAddress, query.getMacAddress());
        }
        queryWrapper.eq(AttendanceWifiConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(AttendanceWifiConfigDO::getCreateDate);

        return list(queryWrapper);
    }

    @Override
    public List<AttendanceWifiConfigDO> listByPage(int currentPage, int pageSize) {
        PageInfo<AttendanceWifiConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }

    @Override
    public List<String> selectFilterList(AttendanceConfigFilterQuery query) {
        // 查询国家
        if (WhetherEnum.NO.getKey().equals(query.getType())) {
            return this.baseMapper.queryWifiCountry();
        }
        return this.baseMapper.queryWifiCity(query.getCountry());
    }
}
