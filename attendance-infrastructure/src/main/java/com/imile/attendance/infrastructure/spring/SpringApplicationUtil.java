package com.imile.attendance.infrastructure.spring;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description Spring应用上下文工具类，
 * 用于在非Spring管理的类中获取Spring容器中的Bean实例
 */
@Component
public class SpringApplicationUtil implements ApplicationContextAware {

    private static final Logger LOGGER = LoggerFactory.getLogger(SpringApplicationUtil.class);

    /**
     * Spring应用上下文对象
     */
    private static ApplicationContext applicationContext;

    /**
     * 实现ApplicationContextAware接口的回调方法，设置上下文环境
     *
     * @param applicationContext Spring上下文对象
     * @throws BeansException Spring异常
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringApplicationUtil.applicationContext = applicationContext;
    }

    /**
     * 根据类型获取Spring容器中的Bean
     *
     * @param clazz Bean的类型
     * @return Bean实例，获取失败返回null
     */
    public static <T> T getBean(Class<T> clazz) {
        T obj;
        try {
            obj = applicationContext.getBean(clazz);
        } catch (Exception e) {
            obj = null;
            LOGGER.error("get bean error!", e);
        }
        return obj;
    }

    /**
     * 获取某个类型的所有Bean实例列表
     *
     * @param clazz Bean的类型
     * @return Bean实例列表
     */
    public static <T> List<T> getBeansOfType(Class<T> clazz) {
        return new ArrayList<>(getBeansMapOfType(clazz).values());
    }

    /**
     * 根据Bean名称获取实例
     *
     * @param beanName Bean的名称
     * @return Bean实例
     */
    public static <T> T getBean(String beanName) {
        return (T) applicationContext.getBean(beanName);
    }

    /**
     * 获取某个类型的所有Bean实例Map
     *
     * @param clazz Bean的类型
     * @return Key为Bean名称，Value为Bean实例的Map
     */
    public static <T> Map<String, T> getBeansMapOfType(Class<T> clazz) {
        Map<String, T> map;
        try {
            map = applicationContext.getBeansOfType(clazz);
        } catch (Exception e) {
            map = null;
            LOGGER.error("get bean error!", e);
        }
        return map == null ? new HashMap<>() : map;
    }

    /**
     * 获取带有指定注解的所有Bean实例Map
     *
     * @param anno 注解类型
     * @return Key为Bean名称，Value为Bean实例的Map
     */
    public static Map<String, Object> getBeansWithAnnotation(Class<? extends Annotation> anno) {
        Map<String, Object> map;
        try {
            map = applicationContext.getBeansWithAnnotation(anno);
        } catch (Exception e) {
            map = null;
        }
        return map;
    }
}
