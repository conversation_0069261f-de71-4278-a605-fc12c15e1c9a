package com.imile.attendance.infrastructure.repository.deviceConfig.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 打卡Wifi mac地址配置表
 *
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
@ApiModel(description = "打卡Wifi mac地址配置表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_wifi_config")
public class AttendanceWifiConfigDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String locationCity;

    /**
     * wifi名称
     */
    @ApiModelProperty(value = "wifi名称")
    private String wifiName;

    /**
     * mac地址
     */
    @ApiModelProperty(value = "mac地址")
    private String macAddress;
}
