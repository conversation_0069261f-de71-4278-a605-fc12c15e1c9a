package com.imile.attendance.infrastructure.repository.rule.dto;

import com.imile.attendance.util.DateHelper;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassItemConfigDTO implements Serializable {

    private Long id;

    /**
     * 班次规则ID
     */
    private Long punchClassId;

    /**
     * 序号
     */
    private Integer sortNo;

    /**
     * 上班时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：1970-01-01 18:00:00
     */
    private Date punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date earliestPunchInTime;

    /**
     * 最晚上班打卡时间 仅有时分秒信息
     */

    private Date latestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：1970-01-01 19:00:00
     */
    private Date latestPunchOutTime;

    /**
     * 是否跨天 跨天是指打卡时间是第二天：0：不跨天，1：跨天
     */
    private Integer isAcross;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 弹性时间
     */
    private BigDecimal elasticTime;

    /**
     * 法定工作时长（不包含休息时间）
     */
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    private BigDecimal attendanceHours;

    /**
     * 休息开始时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date restStartTime;

    /**
     * 休息结束时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date restEndTime;

    /**
     * 判断时间字段值是否不同
     * 时间格式为：HH:mm:ss
     */
    public boolean isTimeFieldDifferent(Date existingTime, Date newTime) {
        if (existingTime == null) {
            return newTime != null;
        }
        String existingTimeStr = DateHelper.formatHHMMSS(existingTime);
        String newTimeStr = DateHelper.formatHHMMSS(newTime);
        return !StringUtils.equalsIgnoreCase(existingTimeStr, newTimeStr);
    }

    /**
     * 判断班次时段信息是否变更
     *
     * @param newRecord 新纪录
     * @return Boolean
     */
    public Boolean judgePunchClassItemConfigUpdate(PunchClassItemConfigDTO newRecord) {
        return this.isTimeFieldDifferent(punchInTime, newRecord.getPunchInTime())
                || this.isTimeFieldDifferent(punchOutTime, newRecord.getPunchOutTime())
                || this.isTimeFieldDifferent(restStartTime, newRecord.getRestStartTime())
                || this.isTimeFieldDifferent(restEndTime, newRecord.getRestEndTime())
                || this.isTimeFieldDifferent(earliestPunchInTime, newRecord.getEarliestPunchInTime())
                || this.isTimeFieldDifferent(latestPunchInTime, newRecord.getLatestPunchInTime())
                || this.isTimeFieldDifferent(latestPunchOutTime, newRecord.getLatestPunchOutTime())
                || newRecord.getElasticTime().compareTo(elasticTime) != 0
                || newRecord.getAttendanceHours().compareTo(attendanceHours) != 0
                || newRecord.getLegalWorkingHours().compareTo(legalWorkingHours) != 0
                ;
    }
}
