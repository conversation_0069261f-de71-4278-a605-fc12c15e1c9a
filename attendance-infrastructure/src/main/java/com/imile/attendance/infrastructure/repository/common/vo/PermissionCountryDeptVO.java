package com.imile.attendance.infrastructure.repository.common.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/24 
 * @Description
 */
@Data
@Accessors(chain = true)
public class PermissionCountryDeptVO {

    /**
     * 是否系统管理员
     */
    private Boolean isSysAdmin;

    /**
     * 是否有部门权限
     */
    private Boolean hasDeptPermission;

    /**
     * 是否有国家权限
     */
    private Boolean hasCountryPermission;

    /**
     * 是否有与国家、部门权限
     */
    private Boolean hasAndDeptAndCountryPermission;

    /**
     * 是否有或国家、部门权限
     */
    private Boolean hasOrDeptAndCountryPermission;

    /**
     * 权限部门列表
     */
    private List<Long> deptIdList;

    /**
     * 权限国家列表
     */
    private List<String> countryList;

}
