package com.imile.attendance.infrastructure.repository.driver.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@ApiModel(description = "司机打卡记录表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("driver_punch_record")
public class DriverPunchRecordDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 司机账号
     */
    @ApiModelProperty(value = "司机账号")
    private String userCode;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名")
    private String userName;

    /**
     * day_id 示例：20240124
     */
    @ApiModelProperty(value = "day_id 示例：20240124")
    private Long dayId;

    /**
     * 数据来源：1：TMS 2：司机App 3：考勤系统
     */
    @ApiModelProperty(value = "数据来源：1：TMS 2：司机App 3：考勤系统")
    private Integer sourceType;

    /**
     * 操作类型：1：DLD签收 2：轨迹打卡 3：请假 4：修改考勤 ...
     */
    @ApiModelProperty(value = "操作类型：1：DLD签收 2：轨迹打卡 3：请假 4：修改考勤 ...")
    private Integer operationType;

    /**
     * 操作内容
     */
    @ApiModelProperty(value = "操作内容")
    private String operationContent;

    /**
     * 操作内容英文
     */
    @ApiModelProperty(value = "操作内容英文")
    private String operationContentEn;

    /**
     * 考勤时间
     */
    @ApiModelProperty(value = "考勤时间")
    private Date operatingTime;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤，3：L 请假 ...
     */
    @ApiModelProperty(value = "出勤类型: 1：P 出勤，2：A 缺勤，3：L 请假 ...")
    private Integer modifyAttendanceType;

    /**
     * 记录数量：轨迹打卡次数、司机签收次数
     */
    @ApiModelProperty(value = "记录数量：轨迹打卡次数、司机签收次数")
    private Long number;

    /**
     * 申请单ID
     */
    @ApiModelProperty(value = "申请单id")
    private Long formId;
}

