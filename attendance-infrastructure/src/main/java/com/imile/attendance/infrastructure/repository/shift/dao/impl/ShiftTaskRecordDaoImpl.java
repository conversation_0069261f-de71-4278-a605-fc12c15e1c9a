package com.imile.attendance.infrastructure.repository.shift.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.shift.model.ShiftTaskRecordDO;
import com.imile.attendance.infrastructure.repository.shift.dao.ShiftTaskRecordDao;
import com.imile.attendance.infrastructure.repository.shift.mapper.ShiftTaskRecordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/4/19 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class ShiftTaskRecordDaoImpl extends ServiceImpl<ShiftTaskRecordMapper, ShiftTaskRecordDO> implements ShiftTaskRecordDao {
}
