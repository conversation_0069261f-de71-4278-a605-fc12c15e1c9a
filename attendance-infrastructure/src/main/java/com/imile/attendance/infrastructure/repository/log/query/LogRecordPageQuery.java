package com.imile.attendance.infrastructure.repository.log.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.query.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description 日志操作查询类
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LogRecordPageQuery extends BaseQuery {

    /**
     * 日历操作模块 字典：attendanceLogModule
     */
    private String operationModule;

    /**
     * 日历操作的具体模块 字典：attendanceLogCode
     */
    private String operationCode;

    /**
     * 支持操作人名称或操作人id
     */
    private String operator;

    /**
     * 操作开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTimeStart;

    /**
     * 操作结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTimeEnd;

    /**
     * 操作内容
     */
    private String operationContent;
}
