package com.imile.attendance.infrastructure.repository.rule.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigExportDTO;
import com.imile.attendance.infrastructure.repository.rule.mapper.PunchClassConfigMapper;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Component
@RequiredArgsConstructor
public class PunchClassConfigDaoImpl extends ServiceImpl<PunchClassConfigMapper, PunchClassConfigDO> implements PunchClassConfigDao {

    @Override
    public List<PunchClassConfigDO> pageQuery(PunchClassConfigQuery query) {
        return this.baseMapper.pageQuery(query);
    }

    @Override
    public PunchClassConfigDO selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        LambdaQueryWrapper<PunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchClassConfigDO::getId, id);
        queryWrapper.eq(PunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public PunchClassConfigDO selectLatestAndActiveById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        LambdaQueryWrapper<PunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchClassConfigDO::getId, id);
        queryWrapper.eq(PunchClassConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<PunchClassConfigDO> selectByIds(Collection<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigDO::getId, idList);
        queryWrapper.eq(PunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<PunchClassConfigDO> selectLatestAndActiveByIds(Collection<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigDO::getId, idList);
        queryWrapper.eq(PunchClassConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<PunchClassConfigDO> selectLatestByClassName(String className, String classNature) {
        if (StringUtils.isEmpty(className) || StringUtils.isEmpty(classNature)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchClassConfigDO::getClassName, className);
        queryWrapper.eq(PunchClassConfigDO::getClassNature, classNature);
        queryWrapper.eq(PunchClassConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<PunchClassConfigDO> selectLatestAndActiveByCountry(Collection<String> countryList, String classNature) {
        if (CollectionUtils.isEmpty(countryList) || StringUtils.isEmpty(classNature)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigDO::getCountry, countryList);
        queryWrapper.eq(PunchClassConfigDO::getClassNature, classNature);
        queryWrapper.eq(PunchClassConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<PunchClassConfigDO> selectLatestCountryRange(Collection<String> countryList, String classNature) {
        if (CollectionUtils.isEmpty(countryList) || StringUtils.isEmpty(classNature)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigDO::getCountry, countryList);
        queryWrapper.eq(PunchClassConfigDO::getClassNature, classNature);
        queryWrapper.eq(PunchClassConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchClassConfigDO::getIsCountryLevel, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<PunchClassConfigDO> selectLatestAndActiveByDeptId(Long deptId, String classNature) {
        if (Objects.isNull(deptId) || StringUtils.isEmpty(classNature)) {
            return null;
        }
        LambdaQueryWrapper<PunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.apply("FIND_IN_SET('" + deptId.toString() + "', dept_ids)");
        queryWrapper.eq(PunchClassConfigDO::getClassNature, classNature);
        queryWrapper.eq(PunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(PunchClassConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<PunchClassConfigDO> selectLatestByClassNature(String classNature) {
        if (StringUtils.isEmpty(classNature)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchClassConfigDO::getClassNature, classNature);
        queryWrapper.eq(PunchClassConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchClassConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<PunchClassConfigExportDTO> export(PunchClassConfigQuery query) {
        return this.baseMapper.export(query);
    }

    @Override
    public void updateClassName(Long id, String className) {
        LambdaQueryWrapper<PunchClassConfigDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(PunchClassConfigDO::getId, id);
        PunchClassConfigDO model = new PunchClassConfigDO();
        model.setClassName(className);
        BaseDOUtil.fillDOUpdate(model);
        update(model, updateWrapper);
    }

    @Override
    public void disabledStatus(Long id) {
        LambdaQueryWrapper<PunchClassConfigDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(PunchClassConfigDO::getId, id);
        updateWrapper.eq(PunchClassConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        PunchClassConfigDO model = new PunchClassConfigDO();
        model.setStatus(StatusEnum.DISABLED.getCode());
        BaseDOUtil.fillDOUpdate(model);
        update(model, updateWrapper);
    }

    @Override
    public void enableStatus(Long id) {
        LambdaQueryWrapper<PunchClassConfigDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(PunchClassConfigDO::getId, id);
        updateWrapper.eq(PunchClassConfigDO::getStatus, StatusEnum.DISABLED.getCode());
        PunchClassConfigDO model = new PunchClassConfigDO();
        model.setStatus(StatusEnum.ACTIVE.getCode());
        BaseDOUtil.fillDOUpdate(model);
        update(model, updateWrapper);
    }

    @Override
    public List<PunchClassConfigDO> selectByCountries(Collection<String> countryList) {
        if (CollectionUtils.isEmpty(countryList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchClassConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchClassConfigDO::getCountry, countryList);
        queryWrapper.eq(PunchClassConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
