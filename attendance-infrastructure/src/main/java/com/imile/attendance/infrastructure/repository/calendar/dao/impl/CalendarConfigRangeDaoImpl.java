package com.imile.attendance.infrastructure.repository.calendar.dao.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.enums.AttendanceRangeBizType;
import com.imile.attendance.enums.RangeTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarConfigRangeIdsDTO;
import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarRangeCountDTO;
import com.imile.attendance.infrastructure.repository.calendar.mapper.CalendarConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDateQuery;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigRangeQuery;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarConfigRangeDaoImpl
 * {@code @since:} 2025-01-17 15:33
 * {@code @description:}
 */
@Slf4j
@Component
@DS(Constants.TableSchema.attendance)
@RequiredArgsConstructor
public class CalendarConfigRangeDaoImpl extends ServiceImpl<CalendarConfigRangeMapper, CalendarConfigRangeDO> implements CalendarConfigRangeDao {


    private final DefaultIdWorker defaultIdWorker;
    //todo 提升到dao层
    private final CalendarConfigRangeMapper rangeMapper;


    @Override
    public CalendarConfigRangeIdsDTO getActiveRecords(CalendarConfigRangeQuery calendarConfigRangeQuery) {
        // 查询使用范围记录
        List<CalendarConfigRangeDO> existedConfigRecords = rangeMapper.listActiveRecords(calendarConfigRangeQuery);
        // 保存与其已关联部门ID
        Collection<Long> existedDeptIds = new HashSet<>();
        // 保存与其已关联用户ID
        Collection<Long> existedUserIds = new HashSet<>();

        Collection<String> existedDeptCodes = new HashSet<>();
        Collection<String> existedUserCodes = new HashSet<>();


        for (CalendarConfigRangeDO existedConfigRecord : existedConfigRecords) {
            if (RangeTypeEnum.USER.getCode().equals(existedConfigRecord.getRangeType())) {
                // 用户ID
                existedUserIds.add(existedConfigRecord.getBizId());
                existedUserCodes.add(existedConfigRecord.getBizCode());
            } else if (RangeTypeEnum.DEPT.getCode().equals(existedConfigRecord.getRangeType())) {
                // 部门ID
                existedDeptIds.add(existedConfigRecord.getBizId());
                existedDeptCodes.add(existedConfigRecord.getBizCode());
            } else {
                log.error("非法的rangeTye：{}", existedConfigRecord.getRangeType());
            }
        }
        return CalendarConfigRangeIdsDTO.of(
                existedDeptIds,
                existedUserIds,
                existedDeptCodes,
                existedUserCodes);
    }

    @Override
    public void coverOldRecords(List<Long> bizIds) {
        coverOldRecords(bizIds, AttendanceRangeBizType.BIZ_ID);
    }

    @Override
    public void coverOldRecordsByBizCodes(List<String> bizCodes) {
        coverOldRecords(bizCodes, AttendanceRangeBizType.BIZ_CODE);
    }

    @Override
    public void updateToOld(String calendarConfigNo) {
        this.updateToOld(calendarConfigNo, BusinessConstant.N);
    }

    @Override
    public void updateToOld(String calendarConfigNo, Integer targetIsLatest) {
        // 校验日历方案配置id不能为空
        BusinessLogicException.checkTrue(StringUtils.isEmpty(calendarConfigNo),
                MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "calendarConfigNo");
        update(null, calendarConfigNo, targetIsLatest, AttendanceRangeBizType.BIZ_ID);
    }

    @Override
    public void extendParentDept(Long parentDeptId, Long deptId) {
        extendParentDeptCommon(parentDeptId, deptId, true);
    }

    @Override
    public void extendParentDeptByCode(String parentDeptCode, String deptCode) {
        extendParentDeptCommon(parentDeptCode, deptCode, false);
    }

    @Override
    public List<CalendarConfigRangeDO> selectConfigRange(List<Long> bizIds) {
        return selectConfigRange(bizIds, AttendanceRangeBizType.BIZ_ID);
    }

    @Override
    public List<CalendarConfigRangeDO> selectConfigRangeByBizCodes(List<String> bizCodes) {
        return selectConfigRange(bizCodes, AttendanceRangeBizType.BIZ_CODE);
    }

    @Override
    public List<CalendarConfigRangeDO> selectRangeByCalendarConfigNo(String calendarConfigNo) {
        if (StringUtils.isBlank(calendarConfigNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CalendarConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CalendarConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(CalendarConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .eq(CalendarConfigRangeDO::getAttendanceConfigNo, calendarConfigNo);
        return this.list(queryWrapper);
    }

    @Override
    public List<CalendarConfigRangeDO> selectCalendarConfigByDate(CalendarConfigDateQuery query) {
        return rangeMapper.selectConfigRangeByDate(query);
    }

    @Override
    public List<CalendarConfigRangeDO> selectCalendarConfigByIds(List<Long> calendarConfigIds) {
        if (CollectionUtils.isEmpty(calendarConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CalendarConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CalendarConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(CalendarConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .in(CalendarConfigRangeDO::getAttendanceConfigId, calendarConfigIds);
        return this.list(queryWrapper);
    }



    /**
     * 覆盖旧记录（支持 bizIds 或 bizCodes）
     *
     * @param bizIdsOrCodes    可以是 List<Long> 或 List<String>
     * @param rangeBizType     日历范围业务类型（bizId 或 bizCode）
     */
    private void coverOldRecords(List<?> bizIdsOrCodes, AttendanceRangeBizType rangeBizType) {
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(bizIdsOrCodes),
                MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, rangeBizType.getValue());
        update(bizIdsOrCodes, null, BusinessConstant.N, rangeBizType);
    }

    /**
     * 查询员工配置的考勤日历（支持 bizIds 或 bizCodes）
     *
     * @param bizIdsOrCodes 可以是 List<Long> 或 List<String>
     * @param rangeBizType     日历范围业务类型（bizId 或 bizCode）
     * @return 查询结果
     */
    private List<CalendarConfigRangeDO> selectConfigRange(List<?> bizIdsOrCodes,
                                                          AttendanceRangeBizType rangeBizType) {
        if (CollectionUtils.isEmpty(bizIdsOrCodes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CalendarConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        switch (rangeBizType){
            case BIZ_ID:
                queryWrapper.in(CollectionUtils.isNotEmpty(bizIdsOrCodes), CalendarConfigRangeDO::getBizId, bizIdsOrCodes);
                break;
            case BIZ_CODE:
                queryWrapper.in(CollectionUtils.isNotEmpty(bizIdsOrCodes), CalendarConfigRangeDO::getBizCode, bizIdsOrCodes);
                break;
            default:
                break;
        }
        queryWrapper.eq(CalendarConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .eq(CalendarConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }


    /**
     * 更新逻辑（支持 bizIds 或 bizCodes）
     *
     * @param idsOrCodes       可以是 List<Long> 或 List<String>
     * @param calendarConfigNo 日历配置编号
     * @param targetIsLatest   是否最新
     * @param rangeBizType     日历范围业务类型（bizId 或 bizCode）
     */
    private void update(List<?> idsOrCodes,
                        String calendarConfigNo,
                        Integer targetIsLatest,
                        AttendanceRangeBizType rangeBizType) {
        if (CollectionUtils.isEmpty(idsOrCodes) && StringUtils.isEmpty(calendarConfigNo)) {
            return;
        }

        LambdaUpdateWrapper<CalendarConfigRangeDO> updateWrapper = Wrappers.lambdaUpdate();
        switch (rangeBizType) {
            case BIZ_ID:
                updateWrapper.in(CollectionUtils.isNotEmpty(idsOrCodes), CalendarConfigRangeDO::getBizId, idsOrCodes);
                break;
            case BIZ_CODE:
                updateWrapper.in(CollectionUtils.isNotEmpty(idsOrCodes), CalendarConfigRangeDO::getBizCode, idsOrCodes);
                break;
            default:
                break;
        }
        updateWrapper.eq(StringUtils.isNotEmpty(calendarConfigNo), CalendarConfigRangeDO::getAttendanceConfigNo, calendarConfigNo);
        updateWrapper.eq(CalendarConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .eq(CalendarConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());

        CalendarConfigRangeDO updateRangeDO = new CalendarConfigRangeDO();
        if (targetIsLatest != null) {
            updateRangeDO.setIsLatest(BusinessConstant.N);
        }
        updateRangeDO.setEndDate(new Date());
        BaseDOUtil.fillDOUpdate(updateRangeDO);

        update(updateRangeDO, updateWrapper);
    }


    /**
     * 继承父部门的考勤配置（支持 bizId 或 bizCode）
     *
     * @param parentBizIdOrCode 父部门 ID 或编码
     * @param deptBizIdOrCode   子部门 ID 或编码
     * @param isId              是否使用 ID（true 表示使用 ID，false 表示使用编码）
     */
    private void extendParentDeptCommon(Object parentBizIdOrCode, Object deptBizIdOrCode, boolean isId) {
        if (parentBizIdOrCode == null || deptBizIdOrCode == null) {
            log.warn("父部门或子部门标识为空");
            return;
        }

        // 查询当前子部门是否已关联非默认考勤方案
        CalendarConfigRangeQuery query = CalendarConfigRangeQuery.builder()
                .rangeType(RangeTypeEnum.DEPT.getCode())
                .calendarConfigStatus(StatusEnum.ACTIVE.getCode())
                .build();
        if (isId) {
            query.setBizId((Long) deptBizIdOrCode);
        } else {
            query.setBizCode((String) deptBizIdOrCode);
        }
        List<CalendarConfigRangeDO> existedConfigRecords = rangeMapper.listActiveRecords(query);
        if (CollectionUtils.isNotEmpty(existedConfigRecords)) {
            log.info("当前子部门已关联非默认考勤方案，不会继承父部门的考勤配置");
            return;
        }

        // 查询父部门适用范围记录
        query = CalendarConfigRangeQuery.builder()
                .rangeType(RangeTypeEnum.DEPT.getCode())
                .build();
        if (isId) {
            query.setBizId((Long) parentBizIdOrCode);
        } else {
            query.setBizCode((String) parentBizIdOrCode);
        }
        List<CalendarConfigRangeDO> parentExistedConfigRecords = rangeMapper.listActiveRecords(query);
        if (CollectionUtils.isEmpty(parentExistedConfigRecords)) {
            log.warn("父部门未关联其他非默认考勤方案");
            return;
        }

        // 实际应该只有一条，取第一条
        CalendarConfigRangeDO model = BeanUtil.copyProperties(parentExistedConfigRecords.get(0), CalendarConfigRangeDO.class);
        model.setId(defaultIdWorker.nextId());
        if (isId) {
            model.setBizId((Long) deptBizIdOrCode);
        } else {
            model.setBizCode((String) deptBizIdOrCode);
        }
        BaseDOUtil.fillDOInsert(model);
        save(model);
    }

    @Override
    public List<CalendarConfigRangeDO> listAllRecords(CalendarConfigRangeQuery query) {
        return rangeMapper.listAllRecords(query);
    }

    @Override
    public List<CalendarConfigRangeDO> listByPage(int currentPage, int pageSize) {
        PageInfo<CalendarConfigRangeDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }

    @Override
    public  List<CalendarRangeCountDTO> countCalendarRange(List<Long> calendarConfigIds) {
        return this.baseMapper.countCalendarRange(calendarConfigIds);
    }

    @Override
    public void deleteByBizId(Long bizId) {
        CalendarConfigRangeDO model = new CalendarConfigRangeDO();
        BaseDOUtil.fillDOUpdate(model);
        model.setIsDelete(IsDeleteEnum.YES.getCode());
        LambdaQueryWrapper<CalendarConfigRangeDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(CalendarConfigRangeDO::getBizId, bizId);
        updateWrapper.eq(CalendarConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        this.baseMapper.update(model, updateWrapper);
    }

    @Override
    public List<UserInfoDO> listOnJobNoDriverUsersExcludeRangeConfigured(RuleRangeUserQuery ruleRangeUserQuery) {
        return this.baseMapper.listOnJobNoDriverUsersExcludeRangeConfigured(ruleRangeUserQuery);
    }

    @Override
    public List<CalendarConfigRangeDO> selectAllByBizId(Long bizId) {
        if (Objects.isNull(bizId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CalendarConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CalendarConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(CalendarConfigRangeDO::getBizId, bizId)
                .orderByDesc(CalendarConfigRangeDO::getCreateDate);
        return this.list(queryWrapper);
    }
}
