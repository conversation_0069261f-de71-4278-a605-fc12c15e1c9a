package com.imile.attendance.infrastructure.repository.employee.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户假期操作记录表
 *
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_leave_record")
public class UserLeaveRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 假期配置id
     */
    @ApiModelProperty(value = "国家假期配置id")
    private Long configId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户编码
     */
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private Date date;
    @ApiModelProperty(value = "日期（dayId）")
    private Long dayId;

    /**
     * 假期名称
     */
    @ApiModelProperty(value = "假期名称")
    private String leaveName;

    /**
     * 假期类型
     */
    @ApiModelProperty(value = "假期类型")
    private String leaveType;

    /**
     * 类型(请假、销假)
     */
    @ApiModelProperty(value = "类型(请假、销假)")
    private String type;

    /**
     * 请假开始日期
     */
    @ApiModelProperty(value = "请假开始日期")
    private Date leaveStartDay;

    /**
     * 请假结束日期
     */
    @ApiModelProperty(value = "请假结束日期")
    private Date leaveEndDay;

    /**
     * 请假分钟
     */
    @ApiModelProperty(value = "请假分钟")
    private BigDecimal leaveMinutes;

    /**
     * 请假时长
     */
    @ApiModelProperty(value = "请假时长")
    private BigDecimal hours;

    /**
     * 上传图片路径
     */
    @ApiModelProperty(value = "上传图片路径")
    private String picturePath;

    /**
     * 假期剩余天数
     */
    @ApiModelProperty(value = "假期剩余天数")
    private BigDecimal leaveResidueDay;

    /**
     * 操作人编码
     */
    @ApiModelProperty(value = "操作人编码")
    private String operationUserCode;

    /**
     * 操作人名称
     */
    @ApiModelProperty(value = "操作人名称")
    private String operationUserName;

    /**
     * 假期减少天数
     */
    @ApiModelProperty(value = "假期减少天数")
    private BigDecimal reduceDay;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
