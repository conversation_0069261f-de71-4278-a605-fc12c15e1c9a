package com.imile.attendance.infrastructure.logRecord.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.logRecord.dto.FieldDiffDTO;
import com.imile.attendance.infrastructure.logRecord.dto.LogOperateBO;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.dto.OperatorLogDTO;
import com.imile.attendance.infrastructure.logRecord.enums.FieldNameEnum;
import com.imile.attendance.infrastructure.logRecord.LogContentService;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.enums.OperationCodeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.OperationModuleEnum;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.log.dao.LogOperationRecordDao;
import com.imile.attendance.infrastructure.repository.log.model.LogOperationRecordDO;
import com.imile.attendance.infrastructure.sync.ReflectGetIdUtil;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.ReflectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 日志记录服务实现类
 * 负责处理系统操作日志的记录，包括对象变更、列表变更和普通操作的日志记录
 *
 * <AUTHOR> chen
 * @Date 2025/3/31
 */
@Slf4j
@Component
public class LogRecordServiceImpl implements LogRecordService {

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private LogOperationRecordDao logOperationRecordDao;
    @Resource
    private Executor logTaskThreadPool;
    @Resource
    private LogContentService logContentService;

    /**
     * 忽略的字段
     */
    private static final List<String> IGNORED_FIELDS = Arrays.asList(
            "serialVersionUID", "createDate", "createUserCode", "createUserName", "lastUpdDate", "lastUpdUserCode",
            "lastUpdUserName", "isDelete", "recordVersion");

    @Override
    public <T> void recordObjectChange(T newObject, T oldObject, LogRecordOptions logRecordOptions) {
        if (isInvalidForLogging(newObject, logRecordOptions)) {
            return;
        }
        diffObj(newObject, oldObject,
                logRecordOptions.getOperationType(),
                logRecordOptions.getCountry(),
                logRecordOptions.getBizName(),
                logRecordOptions.getRemark(),
                logRecordOptions.getFieldNameList());
    }

    @Override
    public <T> void recordListChange(List<T> newList, List<T> oldList, LogRecordOptions logRecordOptions) {
        diffListWithField(newList, oldList, logRecordOptions.getOperationType(),
                FieldNameEnum.ID.getCode(), logRecordOptions.getCountry(), logRecordOptions.getBizName());
    }

    /**
     * 记录操作日志
     *
     * @param obj              需要记录日志的对象
     * @param logRecordOptions 日志记录选项
     * @param <T>              对象类型
     */
    @Override
    public <T> void recordOperation(T obj, LogRecordOptions logRecordOptions) {
        if (isInvalidForLogging(obj, logRecordOptions)) {
            return;
        }
        LogOperateBO logOperateBO = LogOperateBO.buildByOperateType(logRecordOptions.getOperationType());
        if (null == logOperateBO) {
            return;
        }
        OperatorLogDTO operatorLogDTO = getOperatorLogDTO(logOperateBO);
        // 异步处理
        logTaskThreadPool.execute(() -> processRecordOperation(obj, logRecordOptions, logOperateBO, operatorLogDTO));
    }

    /**
     * 处理记录操作日志的具体逻辑
     *
     * @param obj              需要记录日志的对象
     * @param logRecordOptions 日志记录选项，包含操作类型、备注等信息
     * @param logOperateBO     日志操作业务对象，包含操作模块、操作码等信息
     * @param operatorLogDTO   操作者日志DTO，包含操作人信息
     * @param <T>              操作对象的类型
     */
    private <T> void processRecordOperation(T obj,
            LogRecordOptions logRecordOptions,
            LogOperateBO logOperateBO,
            OperatorLogDTO operatorLogDTO) {
        try {
            LogOperationRecordDO operationRecordDO = BeanUtil.copyProperties(operatorLogDTO,
                    LogOperationRecordDO.class);
            operationRecordDO.setOperationTime(new Date());
            Class<?> objClass = obj.getClass();
            // 判断类是不是由TableName修饰
            if (!objClass.isAnnotationPresent(TableName.class)) {
                return;
            }
            // 从该类获取表
            TableName annotation = objClass.getAnnotation(TableName.class);
            operationRecordDO.setForeignTable(Optional.ofNullable(annotation.value()).orElse(null));

            operationRecordDO.setForeignKey(String.valueOf(ReflectGetIdUtil.extractId(obj)));
            operationRecordDO.setId(defaultIdWorker.nextId());
            if (StringUtils.isNotBlank(logRecordOptions.getRemark())) {
                operationRecordDO.setRemark(logRecordOptions.getRemark());
            } else {
                String logContent = logContentService.logContent(
                        logRecordOptions.getPageOperateType(),
                        logOperateBO.getOperationCodeEnum(),
                        logRecordOptions.getCountry(),
                        logRecordOptions.getBizName());
                operationRecordDO.setRemark(logContent);
            }
            // 基础属性生成
            BaseDOUtil.fillDOInsert(operationRecordDO);
            // 落库
            logOperationRecordDao.save(operationRecordDO);
        } catch (Exception e) {
            // 记录异常
            log.error("recordOperation error, obj:{}", JSONObject.toJSONString(obj), e);
        }
    }

    /**
     * 处理列表差异比对并记录日志
     *
     * @param newList       新列表数据
     * @param oldList       旧列表数据
     * @param operationType 操作类型
     * @param fieldName     用于比对的字段名
     * @param country       国家
     * @param bizName       业务名称
     * @param <T>           列表元素类型
     */
    private <T> void diffListWithField(List<T> newList, List<T> oldList, String operationType,
            String fieldName, String country, String bizName) {
        OperatorLogDTO operatorLogDTO = checkOperatorLogDTO(operationType);
        if (operatorLogDTO == null) {
            return;
        }
        logTaskThreadPool.execute(() -> {
            if (CollectionUtils.isEmpty(newList)) {
                log.error("newList 不能为空");
                return;
            }
            doDiffObj(newList, oldList, operatorLogDTO, newList.get(0).getClass().getName(),
                    fieldName, country, bizName, null);
        });
    }

    /**
     * 比对对象差异并记录日志
     *
     * @param newObject     新对象
     * @param oldObject     旧对象
     * @param operationType 操作类型
     * @param country       国家
     * @param bizName       业务名称
     * @param remark        备注
     * @param fieldNameList 需要比对的字段名
     * @param <T>           对象类型
     */
    public <T> void diffObj(T newObject, T oldObject,
                            String operationType,
                            String country,
                            String bizName,
                            String remark,
                            List<String> fieldNameList) {
        OperatorLogDTO operatorLogDTO = checkOperatorLogDTO(operationType);
        if (operatorLogDTO == null) {
            return;
        }
        logTaskThreadPool.execute(() -> {
            if (newObject == null) {
                log.error("newObject 不能为null");
                return;
            }
            diffSingleObject(
                    newObject,
                    oldObject,
                    operatorLogDTO,
                    bizName,
                    country,
                    remark,
                    fieldNameList);
        });
    }

    /**
     * 检查并获取操作日志DTO
     *
     * @param operationType 操作类型
     * @return 操作日志DTO，如果操作类型无效则返回null
     */
    private OperatorLogDTO checkOperatorLogDTO(String operationType) {
        LogOperateBO logOperateBO = LogOperateBO.buildByOperateType(operationType);
        if (logOperateBO == null) {
            log.error("入参错误：{}", operationType);
            return null;
        }
        OperatorLogDTO operatorLogDTO = getOperatorLogDTO(operationType);
        if (operatorLogDTO == null) {
            log.error("入参错误：{}", operationType);
            return null;
        }
        return operatorLogDTO;
    }

    /**
     * 根据操作类型获取操作日志DTO
     *
     * @param operationType 操作类型
     * @return 操作日志DTO，如果操作类型无效则返回null
     */
    private OperatorLogDTO getOperatorLogDTO(String operationType) {
        OperatorLogDTO operatorLogDTO = new OperatorLogDTO();
        OperationTypeEnum operationTypeEnum = OperationTypeEnum.getOperationType(operationType);
        if (operationTypeEnum == null) {
            log.error("不存在 operationType：{}", operationType);
            return null;
        }
        OperationCodeEnum operationCodeEnum = operationTypeEnum.getOperationCodeEnum();
        if (operationCodeEnum == null) {
            return null;
        }
        OperationModuleEnum operationModuleEnum = operationCodeEnum.getOperationModuleEnum();
        if (operationModuleEnum == null) {
            return null;
        }
        operatorLogDTO.setOperationModule(operationModuleEnum.getCode());
        operatorLogDTO.setOperationCode(operationCodeEnum.getCode());
        operatorLogDTO.setOperationCodeDesc(operationCodeEnum.getDesc());
        operatorLogDTO.setOperationType(operationType);
        operatorLogDTO.setOperationUserCode(RequestInfoHolder.getUserCode());
        operatorLogDTO.setOperationUserName(RequestInfoHolder.getUserName());
        return operatorLogDTO;
    }

    /**
     * 获取操作日志DTO
     *
     * @param logOperateBO 日志操作业务对象
     * @return 操作日志DTO
     */
    private OperatorLogDTO getOperatorLogDTO(LogOperateBO logOperateBO) {
        OperatorLogDTO operatorLogDTO = new OperatorLogDTO();
        operatorLogDTO.setOperationModule(logOperateBO.getOperationModuleEnum().getCode());
        operatorLogDTO.setOperationCode(logOperateBO.getOperationCodeEnum().getCode());
        operatorLogDTO.setOperationCodeDesc(logOperateBO.getOperationCodeEnum().getDesc());
        operatorLogDTO.setOperationType(logOperateBO.getOperationTypeEnum().getCode());
        operatorLogDTO.setOperationUserCode(RequestInfoHolder.getUserCode());
        operatorLogDTO.setOperationUserName(RequestInfoHolder.getUserName());
        return operatorLogDTO;
    }

    /**
     * 单对象差异比较方法
     *
     * @param newObject      新对象
     * @param oldObject      旧对象
     * @param operatorLogDTO 操作日志DTO
     * @param bizName        业务名称
     * @param country        国家/地区
     * @param remark         备注说明(可选)
     * @param fieldNameList  需要比对的字段名
     * @param <T>            对象类型
     */
    private <T> void diffSingleObject(T newObject, T oldObject,
            OperatorLogDTO operatorLogDTO,
            String bizName,
            String country,
            String remark,
            List<String> fieldNameList) {
        try {
            if (newObject == null) {
                log.warn("新对象为空，无法进行对比");
                return;
            }

            Class<?> objectClass = newObject.getClass();
            String className = objectClass.getName();

            // 如果旧对象为空，则创建同类型的空实例
            if (oldObject == null) {
                oldObject = ReflectionUtils.newInstance(className);
            } else if (!oldObject.getClass().getName().equals(className)) {
                log.warn("新旧对象类型不一致，无法进行对比");
                return;
            }

            // 判断类是不是由TableName修饰
            if (!objectClass.isAnnotationPresent(TableName.class)) {
                log.warn("对象类不包含TableName注解，无法确定表名");
                return;
            }

            // 从该类获取表
            TableName annotation = objectClass.getAnnotation(TableName.class);

            // 落库类
            LogOperationRecordDO operationRecordDO = BeanUtil.copyProperties(operatorLogDTO,
                    LogOperationRecordDO.class);
            operationRecordDO.setOperationTime(new Date());
            operationRecordDO.setForeignTable(Optional.ofNullable(annotation.value()).orElse(null));

            String bizId = null;
            Long operatedUserId = null;

            // 获取所有字段
            List<Field> declaredFields = ReflectionUtils.getDeclaredFields(objectClass);
            List<FieldDiffDTO> fieldDiffDTOS = new ArrayList<>();
            FieldDiffDTO fieldDiffDTO = new FieldDiffDTO();
            Map<String, Object> oldData = new TreeMap<>();
            Map<String, Object> newData = new TreeMap<>();

            for (Field field : declaredFields) {
                if (CollectionUtils.isNotEmpty(fieldNameList)) {
                    // 如果传入了需要比对的字段名，则只比对传入的字段名
                    if (!fieldNameList.contains(field.getName())) {
                        continue;
                    }
                }
                // 序列化字段跳过
                if (IGNORED_FIELDS.contains(field.getName())) {
                    continue;
                }
                field.setAccessible(Boolean.TRUE);

                // 获取新旧属性的值
                Object newValue = field.get(newObject);
                Object oldValue = field.get(oldObject);

                // 获取表id和被操作用户ID
                if (FieldNameEnum.ID.getCode().equals(field.getName())) {
                    Object key = Optional.ofNullable(newValue).orElse(null);
                    bizId = StringUtils.isEmpty(bizId) ? String.valueOf(key) : bizId;
                }

                if (FieldNameEnum.USER_ID.getCode().equals(field.getName())) {
                    Long userId = field.get(newObject) != null ? (Long) field.get(newObject)
                            : (Long) field.get(oldObject);
                    bizId = String.valueOf(userId);
                    operatedUserId = userId;
                }

                // 如果字段的类型为时间类型，统一转成yyyy-MM-dd HH:mm:ss
                if (field.getType().equals(Date.class) ||
                        field.getType().equals(LocalDateTime.class)) {
                    oldValue = oldValue != null ? DateHelper.formatYYYYMMDDHHMMSS((Date) oldValue) : null;
                    newValue = newValue != null ? DateHelper.formatYYYYMMDDHHMMSS((Date) newValue) : null;
                }

                //如果字段的类型为BigDecimal，则统一转成两位小数在对比
                if (field.getType().equals(BigDecimal.class)) {
                    oldValue = oldValue != null ? ((BigDecimal) oldValue).setScale(2, RoundingMode.HALF_UP) : null;
                    newValue = newValue != null ? ((BigDecimal) newValue).setScale(2, RoundingMode.HALF_UP) : null;
                }

                // 记录旧值
                oldData.put(field.getName(), oldValue);

                // 将新值中有做过修改的值添加进newData中
                if ((oldValue == null && newValue != null) ||
                        (oldValue != null && !oldValue.equals(newValue))) {
                    newData.put(field.getName(), newValue);
                }
            }

            fieldDiffDTO.setOldData(new JSONObject(oldData));
            fieldDiffDTO.setNewData(new JSONObject(newData));
            fieldDiffDTO.setObjClassName(objectClass.getName());
            fieldDiffDTOS.add(fieldDiffDTO);

            // 业务id，有userId时设置为userId，没有时取主键
            operationRecordDO.setForeignKey(bizId);
            operationRecordDO.setId(defaultIdWorker.nextId());
            operationRecordDO.setFieldDiff(JSONObject.toJSONString(fieldDiffDTOS));

            // 设置备注
            if (StringUtils.isNotBlank(remark)) {
                operationRecordDO.setRemark(remark);
            } else {
                String logContent = logContentService.pageUpdateLogContent(
                        operatorLogDTO.getOperationCodeDesc(),
                        country,
                        bizName,
                        operationRecordDO.getFieldDiff());
                operationRecordDO.setRemark(logContent);
            }

            // 设置用户信息
            if (operatedUserId != null) {
                operationRecordDO.setUserId(operatedUserId);
                // 获取名称
                UserInfoDO userInfoDO = userInfoDao.getById(operatedUserId);
                operationRecordDO
                        .setUserName(Optional.ofNullable(userInfoDO).map(UserInfoDO::getUserName).orElse(null));
                operationRecordDO.setDeptId(Optional.ofNullable(userInfoDO).map(UserInfoDO::getDeptId).orElse(null));
            }

            // 基础属性生成
            BaseDOUtil.fillDOInsert(operationRecordDO);
            // 落库
            logOperationRecordDao.save(operationRecordDO);

        } catch (Exception e) {
            // 该处的异常必须捕获，并且不需要抛出，操作日志的记录异常不应该影响业务的正常进行
            log.error("单对象差异比较记录错误", e);
        }
    }

    private <T> void doDiffObj(List<T> newList, List<T> oldList,
            OperatorLogDTO operatorLogDTO,
            String className, String fieldName,
            String country,
            String bizName,
            String remark) {

        try {
            // 若oldList不为空，则需要判断两者类是否相同
            if (!CollectionUtils.isEmpty(oldList) &&
                    !oldList.get(0).getClass().getName().equals(className)) {
                return;
            }
            fieldName = StringUtils.isEmpty(fieldName) ? FieldNameEnum.ID.getCode() : fieldName;
            // oldList转map
            Map<Object, T> oldMap = oldList.stream()
                    .collect(Collectors.toMap(getKey(fieldName), item -> item, (oldValue, newValue) -> oldValue));
            // 落库类
            LogOperationRecordDO operationRecordDO = BeanUtil.copyProperties(operatorLogDTO,
                    LogOperationRecordDO.class);
            operationRecordDO.setOperationTime(new Date());
            String bizId = null;
            List<FieldDiffDTO> fieldDiffDTOS = new ArrayList<>();
            Long operatedUserId = null;
            for (T newObject : newList) {
                Class<?> newObjectClass = newObject.getClass();
                // 判断类是不是由TableName修饰
                if (!newObjectClass.isAnnotationPresent(TableName.class)) {
                    return;
                }
                // 从该类获取表
                TableName annotation = newObjectClass.getAnnotation(TableName.class);
                operationRecordDO.setForeignTable(Optional.ofNullable(annotation.value()).orElse(null));

                Object oldObject = Optional.ofNullable(getOldValue(newObjectClass, newObject, oldMap, fieldName))
                        .orElse(ReflectionUtils.newInstance(className));
                // 获取所有字段
                List<Field> declaredFields = ReflectionUtils.getDeclaredFields(newObjectClass);
                FieldDiffDTO fieldDiffDTO = new FieldDiffDTO();
                JSONObject oldData = new JSONObject();
                JSONObject newData = new JSONObject();
                for (Field field : declaredFields) {
                    field.setAccessible(Boolean.TRUE);
                    // 序列化字段跳过
                    if (IGNORED_FIELDS.contains(field.getName())) {
                        continue;
                    }
                    // 拿到新属性的值
                    Object newValue = field.get(newObject);
                    // 获取表id
                    if (FieldNameEnum.ID.getCode().equals(field.getName())) {
                        Object key = Optional.ofNullable(newValue).orElse(null);
                        // 业务id，有userId时设置为userId，没有时取主键
                        bizId = StringUtils.isEmpty(bizId) ? String.valueOf(key) : bizId;
                    }
                    // 获取当前被操作的userId
                    if (FieldNameEnum.USER_ID.getCode().equals(field.getName())) {
                        // 设置本地变量
                        Long userId = field.get(newObject) != null ? (Long) field.get(newObject)
                                : (Long) field.get(oldObject);
                        bizId = String.valueOf(userId);
                        operatedUserId = userId;
                    }

                    // 直接将旧值添加进oldData
                    Object oldValue = field.get(oldObject);

                    oldData.put(field.getName(), oldValue);

                    // 将新值中有做过修改的值添加进newData中
                    if ((oldValue == null && newValue != null) ||
                            (oldValue != null && !oldValue.equals(newValue))) {
                        newData.put(field.getName(), newValue);
                    }
                }
                fieldDiffDTO.setOldData(oldData);
                fieldDiffDTO.setNewData(newData);
                fieldDiffDTO.setObjClassName(newObjectClass.getName());
                fieldDiffDTOS.add(fieldDiffDTO);
            }
            // 业务id，有userId时设置为userId，没有时取主键
            operationRecordDO.setForeignKey(bizId);
            operationRecordDO.setId(defaultIdWorker.nextId());
            operationRecordDO.setFieldDiff(JSONObject.toJSONString(fieldDiffDTOS));
            if (StringUtils.isNotBlank(remark)) {
                operationRecordDO.setRemark(remark);
            } else {
                String logContent = logContentService.pageUpdateLogContent(
                        operatorLogDTO.getOperationCodeDesc(),
                        country,
                        bizName,
                        operationRecordDO.getFieldDiff());
                operationRecordDO.setRemark(logContent);
            }
            if (operatedUserId != null) {
                operationRecordDO.setUserId(operatedUserId);
                // 获取名称
                UserInfoDO userInfoDO = userInfoDao.getById(operatedUserId);
                operationRecordDO
                        .setUserName(Optional.ofNullable(userInfoDO).map(UserInfoDO::getUserName).orElse(null));
                operationRecordDO.setDeptId(Optional.ofNullable(userInfoDO).map(UserInfoDO::getDeptId).orElse(null));
            }
            // 基础属性生成
            BaseDOUtil.fillDOInsert(operationRecordDO);
            // 落库
            logOperationRecordDao.save(operationRecordDO);
        } catch (Exception e) {
            // 该处的异常必须捕获，并且不需要抛出，操作日志的记录异常不应该影响业务的正常进行
            log.error("操作日志记录错误doDiffObj", e);
        }

    }

    /**
     * 获取新do对应的老do
     */
    private <T> T getOldValue(Class<?> newObjectClass, T newObject, Map<Object, T> oldMap, String fieldName)
            throws Exception {
        Field idField = newObjectClass.getDeclaredField(fieldName);
        idField.setAccessible(true);
        Object key = idField.get(newObject);
        return oldMap.get(key);
    }

    /**
     * 校验对象是否不满足日志记录的条件
     *
     * @param obj              需要记录日志的对象
     * @param logRecordOptions 日志记录选项
     * @param <T>              对象泛型
     * @return 如果满足以下任一条件则返回true:
     *         1. 对象为空
     *         2. 对象类上没有@TableName注解
     *         3. 操作类型为空
     */
    private <T> boolean isInvalidForLogging(T obj, LogRecordOptions logRecordOptions) {
        boolean isInvalid = obj == null
                || !obj.getClass().isAnnotationPresent(TableName.class)
                || StringUtils.isBlank(logRecordOptions.getOperationType());
        if (isInvalid) {
            log.warn("Invalid logging parameters: obj={}, options={}",
                    obj, logRecordOptions);
        }
        return isInvalid;
    }

    /**
     * 获取对象中指定字段的值
     *
     * @param fieldName 要获取的字段名称
     * @param <T>       对象类型
     * @return 返回一个函数，该函数接收一个对象并返回其指定字段的值
     */
    private <T> Function<T, Object> getKey(String fieldName) {
        return bean -> {
            if (bean == null || StringUtils.isEmpty(fieldName)) {
                log.warn("Invalid parameters: bean={}, fieldName={}", bean, fieldName);
                return null;
            }

            try {
                Class<?> beanClass = bean.getClass();
                // 尝试直接获取字段（包括父类字段）
                Field field = ReflectionUtils.findField(beanClass, fieldName);
                if (field == null) {
                    log.warn("Field {} not found in class {}", fieldName, beanClass.getName());
                    return null;
                }

                ReflectionUtils.makeAccessible(field);
                return field.get(bean);
            } catch (IllegalAccessException e) {
                log.error("Failed to access field {} in class {}", fieldName, bean.getClass().getName(), e);
                return null;
            } catch (Exception e) {
                log.error("Unexpected error while getting field value", e);
                return null;
            }
        };
    }
}
