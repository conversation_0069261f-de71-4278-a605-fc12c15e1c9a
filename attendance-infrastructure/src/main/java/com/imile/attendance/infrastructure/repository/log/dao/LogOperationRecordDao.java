package com.imile.attendance.infrastructure.repository.log.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.log.model.LogOperationRecordDO;
import com.imile.attendance.infrastructure.repository.log.query.LogRecordPageQuery;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
public interface LogOperationRecordDao extends IService<LogOperationRecordDO> {

    List<LogOperationRecordDO> listPage(LogRecordPageQuery logRecordPageQuery);
}

