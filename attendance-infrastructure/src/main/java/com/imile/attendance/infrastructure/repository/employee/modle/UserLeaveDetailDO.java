package com.imile.attendance.infrastructure.repository.employee.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 用户假期详情表
 *
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_leave_detail")
public class UserLeaveDetailDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 假期配置id
     */
    @ApiModelProperty(value = "国家假期配置id")
    private Long configId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户编码
     */
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    /**
     * 假期名称
     */
    @ApiModelProperty(value = "假期名称")
    private String leaveName;

    /**
     * 假期类型
     */
    @ApiModelProperty(value = "假期类型")
    private String leaveType;


    /**
     * 状态 ACTIVE、DISABLED
     */
    @ApiModelProperty(value = "状态")
    private String status;


}
