package com.imile.attendance.infrastructure.repository.rule.dao;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.OverTimeConfigPageQuery;
import com.imile.attendance.infrastructure.repository.rule.query.OverTimeConfigQuery;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
public interface OverTimeConfigDao extends IService<OverTimeConfigDO> {

    /**
     * 根据名称查询加班配置
     * 
     * @param name 名称
     * @return 加班配置
     */
    OverTimeConfigDO getByName(String name);

    /**
     * 根据配置编码查询最新且启用的加班配置
     * 
     * @param configNo 配置编码
     * @return 加班配置
     */
    OverTimeConfigDO getLatestByConfigNo(String configNo);

    /**
     * 根据国家查询加班配置
     * 
     * @param country 国家
     * @return 加班配置
     */
    List<OverTimeConfigDO> getByCountry(String country);


    /**
     * 根据国家列表查询加班配置
     * 
     * @param countries 国家列表
     * @return 加班配置
     */
    List<OverTimeConfigDO> listByCountries(List<String> countries);

    /**
     * 根据配置id列表查询最新且启用的加班配置
     * 
     * @param configIdList 配置id列表
     * @return 加班配置
     */
    List<OverTimeConfigDO> listLatestByConfigIds(List<Long> configIdList);

    /**
     * 根据配置id列表查询加班配置
     *
     * @param configIdList 配置id列表
     * @return 加班配置
     */
    List<OverTimeConfigDO> listByConfigIds(List<Long> configIdList);

    /**
     * 根据配置编码查询最新配置(不区分是否启用)
     * 
     * @param configNo 配置编码
     * @return 加班配置
     */
    List<OverTimeConfigDO> listByConfigNo(String configNo);

    /**
     * 根据查询条件查询加班配置
     * 
     * @param query 查询条件
     * @return 加班配置
     */
    List<OverTimeConfigDO> listByQuery(OverTimeConfigQuery query);

    /**
     * 根据国家列表查询国家级别的加班配置
     * 
     * @param countryList 国家列表
     * @return 国家级别的加班配置列表
     */
    List<OverTimeConfigDO> listCountryLevelConfigsByCountries(List<String> countryList);

    /**
     * 分页查询
     */
    List<OverTimeConfigDO> pageQuery(OverTimeConfigPageQuery query);
}
