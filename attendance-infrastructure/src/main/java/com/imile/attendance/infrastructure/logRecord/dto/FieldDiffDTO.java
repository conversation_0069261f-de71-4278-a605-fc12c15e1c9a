package com.imile.attendance.infrastructure.logRecord.dto;

import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.util.ApiModelPropertyUtils;
import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
@Data
public class FieldDiffDTO {

    /**
     * 对象类名
     */
    private String objClassName;
    /**
     * 旧值
     */
    private JSONObject oldData;
    /**
     * 新值
     */
    private JSONObject newData;



    public static void main(String[] args) throws ClassNotFoundException {
        String name = CalendarConfigDO.class.getName();
        System.out.println(name);

        Class<?> aClass = Class.forName(name);
        System.out.println(aClass);

        String fieldNameDesc = ApiModelPropertyUtils.getPropertyValue(aClass, "country");
        System.out.println("country字段描述: " + fieldNameDesc); // 输出: 国家
    }
}
