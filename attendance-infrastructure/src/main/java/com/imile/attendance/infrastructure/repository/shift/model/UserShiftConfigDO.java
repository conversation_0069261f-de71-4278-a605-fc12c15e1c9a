package com.imile.attendance.infrastructure.repository.shift.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.common.annotation.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17 
 * @Description
 */
@ApiModel(description = "员工排班配置表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_shift_config")
public class UserShiftConfigDO extends BaseDO {

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户
     */
    @ApiModelProperty(value = "用户")
    private Long userId;
    /**
     * 考勤日历方案id
     */
    @ApiModelProperty(value = "考勤日历方案id")
    private Long attendanceConfigId;
    /**
     * 班次规则id
     */
    @ApiModelProperty(value = "班次规则id")
    private Long punchClassConfigId;
    /**
     * 班次时间
     */
    @ApiModelProperty(value = "班次时间")
    private Date classTime;
    /**
     * 排班次的dayId
     */
    @ApiModelProperty(value = "排班次的dayId")
    private Long dayId;
    /**
     * 当天的排班规则，有班次时是班次名称，无班次是OFF H 等
     */
    @ApiModelProperty(value = "当天的排班规则，有班次时是班次名称，无班次是OFF H 等")
    private String dayShiftRule;
    /**
     * 排班类型,自动排班、循环排班、自定义排班 ShiftTypeEnum
     */
    @ApiModelProperty(value = "排班类型,自动排班、循环排班、自定义排班 ShiftTypeEnum")
    private String shiftType;
    /**
     * 数据来源:页面操作/批量排班/系统排班/循环排班/排班导入
     */
    @ApiModelProperty(value = "数据来源:页面操作/批量排班/系统排班/循环排班/排班导入")
    private String dataSource;
    /**
     * 是否最新
     */
    @ApiModelProperty(value = "是否最新")
    private Integer isLatest;
    /**
     * 排班任务标识
     */
    @ApiModelProperty(value = "排班任务标识")
    private String taskFlag;

    /**
     * 是否有班次
     */
    public Boolean havePunchClassId() {
        return Objects.nonNull(this.punchClassConfigId) && this.punchClassConfigId > 0;
    }
}
