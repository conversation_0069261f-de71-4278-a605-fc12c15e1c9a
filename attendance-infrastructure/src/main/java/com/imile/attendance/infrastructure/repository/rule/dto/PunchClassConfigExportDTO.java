package com.imile.attendance.infrastructure.repository.rule.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigExportDTO implements Serializable {

    /**
     * 班次ID
     */
    private Long id;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次类型
     */
    private String classType;

    /**
     * 状态
     */
    private String status;

    /**
     * 时段
     */
    private Integer sortNo;

    /**
     * 总法定工作时长（不包含休息时间）
     */
    private BigDecimal totalLegalWorkingHours;

    /**
     * 总出勤时长（包含休息时间）
     */
    private BigDecimal totalAttendanceHours;

    /**
     * 班次时段ID
     */
    private Long itemClassId;

    /**
     * 上班时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：1970-01-01 18:00:00
     */
    private Date punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date earliestPunchInTime;

    /**
     * 最晚上班打卡时间 仅有时分秒信息
     */

    private Date latestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：1970-01-01 19:00:00
     */
    private Date latestPunchOutTime;

    /**
     * 弹性时间
     */
    private BigDecimal elasticTime;

    /**
     * 休息开始时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date restStartTime;

    /**
     * 休息结束时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date restEndTime;

    /**
     * 法定工作时长（不包含休息时间）
     */
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    private BigDecimal attendanceHours;

    /**
     * 适用国家
     */
    private String country;

    /**
     * 适用部门
     */
    private String deptIds;

    /**
     * 适用部门
     */
    private List<String> deptStrList;

    /**
     * 适用人员
     */
    private List<String> userStrList;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 最近修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

}
