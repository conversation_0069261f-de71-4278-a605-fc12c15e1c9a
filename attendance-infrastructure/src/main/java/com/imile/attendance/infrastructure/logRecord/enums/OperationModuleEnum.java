package com.imile.attendance.infrastructure.logRecord.enums;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description 日历操作模块
 */
@Getter
public enum OperationModuleEnum {

    CALENDAR_MODULE("CALENDAR_MODULE", "日历模块"),
    SHIFT_MODULE("SHIFT_MODULE", "班次管理模块"),
    ATTENDANCE_RULE_MODULE("ATTENDANCE_RULE_MODULE", "考勤规则模块"),
    SHIFT_SCHEDULE_MODULE("SHIFT_SCHEDULE_MODULE", "排班计划模块"),
    ATTENDANCE_ARCHIVE_MODULE("ATTENDANCE_ARCHIVE_MODULE", "考勤档案模块"),
    ATTENDANCE_DEVICE_MODULE("ATTENDANCE_DEVICE_MODULE", "考勤设备模块"),
    LEAVE_CONFIG_MODULE("LEAVE_CONFIG_MODULE", "假期规则模块"),
    ;


    private final String code;

    private final String desc;

    OperationModuleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static final Map<String, OperationModuleEnum> cacheMap = new ConcurrentHashMap<>();

    public static OperationModuleEnum getOperationModule(String code) {
        return code == null ? null : cacheMap.get(code);
    }

    static {
        OperationModuleEnum[] attributes = values();
        for (OperationModuleEnum codeEnum : attributes) {
            cacheMap.put(codeEnum.getCode(), codeEnum);
        }

    }
}
