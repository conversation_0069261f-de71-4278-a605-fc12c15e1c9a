package com.imile.attendance.infrastructure.repository.form.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;


import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
public interface AttendanceFormRelationDao extends IService<AttendanceFormRelationDO> {

    List<AttendanceFormRelationDO> selectRelationByFormIdList(List<Long> formIdList);

    List<AttendanceFormRelationDO> selectRelationByRelationIdList(List<Long> relationIdList);

}

