package com.imile.attendance.infrastructure.repository.form.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;


import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
public interface AttendanceFormAttrDao extends IService<AttendanceFormAttrDO> {

    List<AttendanceFormAttrDO> selectFormAttrByFormIdList(List<Long> formIdList);

    List<AttendanceFormAttrDO> selectFormAttrByFormId(Long formId);
}

