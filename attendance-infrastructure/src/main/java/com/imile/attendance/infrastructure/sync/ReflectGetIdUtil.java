package com.imile.attendance.infrastructure.sync;

import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26 
 * @Description
 */
@Slf4j
public class ReflectGetIdUtil {

    // ID方法缓存，避免重复反射查找
    private static final Map<Class<?>, Method> ID_METHOD_CACHE = new ConcurrentHashMap<>();

    /**
     * 通过缓存优化的ID提取方法
     */
    public static Object extractId(Object obj) {
        if (obj == null) {
            return null;
        }

        Class<?> clazz = obj.getClass();
        // 从缓存获取getter方法
        Method getIdMethod = ID_METHOD_CACHE.computeIfAbsent(clazz, ReflectGetIdUtil::findIdMethod);

        if (getIdMethod == null) {
            log.error("无法找到类{}的getId方法", clazz.getName());
            return null;
        }

        try {
            return getIdMethod.invoke(obj);
        } catch (Exception e) {
            log.error("调用getId方法失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 查找类的ID获取方法
     * 支持多种常见命名模式
     */
    private static Method findIdMethod(Class<?> clazz) {
        // 尝试多种可能的getter方法名
        String[] possibleMethodNames = {"getId", "getID", "get_id", "id"};

        for (String methodName : possibleMethodNames) {
            try {
                Method method = clazz.getMethod(methodName);
                // 确保返回类型不是void
                if (method.getReturnType() != void.class) {
                    return method;
                }
            } catch (NoSuchMethodException e) {
                // 忽略，尝试下一个可能的方法名
            }
        }

        // 最后尝试查找名为"id"的字段，并获取其getter方法
        try {
            Field idField = clazz.getDeclaredField("id");
            String capitalizedFieldName = idField.getName().substring(0, 1).toUpperCase() +
                    idField.getName().substring(1);
            return clazz.getMethod("get" + capitalizedFieldName);
        } catch (Exception e) {
            // 忽略
        }

        log.warn("无法找到类{}的ID获取方法", clazz.getName());
        return null;
    }


    public static void main(String[] args) {
        for (int i = 0; i < 1000; i++) {
            CalendarConfigDO calendarConfigDO = new CalendarConfigDO();
            calendarConfigDO.setId((long) i + 1);
            System.out.println(extractId(calendarConfigDO));
        }
    }
}
