package com.imile.attendance.infrastructure.repository.employee.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} UserLeaveStageDetailQuery
 * {@code @since:} 2024-04-19 17:14
 * {@code @description:}
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLeaveStageDetailQuery {
    /**
     * 用户假期id
     */
    private List<Long> leaveIdList;

    /**
     * 假期标记: 0：表示非结转，1：结转，...
     */
    private Integer leaveMark;

    /**
     * 是否失效: 0：表示未失效，1：失效，...
     */
    private Integer isInvalid;
}
