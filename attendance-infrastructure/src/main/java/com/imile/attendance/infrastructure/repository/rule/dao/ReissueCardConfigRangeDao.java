package com.imile.attendance.infrastructure.repository.rule.dao;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigRangeByDateQuery;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
public interface ReissueCardConfigRangeDao extends IService<ReissueCardConfigRangeDO> {

    /**
     * 根据用户ID列表获取当前最新的补卡规则配置适用范围
     *
     * @param userIds 用户ID列表
     * @return 补卡规则配置适用范围列表
     */
    List<ReissueCardConfigRangeDO> listConfigRanges(List<Long> userIds);


    /**
     * 根据用户ID列表获取启用的补卡规则配置适用范围(不区分是否最新)
     *
     * @param userIds 用户ID列表
     * @return 补卡规则配置适用范围列表
     */
    List<ReissueCardConfigRangeDO> listActivedConfigRanges(List<Long> userIds);

    /**
     * 根据用户ID列表获取当前最新的补卡规则配置适用范围（不区分是否启用）
     *
     * @param userIds 用户ID列表
     * @return 补卡规则配置适用范围列表
     */
    List<ReissueCardConfigRangeDO> listNotDeletedConfigRanges(List<Long> userIds);

    /**
     * 查询员工所有的适用范围
     *
     * @param userId 用户ID
     * @return 员工所有的适用范围
     */
    List<ReissueCardConfigRangeDO> listAllConfigRanges(Long userId);

    /**
     * 根据用户id列表获取当前配置适用范围(不区分是否最新和状态)
     */
    List<ReissueCardConfigRangeDO> listAllRangeByUserIds(List<Long> userIdList);

    /**
     * 根据配置ID获取当前最新启用的补卡规则配置适用范围
     *
     * @param configId 配置ID
     * @return 补卡规则配置适用范围列表
     */
    List<ReissueCardConfigRangeDO> listByConfigId(Long configId);

    /**
     * 根据配置ID获取启用的补卡规则配置适用范围(不区分是否最新)
     *
     * @param configId 配置ID
     * @return 启用的补卡规则配置适用范围列表
     */
    List<ReissueCardConfigRangeDO> listActivedConfigByConfigId(Long configId);

    /**
     * 根据配置ID获取补卡规则配置适用范围（不区分是否启用）
     *
     * @param configId 配置ID
     * @return 补卡规则配置适用范围列表
     */
    List<ReissueCardConfigRangeDO> listNotDeletedByConfigId(Long configId);

    /**
     * 根据配置ID获取停用的补卡规则配置适用范围
     *
     * @param configId 配置ID
     * @return 补卡规则配置适用范围列表
     */
    List<ReissueCardConfigRangeDO> listDisabledByConfigId(Long configId);

    /**
     * 根据配置ID列表获取当前最新的补卡规则配置适用范围
     *
     * @param configIdList 配置ID列表
     * @return 补卡规则配置适用范围列表
     */
    List<ReissueCardConfigRangeDO> listByConfigIds(List<Long> configIdList);

    /**
     * 根据配置ID列表获取当前最新的补卡规则配置适用范围（不区分是否启用）
     *
     * @param configIdList 配置ID列表
     * @return 补卡规则配置适用范围列表
     */
    List<ReissueCardConfigRangeDO> listNotDeletedByConfigIds(List<Long> configIdList);

    /**
     * 统计国家下在职非司机且未配置规则的用户总数（国家级规则的人数）
     *
     * @param country 国家
     * @return 用户总数
     */
    Integer countOnJobNoDriverNotConfiguredUsers(String country);

    /**
     * 统计国家下在职非司机且未配置规则的用户列表（国家级规则的用户列表）
     *
     * @param ruleRangeUserQuery 查询条件
     * @return 用户列表
     */
    List<UserInfoDO> listOnJobNoDriverUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery);

    /**
     * 统计多个国家下在职非司机且未配置规则的用户列表
     *
     * @param ruleRangeUserQuery 查询条件
     * @return 用户列表
     */
    List<UserInfoDO> listOnJobNoDriverMultiCountryUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery);

    /**
     * 根据日期范围查询规则
     *
     * @param query
     * @return
     */
    List<ReissueCardConfigRangeDO> selectConfigRangeByDate(ReissueCardConfigRangeByDateQuery query);
}
