package com.imile.attendance.infrastructure.repository.employee.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.employee.dao.UserClassNatureModifyRecordDao;
import com.imile.attendance.infrastructure.repository.employee.mapper.UserClassNatureModifyRecordMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserClassNatureModifyRecordDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/5/10
 */
@Component
@RequiredArgsConstructor
public class UserClassNatureModifyRecordDaoImpl extends ServiceImpl<UserClassNatureModifyRecordMapper, UserClassNatureModifyRecordDO> implements UserClassNatureModifyRecordDao {

    @Override
    public List<UserClassNatureModifyRecordDO> selectByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserClassNatureModifyRecordDO> queryWrapper = Wrappers.lambdaQuery(UserClassNatureModifyRecordDO.class);
        queryWrapper.eq(UserClassNatureModifyRecordDO::getUserId, userId);
        queryWrapper.eq(UserClassNatureModifyRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(UserClassNatureModifyRecordDO::getCreateDate);
        return this.list(queryWrapper);
    }

    @Override
    public UserClassNatureModifyRecordDO selectLatestByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            return null;
        }
        LambdaQueryWrapper<UserClassNatureModifyRecordDO> queryWrapper = Wrappers.lambdaQuery(UserClassNatureModifyRecordDO.class);
        queryWrapper.eq(UserClassNatureModifyRecordDO::getUserId, userId);
        queryWrapper.eq(UserClassNatureModifyRecordDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(UserClassNatureModifyRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(UserClassNatureModifyRecordDO::getCreateDate);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }
}
