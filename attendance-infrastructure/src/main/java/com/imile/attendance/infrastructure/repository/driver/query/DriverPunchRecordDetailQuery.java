package com.imile.attendance.infrastructure.repository.driver.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} DriverPunchRecordDetailQuery
 * {@code @since:} 2024-01-22 11:21
 * {@code @description:}
 */
@Data
public class DriverPunchRecordDetailQuery implements Serializable {

    private static final long serialVersionUID = 4183463199449847641L;

    /**
     * 司机账号
     */
    private String userCode;

    /**
     * day_id 示例：20240124
     */
    private Long dayId;

    /**
     * dayIdList
     */
    private List<Long> dayIdList;

    /**
     * 打卡记录操作类型
     */
    private Integer operationType;
}
