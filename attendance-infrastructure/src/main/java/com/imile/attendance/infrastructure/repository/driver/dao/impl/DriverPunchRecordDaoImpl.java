package com.imile.attendance.infrastructure.repository.driver.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverPunchRecordDao;
import com.imile.attendance.infrastructure.repository.driver.mapper.DriverPunchRecordMapper;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/6
 * @Description
 */
@Component
@RequiredArgsConstructor
public class DriverPunchRecordDaoImpl extends ServiceImpl<DriverPunchRecordMapper, DriverPunchRecordDO> implements DriverPunchRecordDao {

    @Override
    public List<DriverPunchRecordDO> listByUserCodeAndDayIdAndOperationType(DriverPunchRecordQuery query) {
        if(ObjectUtil.isNull(query)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DriverPunchRecordDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(DriverPunchRecordDO::getUserCode, query.getUserCode());
        queryWrapper.eq(DriverPunchRecordDO::getDayId, query.getDayId());
        queryWrapper.eq(DriverPunchRecordDO::getOperationType, query.getOperationType());
        queryWrapper.eq(DriverPunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<DriverPunchRecordDO> listPunchRecordDetail(DriverPunchRecordDetailQuery query) {
        if(ObjectUtil.isNull(query)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DriverPunchRecordDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getUserCode()), DriverPunchRecordDO::getUserCode, query.getUserCode());
        queryWrapper.eq(ObjectUtil.isNotNull(query.getDayId()), DriverPunchRecordDO::getDayId, query.getDayId());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getDayIdList()), DriverPunchRecordDO::getDayId, query.getDayIdList());
        queryWrapper.eq(ObjectUtil.isNotNull(query.getOperationType()), DriverPunchRecordDO::getOperationType, query.getOperationType());
        queryWrapper.eq(DriverPunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(DriverPunchRecordDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<DriverPunchRecordDO> listByPage(int currentPage, int pageSize) {
        PageInfo<DriverPunchRecordDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}

