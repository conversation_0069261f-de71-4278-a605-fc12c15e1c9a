package com.imile.attendance.infrastructure.logRecord.dto;

import com.imile.attendance.infrastructure.logRecord.enums.OperationCodeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.OperationModuleEnum;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31
 * @Description
 */
@Data
public class LogOperateBO {

    private OperationTypeEnum operationTypeEnum;
    private OperationCodeEnum operationCodeEnum;
    private OperationModuleEnum operationModuleEnum;

    public static LogOperateBO of(OperationTypeEnum operationTypeEnum,
            OperationCodeEnum operationCodeEnum,
            OperationModuleEnum operationModuleEnum) {
        LogOperateBO logOperateBO = new LogOperateBO();
        logOperateBO.setOperationTypeEnum(operationTypeEnum);
        logOperateBO.setOperationCodeEnum(operationCodeEnum);
        logOperateBO.setOperationModuleEnum(operationModuleEnum);
        return logOperateBO;
    }

    public static LogOperateBO buildByOperateType(String operationType) {
        OperationTypeEnum operationTypeEnum = OperationTypeEnum.getOperationType(operationType);
        if (operationTypeEnum == null) {
            return null;
        }
        OperationCodeEnum operationCodeEnum = operationTypeEnum.getOperationCodeEnum();
        if (operationCodeEnum == null) {
            return null;
        }
        OperationModuleEnum operationModuleEnum = operationCodeEnum.getOperationModuleEnum();
        if (operationModuleEnum == null) {
            return null;
        }
        return LogOperateBO.of(operationTypeEnum, operationCodeEnum, operationModuleEnum);
    }
}
