package com.imile.attendance.infrastructure.repository.shift.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/21 
 * @Description 用户排班的天配置
 */
@Data
public class ShiftDayConfigDTO {

    /**
     * 用户的id
     */
    private Long userId;

    /**
     * 排班日期，yyyy-MM-dd格式
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date classTime;

    /**
     * 日期
     */
    private Date date;

    /**
     * 日期字符串（导出使用,15-Nov-22格式）
     */
    private String dateStrForExport;

    /**
     * dayId
     */
    private Long dayId;

    /**
     * 班次信息id
     */
    private Long classId;

    /**
     * 班次名称
     */
    private String className;


    /**
     * 排班记录ID
     */
    private Long userShiftConfigId;


    /**
     * 是否可编辑(今天之后，入职之后，有班次) 0不可以  1可以
     */
    private Integer isEdit;

    /**
     * 当天的打卡规则，对于为排班的日期，这里可能存在默认值，比如当天节假日，这里默认值就是PH
     * 如果是历史排班日期，就展示已经排好的类型
     */
    private String dayShiftRule;

    /**
     * 该用户可以选择的班次,OFF,H
     */
    List<UserDayShiftRuleDTO> userDayShiftRuleList;

    /**
     * 该用户在当天实际的排班规则类型，展示日历类型和具体当天的班次或假期名称
     */
    UserDayShiftRuleDetailDTO userDayShiftRuleDetailDTO;
}
