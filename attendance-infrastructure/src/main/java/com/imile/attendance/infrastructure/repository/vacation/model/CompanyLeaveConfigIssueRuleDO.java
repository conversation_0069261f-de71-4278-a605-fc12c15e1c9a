package com.imile.attendance.infrastructure.repository.vacation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 国家福利假发放规则表
 *
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@ApiModel(description = "国家福利假发放规则表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("company_leave_config_issue_rule")
public class CompanyLeaveConfigIssueRuleDO extends BaseDO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家假期配置id
     */
    @ApiModelProperty(value = "国家假期配置id")
    private Long leaveId;

    /**
     * 发放频次：1：周期性发放，2:一次性发放
     */
    @ApiModelProperty(value = "发放频次：1：周期性发放，2:一次性发放")
    private Integer issueFrequency;

    /**
     * 发放时间：1：每年固定日，2:每月固定日，3:员工入职日，4:按入职日，5:按派遣日
     */
    @ApiModelProperty(value = "发放时间：1：每年固定日，2:每月固定日，3:员工入职日，4:按入职日，5:按派遣日")
    private Integer issueTime;

    /**
     * 发放日期：月份：发放频次为每年的时候月份不为0，发放频次为每月的时候月份为0
     */
    @ApiModelProperty(value = "发放日期：月份：发放频次为每年的时候月份不为0，发放频次为每月的时候月份为0")
    private Integer issueMonth;

    /**
     * 发放日期：日
     */
    @ApiModelProperty(value = "发放日期：日")
    private Integer issueDay;

    /**
     * 发放类型：1:固定额度 2:司领递增 3:不限定额度 4:无初始额度 5:按派遣国远近 6:随年龄增加 7:随工龄增加 8:按常驻省市配置
     */
    @ApiModelProperty(value = "发放类型：1:固定额度 2:司领递增 3:不限定额度 4:无初始额度 5:按派遣国远近 6:随年龄增加 7:随工龄增加 8:按常驻省市配置")
    private Integer issueType;

    /**
     * 发放额度
     */
    @ApiModelProperty(value = "发放额度:没用，主要记录额度，对于issue_type=1使用假期详情表发放")
    private BigDecimal issueQuota;

    /**
     * 循环单位(每满xx年/月)
     */
    @ApiModelProperty(value = "每满单位：0：无 1：年 2:月")
    private Integer cycleUnit;

    /**
     * 循环数值(每满xx年/月)
     */
    @ApiModelProperty(value = "每满月/年数量")
    private Integer cycleNumber;

    /**
     * 是否折扣：0:表示无该选项 ，1:表示折扣，2:表示无需折扣
     */
    @ApiModelProperty(value = "是否折扣：0:表示无该选项 ，1:表示折扣，2:表示无需折扣")
    private Long isConvert;

    /**
     * 是否按入职日到派遣日折扣：0:表示无该选项 ，1:表示折扣，2:表示无需折扣
     */
    @ApiModelProperty(value = "是否折扣：0:表示无该选项 ，1:表示折扣，2:表示无需折扣")
    private Integer isConvertDispatch;

    /**
     * 假期发放时取整规则：0:表示无该选项 ，1:表示不取整（四舍五入两位小数），2:表示向下取整（1.23-->1），3:表示向上取整（1.23-->2）:当is_convert == 1的时候，该字段才不为0，其余为0
     */
    @ApiModelProperty(value = "假期发放时取整规则：0:表示无该选项 ，1:表示不取整（四舍五入两位小数），2:表示向下取整（1.23-->1），3:表示向上取整（1.23-->2）:当is_convert == 1的时候，该字段才不为0，其余为0")
    private Integer issueRoundingRule;

    /**
     * 是否符合跨层级新规则时重新计算发放假期 0:否 1:是
     */
    @ApiModelProperty(value = "是否符合跨层级新规则时重新计算发放假期 0:否 1:是")
    private Integer isRecalculate;
}