package com.imile.attendance.infrastructure.repository.deviceConfig.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceGpsConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceConfigFilterQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceGpsConfigQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
public interface AttendanceGpsConfigDao extends IService<AttendanceGpsConfigDO> {

    /**
     * GPS列表查询
     */
    List<AttendanceGpsConfigDO> list(AttendanceGpsConfigQuery query);

    List<AttendanceGpsConfigDO> listByPage(int currentPage, int pageSize);

    /**
     * GPS筛选条件查询
     */
    List<String> selectFilterList(AttendanceConfigFilterQuery query);
}
