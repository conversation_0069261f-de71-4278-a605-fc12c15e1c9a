package com.imile.attendance.infrastructure.repository.shift.dto;

import com.imile.attendance.enums.CalendarDayTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工作日，则展示P及班次名称，若暂未排班，则不展示班次名称
 * 节假日，则展示H及假期名称，若排班了，则展示班次名称
 * 休息日，则展示为W，若暂未排班，则不展示班次名称;排班了有班次，则展示为W及班次名称
 *
 * <AUTHOR> chen
 * @Date 2025/4/23 
 * @Description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDayShiftRuleDetailDTO {

    /**
     * 实际日历类型，P,H,W
     */
    private String calendarDayType;

    /**
     * 当为H时，展示假期名称
     */
    private String holidayName;

    /**
     * 实际排班规则名称
     */
    private String realShiftRuleName;


    public static UserDayShiftRuleDetailDTO buildPresentAndClass(String className) {
        return UserDayShiftRuleDetailDTO.builder()
                .calendarDayType(CalendarDayTypeEnum.PRESENT.getShortCode())
                .realShiftRuleName(className)
                .build();
    }

    public static UserDayShiftRuleDetailDTO buildPresentAndNoClass() {
        return UserDayShiftRuleDetailDTO.builder()
                .calendarDayType(CalendarDayTypeEnum.PRESENT.getShortCode())
                .build();
    }

    public static UserDayShiftRuleDetailDTO buildHoliday(String holidayName, String className) {
        return UserDayShiftRuleDetailDTO.builder()
                .calendarDayType(CalendarDayTypeEnum.HOLIDAY.getShortCode())
                .holidayName(holidayName)
                .realShiftRuleName(className)
                .build();
    }

    public static UserDayShiftRuleDetailDTO buildOff() {
        return UserDayShiftRuleDetailDTO.builder()
                .calendarDayType(CalendarDayTypeEnum.WEEKEND.getShortCode())
                .build();
    }

    public static UserDayShiftRuleDetailDTO buildOffAndClass(String className) {
        return UserDayShiftRuleDetailDTO.builder()
                .calendarDayType(CalendarDayTypeEnum.WEEKEND.getShortCode())
                .realShiftRuleName(className)
                .build();
    }

}
