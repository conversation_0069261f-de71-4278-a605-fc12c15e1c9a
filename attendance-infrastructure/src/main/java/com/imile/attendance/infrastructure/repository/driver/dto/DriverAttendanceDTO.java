package com.imile.attendance.infrastructure.repository.driver.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceDTO
 * {@code @since:} 2024-01-24 14:59
 * {@code @description:}
 */
@Data
public class DriverAttendanceDTO implements Serializable {

    private static final long serialVersionUID = 299764480283000184L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 司机账号
     */
    private String userCode;

    /**
     * 年
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 日
     */
    private Integer day;

    /**
     * 日期
     */
    private Date date;

    /**
     * day_id 示例：20240124
     */
    private Long dayId;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    private Integer attendanceType;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    private String attendanceTypeDesc;

    /**
     * 司机签收次数
     */
    private Long dldNumber;

    /**
     * 轨迹打卡次数
     */
    private Long locusNumber;

    /**
     * 申请单id
     */
    private Long formId;

    /**
     * 最近操作时间：取司机打卡记录表某人最近的操作时间
     */
    private Date lastOperatingTime;
}
