package com.imile.attendance.infrastructure.repository.deviceConfig.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceMobileConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.dto.AttendanceMobileConfigListDTO;
import com.imile.attendance.infrastructure.repository.deviceConfig.mapper.AttendanceMobileConfigMapper;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceMobileConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigListQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/4/17
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceMobileConfigDaoImpl extends ServiceImpl<AttendanceMobileConfigMapper, AttendanceMobileConfigDO> implements AttendanceMobileConfigDao {

    @Override
    public List<AttendanceMobileConfigDO> list(AttendanceMobileConfigQuery query) {
        LambdaQueryWrapper<AttendanceMobileConfigDO> queryWrapper = Wrappers.lambdaQuery();
//        if (StringUtils.isNotBlank(query.getUserCodeOrName())) {
//            queryWrapper.and(wrapper -> wrapper.like(AttendanceMobileConfigDO::getUserCode, query.getUserCodeOrName())
//                    .or().like(AttendanceMobileConfigDO::getUserName, query.getUserCodeOrName()));
//        }
        if (StringUtils.isNotBlank(query.getUserCode())) {
            queryWrapper.eq(AttendanceMobileConfigDO::getUserCode, query.getUserCode());
        }
        if (StringUtils.isNotBlank(query.getMobileUnicode())) {
            queryWrapper.eq(AttendanceMobileConfigDO::getMobileUnicode, query.getMobileUnicode());
        }
        if (StringUtils.isNotBlank(query.getMobileModel())) {
            queryWrapper.eq(AttendanceMobileConfigDO::getMobileModel, query.getMobileModel());
        }
        if (StringUtils.isNotBlank(query.getMobileBranch())) {
            queryWrapper.eq(AttendanceMobileConfigDO::getMobileBranch, query.getMobileBranch());
        }
        if (StringUtils.isNotBlank(query.getMobileVersion())) {
            queryWrapper.eq(AttendanceMobileConfigDO::getMobileVersion, query.getMobileVersion());
        }
        if (Objects.nonNull(query.getIsDelete())) {
            queryWrapper.eq(AttendanceMobileConfigDO::getIsDelete, query.getIsDelete());
        }

        return list(queryWrapper);
    }

    @Override
    public List<AttendanceMobileConfigListDTO> queryAttendanceMobileConfig(AttendanceMobileConfigListQuery query) {
        return this.baseMapper.queryAttendanceMobileConfig(query);
    }

    @Override
    public List<AttendanceMobileConfigDO> listByPage(int currentPage, int pageSize) {
        PageInfo<AttendanceMobileConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }

    @Override
    public List<AttendanceMobileConfigDO> queryAttendanceMobileConfigByUserCode(String userCode) {
        LambdaQueryWrapper<AttendanceMobileConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AttendanceMobileConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(AttendanceMobileConfigDO::getUserCode, userCode);
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceMobileConfigDO> queryHistoryMobileConfigByUserCode(String userCode) {
        LambdaQueryWrapper<AttendanceMobileConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AttendanceMobileConfigDO::getUserCode, userCode);
        queryWrapper.orderByDesc(AttendanceMobileConfigDO::getCreateDate);
        return this.list(queryWrapper);
    }
}
