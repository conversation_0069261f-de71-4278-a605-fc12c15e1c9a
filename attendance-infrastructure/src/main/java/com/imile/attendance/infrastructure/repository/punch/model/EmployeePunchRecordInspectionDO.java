package com.imile.attendance.infrastructure.repository.punch.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} EmployeePunchRecordInspection
 * {@code @since:} 2025-01-09 14:18
 * {@code @description:}
 */

/**
 * 打卡记录巡检表
 */
@ApiModel(description = "打卡记录巡检表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("employee_punch_record_inspection")
public class EmployeePunchRecordInspectionDO extends BaseDO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 打卡记录主键id
     */
    @ApiModelProperty(value = "打卡记录主键id")
    private Long punchRecordId;

    /**
     * 用户账号
     */
    @ApiModelProperty(value = "用户账号")
    private String userCode;

    /**
     * 打卡地
     */
    @ApiModelProperty(value = "打卡地")
    private String country;

    /**
     * dayid 示例：20220124
     */
    @ApiModelProperty(value = "dayid 示例：20220124")
    private String dayId;

    /**
     * 数据来源 司机打卡：driver 微信：wechat
     */
    @ApiModelProperty(value = "数据来源 司机打卡：driver 微信：wechat")
    private String sourceType;

    /**
     * 打卡时间
     */
    @ApiModelProperty(value = "打卡时间")
    private Date punchTime;

    /**
     * 打卡记录表创建时间-当地
     */
    @ApiModelProperty(value = "打卡记录表创建时间-当地")
    private Date punchRecordCreateDateLocal;

    /**
     * 打卡记录表创建时间
     */
    @ApiModelProperty(value = "打卡记录表创建时间")
    private Date punchRecordCreateDate;

    /**
     * 打卡区域
     */
    @ApiModelProperty(value = "打卡区域")
    private String punchArea;

    /**
     * 打卡方式
     */
    @ApiModelProperty(value = "打卡方式")
    private String punchCardType;
}