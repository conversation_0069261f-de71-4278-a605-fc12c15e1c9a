package com.imile.attendance.infrastructure.repository.vacation.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigCarryOverRangeDao;
import com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveConfigCarryOverRangeMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description 假期结转规则范围 数据库操作实现类
 */
@Component
@RequiredArgsConstructor
public class CompanyLeaveConfigCarryOverRangeDaoImpl extends ServiceImpl<CompanyLeaveConfigCarryOverRangeMapper, CompanyLeaveConfigCarryOverRangeDO> implements CompanyLeaveConfigCarryOverRangeDao {

    @Override
    public List<CompanyLeaveConfigCarryOverRangeDO> selectByCarryOverId(List<Long> carryOverIdList) {
        if (CollUtil.isEmpty(carryOverIdList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigCarryOverRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CompanyLeaveConfigCarryOverRangeDO::getCarryOverId, carryOverIdList);
        queryWrapper.eq(CompanyLeaveConfigCarryOverRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<CompanyLeaveConfigCarryOverRangeDO> getLeaveConfigCarryOverRangeList(Long carryOverId) {
        if (ObjectUtil.isNull(carryOverId)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigCarryOverRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CompanyLeaveConfigCarryOverRangeDO::getCarryOverId, carryOverId);
        queryWrapper.eq(CompanyLeaveConfigCarryOverRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
