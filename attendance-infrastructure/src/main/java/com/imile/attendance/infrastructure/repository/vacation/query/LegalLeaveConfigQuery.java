package com.imile.attendance.infrastructure.repository.vacation.query;

import com.imile.common.query.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class LegalLeaveConfigQuery extends BaseQuery {

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地国家列表
     */
    private List<String> locationCountryList;

    /**
     * 上一年
     */
    private Integer lastYear;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 下一年
     */
    private Integer nextYear;

    /**
     * 法定假期名称
     */
    private String legalLeaveName;

    /**
     * 日历id
     */
    private Long attendanceConfigId;

    /**
     * 年集合
     */
    private List<Integer> yearList;

}
