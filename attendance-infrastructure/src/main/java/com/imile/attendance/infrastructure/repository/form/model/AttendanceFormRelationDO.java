package com.imile.attendance.infrastructure.repository.form.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@ApiModel(description = "考勤申请单关联表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_form_relation")
public class AttendanceFormRelationDO extends BaseDO {

    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 申请单ID
     */
    @ApiModelProperty(value = "申请单ID")
    private Long formId;

    /**
     * 关联信息ID
     */
    @ApiModelProperty(value = "关联信息ID")
    private Long relationId;

    /**
     * 关联信息类型(异常考勤/审批单据)
     */
    @ApiModelProperty(value = "关联信息类型(异常考勤/审批单据)")
    private String relationType;
}

