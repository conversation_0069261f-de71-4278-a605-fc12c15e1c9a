package com.imile.attendance.infrastructure.repository.employee.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Data
public class UserArchiveDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 英文姓名
     */
    private String userNameEn;

    /**
     * 常驻国
     */
    private String locationCountry;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeType;

    /**
     * 是否为司机 是否为司机
     */
    private Integer isDriver;

    /**
     * 是否派遣
     */
    private Integer isGlobalRelocation;

    /**
     * 工作岗位
     */
    private Long postId;

    /**
     * 所属部门id
     */
    private Long deptId;

    /**
     * 工作状态 在职、离职等
     */
    private String workStatus;

    /**
     * 状态
     */
    private String status;

    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 最近修改时间
     */
    private Date lastUpdDate;

    /**
     * 最近修改人
     */
    private String lastUpdUserName;
}
