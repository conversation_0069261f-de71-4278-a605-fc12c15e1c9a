package com.imile.attendance.infrastructure.common;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/8
 */
@Mapper
public interface AttendanceCommonMapper {
    /**
     * 获取指定表中指定列的最大值
     *
     * @param tableName
     * @param columnName
     * @return
     */
    Long maxValue(@Param("tableName") String tableName, @Param("columnName") String columnName);
}
