package com.imile.attendance.infrastructure.common;

import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/22 
 * @Description
 */
public class CommonUserService {


    public static List<String> getCountryEmployeeTypes(String locationCountry) {
        if (StringUtils.isBlank(locationCountry)) {
            return Collections.emptyList();
        }
        if (CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(locationCountry)) {
            return EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT;
        } else {
            return EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT;
        }
    }

}
