package com.imile.attendance.infrastructure.repository.vacation.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigDao;
import com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveConfigMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveQuery;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description 假期规则 数据库操作实现类
 */
@Component
@RequiredArgsConstructor
public class CompanyLeaveConfigDaoImpl extends ServiceImpl<CompanyLeaveConfigMapper, CompanyLeaveConfigDO> implements CompanyLeaveConfigDao {

    @Override
    public List<CompanyLeaveConfigDO> selectLeaveConfigByCountry(List<String> countryList) {
        if (CollectionUtils.isEmpty(countryList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CompanyLeaveConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CompanyLeaveConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(CompanyLeaveConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(CompanyLeaveConfigDO::getIsDispatch, BusinessConstant.N);
        queryWrapper.in(CompanyLeaveConfigDO::getCountry, countryList);
        return list(queryWrapper);
    }

    @Override
    public List<CompanyLeaveConfigDO> selectLeaveConfig(CompanyLeaveQuery companyLeaveQuery) {
        LambdaQueryWrapper<CompanyLeaveConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(companyLeaveQuery.getIdList())) {
            queryWrapper.in(CompanyLeaveConfigDO::getId, companyLeaveQuery.getIdList());
        }
        if (StringUtils.isNotBlank(companyLeaveQuery.getLeaveName())) {
            queryWrapper.like(CompanyLeaveConfigDO::getLeaveName, companyLeaveQuery.getLeaveName());
        }
        if (CollectionUtils.isNotEmpty(companyLeaveQuery.getLeaveNameList())) {
            queryWrapper.in(CompanyLeaveConfigDO::getLeaveName, companyLeaveQuery.getLeaveNameList());
        }
        if (StringUtils.isNotBlank(companyLeaveQuery.getLeaveType())) {
            queryWrapper.eq(CompanyLeaveConfigDO::getLeaveType, companyLeaveQuery.getLeaveType());
        }
        if (CollectionUtils.isNotEmpty(companyLeaveQuery.getLeaveTypeList())) {
            queryWrapper.in(CompanyLeaveConfigDO::getLeaveType, companyLeaveQuery.getLeaveTypeList());
        }
        if (StringUtils.isNotBlank(companyLeaveQuery.getStatus())) {
            queryWrapper.eq(CompanyLeaveConfigDO::getStatus, companyLeaveQuery.getStatus());
        }
        if (StringUtils.isNotBlank(companyLeaveQuery.getCountry())) {
            queryWrapper.eq(CompanyLeaveConfigDO::getCountry, companyLeaveQuery.getCountry());
        }
        if (CollectionUtils.isNotEmpty(companyLeaveQuery.getCountryList())) {
            queryWrapper.in(CompanyLeaveConfigDO::getCountry, companyLeaveQuery.getCountryList());
        }
        if (Objects.nonNull(companyLeaveQuery.getIsDispatch())) {
            queryWrapper.eq(CompanyLeaveConfigDO::getIsDispatch, companyLeaveQuery.getIsDispatch());
        }

        queryWrapper.eq(CompanyLeaveConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
