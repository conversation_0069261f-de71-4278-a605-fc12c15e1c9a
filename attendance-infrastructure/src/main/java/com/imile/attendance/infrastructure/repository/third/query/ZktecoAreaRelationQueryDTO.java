package com.imile.attendance.infrastructure.repository.third.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZktecoAreaRelationQueryDTO extends ResourceQuery {

    /**
     * 中控区域名称
     */
    private String zktecoAreaName;

    /**
     * 考勤机编号
     */
    private String terminalSn;

    /**
     * 部门
     */
    private Long deptId;

    /**
     * 国家
     */
    private String country;
}
