package com.imile.attendance.infrastructure.repository.vacation.dao.impl;


import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.vacation.dao.UserLeaveConfigHistoryDao;
import com.imile.attendance.infrastructure.repository.vacation.mapper.UserLeaveConfigHistoryMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.UserLeaveConfigHistoryDO;
import com.imile.attendance.infrastructure.repository.vacation.query.UserLeaveConfigHistoryQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 人员常驻国切换历史假期范围表 数据库操作
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-22
 */
@Service
public class UserLeaveConfigHistoryDaoImpl extends ServiceImpl<UserLeaveConfigHistoryMapper, UserLeaveConfigHistoryDO> implements UserLeaveConfigHistoryDao {

    @Override
    public List<UserLeaveConfigHistoryDO> selectLeaveHistoryInfoByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<UserLeaveConfigHistoryDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(UserLeaveConfigHistoryDO::getUserId, userId);
        lambdaQuery.eq(UserLeaveConfigHistoryDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(lambdaQuery);
    }

    @Override
    public List<UserLeaveConfigHistoryDO> selectLeaveHistoryInfo(UserLeaveConfigHistoryQuery query) {
        LambdaQueryWrapper<UserLeaveConfigHistoryDO> lambdaQuery = new LambdaQueryWrapper<>();
        if (Objects.nonNull(query.getUserId())) {
            lambdaQuery.eq(UserLeaveConfigHistoryDO::getUserId, query.getUserId());
        }
        if (StringUtils.isNotBlank(query.getUserCode())) {
            lambdaQuery.eq(UserLeaveConfigHistoryDO::getUserCode, query.getUserCode());
        }
        if (Objects.nonNull(query.getLeaveId())) {
            lambdaQuery.eq(UserLeaveConfigHistoryDO::getLeaveId, query.getLeaveId());
        }
        if (StringUtils.isNotBlank(query.getLeaveName())) {
            lambdaQuery.eq(UserLeaveConfigHistoryDO::getLeaveName, query.getLeaveName());
        }
        if (StringUtils.isNotBlank(query.getLeaveType())) {
            lambdaQuery.eq(UserLeaveConfigHistoryDO::getLeaveType, query.getLeaveType());
        }
        if (StringUtils.isNotBlank(query.getLeaveCountry())) {
            lambdaQuery.eq(UserLeaveConfigHistoryDO::getLeaveCountry, query.getLeaveCountry());
        }
        lambdaQuery.eq(UserLeaveConfigHistoryDO::getIsDelete, IsDeleteEnum.NO);
        return this.list(lambdaQuery);
    }
}
