package com.imile.attendance.infrastructure.repository.calendar.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description 日期表
 */
@ApiModel(description = "日期表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("base_day_info")
public class BaseDayInfoDO extends BaseDO {

    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private Integer year;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    private Integer quarter;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private Integer month;

    /**
     * 周
     */
    @ApiModelProperty(value = "周")
    private Integer week;

    /**
     * 日
     */
    @ApiModelProperty(value = "日")
    private Integer day;

    /**
     * 星期几
     */
    @ApiModelProperty(value = "星期几")
    private String dayOfWeek;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private Date date;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private BigDecimal orderby;
}

