package com.imile.attendance.infrastructure.repository.shift.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17 
 * @Description
 */
@ApiModel(description = "员工循环排班配置表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_cycle_shift_config")
public class UserCycleShiftConfigDO extends BaseDO {
    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /**
     * 循环周期
     */
    @ApiModelProperty(value = "循环周期")
    private Integer cyclePeriod;
    /**
     * 每天的班次明细
     */
    @ApiModelProperty(value = "每天的班次明细")
    private String dayShiftInfo;
    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    private Date effectDate;
    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间")
    private Date expireDate;
    /**
     * 是否最新
     */
    @ApiModelProperty(value = "是否最新")
    private Integer isLatest;
}
