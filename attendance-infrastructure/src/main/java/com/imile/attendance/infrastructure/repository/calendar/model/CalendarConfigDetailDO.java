package com.imile.attendance.infrastructure.repository.calendar.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.attendance.enums.CalendarDayTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarConfigDetailDO
 * {@code @since:} 2025-01-17 14:43
 * {@code @description:}
 */
@ApiModel(description = "日历配置明细表 不保存出勤日。只保存非出勤日")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("calendar_config_detail")
public class CalendarConfigDetailDO extends BaseDO {

    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 日历配置id
     */
    @ApiModelProperty(value = "日历配置id")
    private Long attendanceConfigId;

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private Integer year;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private Integer month;

    /**
     * 天
     */
    @ApiModelProperty(value = "天")
    private Integer day;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private Date date;

    /**
     * 日期信息ID 形如：20211209
     */
    @ApiModelProperty(value = "日期信息ID 形如：20211209")
    private Long dayId;

    /**
     * 出勤类型 PRESENT 应出勤日，WEEKEND 休息日 ，HOLIDAY 节假日
     */
    @ApiModelProperty(value = "出勤类型 PRESENT 应出勤日，WEEKEND 休息日 ，HOLIDAY 节假日")
    private String dayType;

    /**
     * 是否为最新
     */
    @ApiModelProperty(value = "是否为最新")
    private Integer isLatest;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String remark;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private BigDecimal orderby;



    public Boolean areWeekend() {
        return StringUtils.equals(dayType, CalendarDayTypeEnum.WEEKEND.getCode());
    }

    public Boolean areHOLIDAY() {
        return StringUtils.equals(dayType, CalendarDayTypeEnum.HOLIDAY.getCode());
    }

    public Boolean arePRESENT() {
        return StringUtils.equals(dayType, CalendarDayTypeEnum.PRESENT.getCode());
    }
}