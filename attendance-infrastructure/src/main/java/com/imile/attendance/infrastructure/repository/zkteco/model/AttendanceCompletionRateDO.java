package com.imile.attendance.infrastructure.repository.zkteco.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 打卡信息统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_completion_rate")
public class AttendanceCompletionRateDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 日期
     */
    private Long dayId;
    /**
     * 部门id
     */
    private Long deptId;


    /**
     * 员工类型
     */
    private String employeeType;

    /**
     * 中控人数
     */
    private Integer zkAttendanceNum;

    /**
     * 非司机的活跃人数
     */
    private Integer activeNum;

    /**
     * 注册率
     */
    private BigDecimal activeRate;

    /**
     * 录入系统的人数
     */
    private Integer registrationNum;

    /**
     * 当天实际应考勤人数
     */
    private Integer actualAttendanceNum;

    /**
     * 脸部/掌纹注册率
     */
    private BigDecimal registrationRate;

    /**
     * 当天实际打卡人数
     */
    private Integer punchNum;

    /**
     * 打卡率
     */
    private BigDecimal punchRate;

}