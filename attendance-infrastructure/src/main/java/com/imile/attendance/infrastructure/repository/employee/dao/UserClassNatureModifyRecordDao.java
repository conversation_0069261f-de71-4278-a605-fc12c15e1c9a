package com.imile.attendance.infrastructure.repository.employee.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserClassNatureModifyRecordDO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/10
 */
public interface UserClassNatureModifyRecordDao extends IService<UserClassNatureModifyRecordDO> {

    List<UserClassNatureModifyRecordDO> selectByUserId(Long userId);

    UserClassNatureModifyRecordDO selectLatestByUserId(Long userId);

}
