package com.imile.attendance.infrastructure.repository.vacation.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigRangDao;
import com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveConfigRangMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.query.LeaveConfigRangQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description 假期范围 数据库操作实现类
 */
@Component
@RequiredArgsConstructor
public class CompanyLeaveConfigRangDaoImpl extends ServiceImpl<CompanyLeaveConfigRangMapper, CompanyLeaveConfigRangDO> implements CompanyLeaveConfigRangDao {

    @Override
    public List<CompanyLeaveConfigRangDO> getLeaveConfigRangList(LeaveConfigRangQuery leaveConfigRangQuery) {
        if (ObjectUtil.isNull(leaveConfigRangQuery)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigRangDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ObjectUtil.isNotNull(leaveConfigRangQuery.getLeaveId()), CompanyLeaveConfigRangDO::getLeaveId, leaveConfigRangQuery.getLeaveId());
        queryWrapper.eq(ObjectUtil.isNotNull(leaveConfigRangQuery.getRangeType()), CompanyLeaveConfigRangDO::getRangeType, leaveConfigRangQuery.getRangeType());
        queryWrapper.eq(CompanyLeaveConfigRangDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<CompanyLeaveConfigRangDO> selectByLeaveId(List<Long> allCompanyLeaveConfigIdList) {
        if (CollectionUtils.isEmpty(allCompanyLeaveConfigIdList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigRangDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CompanyLeaveConfigRangDO::getLeaveId, allCompanyLeaveConfigIdList);
        queryWrapper.eq(CompanyLeaveConfigRangDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    /**
     * 获取假期配置范围
     *
     * @param userCodeList 用户code
     * @return 假期配置范围
     */
    @Override
    public List<CompanyLeaveConfigRangDO> selectRangByUserCode(List<String> userCodeList) {
        if (CollectionUtils.isEmpty(userCodeList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveConfigRangDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CompanyLeaveConfigRangDO::getUserCode, userCodeList);
        queryWrapper.eq(CompanyLeaveConfigRangDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerCompanyLeaveConfigRang(List<CompanyLeaveConfigRangDO> addLeaveRang, List<CompanyLeaveConfigRangDO> updateLeaveRang) {
        if (CollectionUtils.isNotEmpty(addLeaveRang)) {
            saveBatch(addLeaveRang);
        }
        if (CollectionUtils.isNotEmpty(updateLeaveRang)) {
            updateBatchById(updateLeaveRang);
        }
    }
}
