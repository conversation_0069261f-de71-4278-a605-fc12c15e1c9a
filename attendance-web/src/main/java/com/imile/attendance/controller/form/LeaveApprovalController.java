package com.imile.attendance.controller.form;

import com.imile.attendance.annon.NoAttendanceLoginAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.biz.leave.LeaveApprovalService;
import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.param.AttendanceApprovalInfoParam;
import com.imile.attendance.form.biz.leave.param.LeaveAddParam;
import com.imile.attendance.form.param.RevokeAddParam;
import com.imile.attendance.form.vo.ApprovalResultVO;
import com.imile.attendance.form.biz.leave.vo.LeaveFormInfoExportVO;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.framework.redis.client.ImileRedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @description: HR考勤请假审批入口
 * @author: han.wang
 * @createDate: 2025-5-7
 */
@Slf4j
@RestController
@RequestMapping("/attendance/approval")
public class LeaveApprovalController extends BaseController {
    @Resource
    private LeaveApprovalService leaveApprovalService;
    @Resource
    private ImileRedisClient redissonClient;

    private static final String SYNC_LOCK = "HRMS:LOCK:ATTENDANCE_SYNC:";

    /**
     * 请假申请(点击新增按钮调用 新增/暂存)
     */
    @PostMapping("/leave/add")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> leaveAdd(@RequestBody @Validated LeaveAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(),
                    ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = leaveApprovalService.leaveAdd(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 请假申请(暂存后更新/驳回后更新)
     */
    @PostMapping("/leave/update")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> leaveUpdate(@RequestBody @Validated LeaveAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = leaveApprovalService.leaveUpdate(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 请假-撤销申请
     */
    @PostMapping("/leave/revoke/add")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> leaveRevokeAdd(@RequestBody @Validated RevokeAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = leaveApprovalService.leaveRevokeAdd(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 请假申请预览
     */
    @PostMapping("/leave/preview")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<List<ApprovalDetailStepRecordDTO>> leavePreview(@RequestBody @Validated LeaveAddParam param) {
        List<ApprovalDetailStepRecordDTO> stepRecordDTOS = leaveApprovalService.leavePreview(param);
        return Result.ok(stepRecordDTOS);
    }

    /**
     * 请假-撤销申请预览
     */
    @PostMapping("/leave/revoke/preview")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<List<ApprovalDetailStepRecordDTO>> leaveRevokePreview(@RequestBody @Validated RevokeAddParam param) {
        List<ApprovalDetailStepRecordDTO> stepRecordDTOS = leaveApprovalService.leaveRevokePreview(param);
        return Result.ok(stepRecordDTOS);
    }

    /**
     * 请假单据信息导出
     */
    @PostMapping("/export")
    public Result<PaginationResult<LeaveFormInfoExportVO>> listExport(HttpServletRequest request,
                                                                      AttendanceApprovalInfoParam param) {
        setExcelCallBackParam(request, param);
        PaginationResult<LeaveFormInfoExportVO> res = leaveApprovalService.listExport(param);
        return Result.ok(res);
    }
}
