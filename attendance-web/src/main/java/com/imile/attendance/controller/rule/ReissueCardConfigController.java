package com.imile.attendance.controller.rule;

import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigPageQuery;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigQuery;
import com.imile.attendance.rule.ReissueCardConfigService;
import com.imile.attendance.rule.command.ReissueCardConfigAddCommand;
import com.imile.attendance.rule.command.ReissueCardConfigStatusSwitchCommand;
import com.imile.attendance.rule.command.ReissueCardConfigUpdateCommand;
import com.imile.attendance.rule.dto.ReissueCardConfigDetailDTO;
import com.imile.attendance.rule.dto.ReissueCardConfigPageDTO;
import com.imile.attendance.rule.dto.RuleConfigChangeCheckDTO;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.dto.UpdateRuleReflectResult;
import com.imile.attendance.rule.query.RuleConfigUserQuery;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 补卡规则
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description
 */
@RestController
@RequestMapping("/reissueCard/config")
public class ReissueCardConfigController {

    @Resource
    private ReissueCardConfigService reissueCardConfigService;


    /**
     * 分页查询
     */
    @PostMapping("/page")
    public Result<PaginationResult<ReissueCardConfigPageDTO>> page(@RequestBody ReissueCardConfigPageQuery query){
        return Result.ok(reissueCardConfigService.pageReissueCardConfigList(query));
    }

    /**
     * 查询详情
     */
    @GetMapping("/detail")
    public Result<ReissueCardConfigDetailDTO> detail(String configNo){
        return Result.ok(reissueCardConfigService.queryConfigDetail(configNo));
    }

    /**
     * 分页查询补卡配置的用户列表
     */
    @PostMapping("/page/applyUser")
    public Result<PaginationResult<RuleConfigUserInfoDTO>> pageApplyUser(@RequestBody RuleConfigUserQuery query){
        return Result.ok(reissueCardConfigService.pageConfigUserList(query));
    }

    /**
     * 添加
     */
    @PostMapping("/add")
    public Result<RuleConfigChangeCheckDTO> add(@RequestBody ReissueCardConfigAddCommand addCommand){
        return Result.ok(reissueCardConfigService.add(addCommand));
    }

    /**
     * 检查补卡规则更新的影响范围，分析规则变更受影响的用户数量
     */
    @PostMapping("/checkUpdateRule")
    public Result<UpdateRuleReflectResult> checkUpdateRule(@RequestBody @Validated ReissueCardConfigUpdateCommand updateCommand){
        return Result.ok(reissueCardConfigService.checkUpdateRule(updateCommand));
    }

    /**
     * 更新
     */
    @PostMapping("/update")
    public Result<RuleConfigChangeCheckDTO> update(@RequestBody @Validated ReissueCardConfigUpdateCommand updateCommand){
        return Result.ok(reissueCardConfigService.update(updateCommand));
    }

    /**
     * 检查补卡规则状态切换的影响范围,分析状态变更（启用/停用）对用户范围的影响
     */
    @PostMapping("/checkStatusSwitch")
    public Result<UpdateRuleReflectResult> checkStatusSwitch(@RequestBody @Validated ReissueCardConfigStatusSwitchCommand statusSwitchCommand){
        return Result.ok(reissueCardConfigService.checkStatusSwitch(statusSwitchCommand));
    }

    /**
     * 启用或停用
     */
    @PostMapping("/statusSwitch")
    public Result<RuleConfigChangeCheckDTO> statusSwitch(@RequestBody @Validated ReissueCardConfigStatusSwitchCommand statusSwitchCommand){
        return Result.ok(reissueCardConfigService.statusSwitch(statusSwitchCommand));
    }


}
