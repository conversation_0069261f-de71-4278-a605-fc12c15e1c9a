package com.imile.attendance.controller.calendar;

import com.imile.attendance.calendar.application.CalendarConfigApplicationService;
import com.imile.attendance.calendar.command.CalendarLegalLeaveConfigAddCommand;
import com.imile.attendance.calendar.query.CalendarLegalLeaveConfigDetailQuery;
import com.imile.attendance.calendar.query.CalendarLegalLeaveConfigListQuery;
import com.imile.attendance.calendar.vo.LegalLeaveConfigDetailVO;
import com.imile.attendance.calendar.vo.LegalLeaveConfigVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 日历国家法定假期记录表控制层
 */
@RestController
@RequestMapping("/legal/leave/config")
public class CalendarLegalLeaveConfigController {

    @Resource
    private CalendarConfigApplicationService calendarConfigApplicationService;


    /**
     * 保存国家法定假期记录
     *
     * @param legalLeaveConfigAddCommand 保存参数
     * @return boolean
     */
    @PostMapping("save")
    @Deprecated
    public Result<Boolean> save(@Validated @RequestBody CalendarLegalLeaveConfigAddCommand legalLeaveConfigAddCommand) {
        calendarConfigApplicationService.addCalendarLegalLeaveConfig(legalLeaveConfigAddCommand);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 查询国家法定假期记录 分页
     *
     * @param listQuery 查询参数
     * @return 分页结果
     */
    @PostMapping("list")
    @Deprecated
    public Result<PaginationResult<LegalLeaveConfigVO>> list(@RequestBody @Valid CalendarLegalLeaveConfigListQuery listQuery) {
        PaginationResult<LegalLeaveConfigVO> legalLeaveConfigList = calendarConfigApplicationService.queryLegalLeave(listQuery);
        return Result.ok(legalLeaveConfigList);
    }

    /**
     * 法定假期详情
     * @param detailQuery 查询参数
     * @return  LegalLeaveConfigDetailVO
     */
    @PostMapping("detail")
    public Result<LegalLeaveConfigDetailVO> detail(@RequestBody @Valid CalendarLegalLeaveConfigDetailQuery detailQuery) {
        LegalLeaveConfigDetailVO legalLeaveConfigDetail = calendarConfigApplicationService.queryLegalLeaveDetail(detailQuery);
        return Result.ok(legalLeaveConfigDetail);
    }
}
