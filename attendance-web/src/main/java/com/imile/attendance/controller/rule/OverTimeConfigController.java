package com.imile.attendance.controller.rule;

import com.imile.attendance.infrastructure.repository.rule.query.OverTimeConfigPageQuery;
import com.imile.attendance.infrastructure.repository.rule.query.OverTimeConfigQuery;
import com.imile.attendance.rule.OverTimeConfigService;
import com.imile.attendance.rule.command.OverTimeConfigAddCommand;
import com.imile.attendance.rule.command.OverTimeConfigStatusSwitchCommand;
import com.imile.attendance.rule.command.OverTimeConfigUpdateCommand;
import com.imile.attendance.rule.dto.OverTimeConfigDetailDTO;
import com.imile.attendance.rule.dto.OverTimeConfigPageDTO;
import com.imile.attendance.rule.dto.RuleConfigChangeCheckDTO;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.dto.UpdateRuleReflectResult;
import com.imile.attendance.rule.query.RuleConfigUserQuery;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 加班规则
 * <AUTHOR> chen
 * @Date 2025/4/17 
 * @Description
 */
@RestController
@RequestMapping("/overTime/config")
public class OverTimeConfigController {

    @Resource
    private OverTimeConfigService overTimeConfigService;

    /**
     * 分页查询
     */
    @PostMapping("/page")
    public Result<PaginationResult<OverTimeConfigPageDTO>> page(@RequestBody OverTimeConfigPageQuery query) {
        return Result.ok(overTimeConfigService.page(query));
    }

    /**
     * 查询加班配置详情
     * @param configNo 配置编码
     * @return 加班配置详情
     */
    @GetMapping("/detail")
    public Result<OverTimeConfigDetailDTO> detail(String configNo) {
        return Result.ok(overTimeConfigService.queryConfigDetail(configNo));
    }

    /**
     * 分页查询加班配置的用户列表
     * @param query 查询参数
     * @return 分页结果
     */
    @PostMapping("/page/applyUser")
    public Result<PaginationResult<RuleConfigUserInfoDTO>> pageApplyUser(@RequestBody RuleConfigUserQuery query) {
        return Result.ok(overTimeConfigService.pageConfigUserList(query));
    }


    /**
     * 添加
     */
    @PostMapping("/add")
    public Result<RuleConfigChangeCheckDTO> add(@RequestBody OverTimeConfigAddCommand addCommand) {
        return Result.ok(overTimeConfigService.add(addCommand));
    }

    /**
     * 检查加班规则更新的影响范围，分析规则变更受影响的用户数量
     */
    @PostMapping("/checkUpdateRule")
    public Result<UpdateRuleReflectResult> checkUpdateRule(@RequestBody @Validated OverTimeConfigUpdateCommand updateCommand) {
        return Result.ok(overTimeConfigService.checkUpdateRule(updateCommand));
    }

    /**
     * 更新
     */
    @PostMapping("/update")
    public Result<RuleConfigChangeCheckDTO> update(@RequestBody @Validated OverTimeConfigUpdateCommand updateCommand) {
        return Result.ok(overTimeConfigService.update(updateCommand));
    }

    /**
     * 检查加班规则状态切换的影响范围,分析状态变更（启用/停用）对用户范围的影响
     */
    @PostMapping("/checkStatusSwitch")
    public Result<UpdateRuleReflectResult> checkStatusSwitch(@RequestBody @Validated OverTimeConfigStatusSwitchCommand statusSwitchCommand) {
        return Result.ok(overTimeConfigService.checkStatusSwitch(statusSwitchCommand));
    }

    /**
     * 启用或停用
     */
    @PostMapping("/statusSwitch")
    public Result<RuleConfigChangeCheckDTO> statusSwitch(@RequestBody @Validated OverTimeConfigStatusSwitchCommand statusSwitchCommand) {
        return Result.ok(overTimeConfigService.statusSwitch(statusSwitchCommand));
    }
}
