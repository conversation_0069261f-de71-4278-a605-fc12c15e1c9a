package com.imile.attendance.controller.vacation;

import com.imile.attendance.controller.BaseController;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveTypeQuery;
import com.imile.attendance.vacation.CompanyLeaveTypeService;
import com.imile.attendance.vacation.dto.CompanyLeaveTypeDTO;
import com.imile.attendance.vacation.mapstruct.CompanyLeaveConfigTypeMapstruct;
import com.imile.attendance.vacation.vo.CompanyLeaveTypeVO;
import com.imile.common.result.Result;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 假期类型控制层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/2/17
 */
@Slf4j
@RestController
@RequestMapping("/leave/type")
public class CompanyLeaveTypeController extends BaseController {

    @Resource
    private CompanyLeaveTypeService companyLeaveTypeService;

    /**
     * 假期类型条件查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    @PostMapping("/queryByCondition")
    public Result<List<CompanyLeaveTypeVO>> queryByCondition(@RequestBody @Validated CompanyLeaveTypeQuery query) {
        List<CompanyLeaveTypeDTO> companyLeaveTypeDTO = companyLeaveTypeService.queryByCondition(query);
        List<CompanyLeaveTypeVO> resultVO = CompanyLeaveConfigTypeMapstruct.INSTANCE.toConfigTypeVO(companyLeaveTypeDTO);
        return Result.ok(resultVO);
    }

}
