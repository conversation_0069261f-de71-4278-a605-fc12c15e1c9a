package com.imile.attendance.shift;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> chen
 * @Date 2025/5/16
 * @Description 测试UserShiftConfigManage类的方法
 */
public class UserShiftConfigManageTest extends BaseTest {

    @Resource
    private UserShiftConfigManage userShiftConfigManage;

    /**
     * 测试根据用户ID列表和日期ID（包含之后日期）获取班次配置的功能
     */
    @Test
    public void testGetShiftConfigByUserIdsAndDayIdIncludeAfter() {
        // 准备测试数据
        List<Long> userIds = Arrays.asList(1103011271906697217L, 1197567969644118016L);
        Long dayId = 20250601L; // 2025年6月1日

        // 执行测试
        Map<Long, List<UserShiftConfigDO>> result = userShiftConfigManage.getShiftConfigByUserIdsAndDayIdIncludeAfter(userIds, dayId);

        // 验证结果
        Optional.ofNullable(result).ifPresent(map -> {
            System.out.println("获取到的用户排班配置数量: " + map.size());
            map.forEach((userId, configs) -> {
                System.out.println("用户ID: " + userId + ", 排班配置数量: " + configs.size());
                if (!configs.isEmpty()) {
                    System.out.println("第一条排班配置: " + configs.get(0));
                }
            });
        });

        // 测试边界情况：空用户ID列表
        Map<Long, List<UserShiftConfigDO>> emptyUserResult = userShiftConfigManage.getShiftConfigByUserIdsAndDayIdIncludeAfter(Arrays.asList(), dayId);
        System.out.println("空用户ID列表结果: " + (emptyUserResult.isEmpty() ? "空Map" : "非空Map"));

        // 测试边界情况：null日期ID
        Map<Long, List<UserShiftConfigDO>> nullDayIdResult = userShiftConfigManage.getShiftConfigByUserIdsAndDayIdIncludeAfter(userIds, null);
        System.out.println("null日期ID结果: " + (nullDayIdResult.isEmpty() ? "空Map" : "非空Map"));
    }

    /**
     * 测试根据用户ID列表、班次ID列表和日期ID（包含之后日期）获取班次配置的功能
     */
    @Test
    public void testGetShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter() {
        // 准备测试数据
        List<Long> userIds = Arrays.asList(1345677607882084353L, 1343865659180040192L);
        List<Long> classIds = Arrays.asList(1346616738934075393L, 1319018537230155777L); // 示例班次ID
        Long dayId = 20250601L; // 2025年6月1日

        // 执行测试
        Map<Long, List<UserShiftConfigDO>> result = userShiftConfigManage.getShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter(userIds, classIds, dayId);

        // 验证结果
        Optional.ofNullable(result).ifPresent(map -> {
            System.out.println("获取到的用户排班配置数量: " + map.size());
            map.forEach((userId, configs) -> {
                System.out.println("用户ID: " + userId + ", 排班配置数量: " + configs.size());
                if (!configs.isEmpty()) {
                    System.out.println("第一条排班配置: " + configs.get(0));
                }
            });
        });

        // 测试边界情况：空用户ID列表
        Map<Long, List<UserShiftConfigDO>> emptyUserResult = userShiftConfigManage.getShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter(Arrays.asList(), classIds, dayId);
        System.out.println("空用户ID列表结果: " + (emptyUserResult.isEmpty() ? "空Map" : "非空Map"));

        // 测试边界情况：空班次ID列表
        Map<Long, List<UserShiftConfigDO>> emptyClassResult = userShiftConfigManage.getShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter(userIds, Arrays.asList(), dayId);
        System.out.println("空班次ID列表结果: " + (emptyClassResult.isEmpty() ? "空Map" : "非空Map"));

        // 测试边界情况：null日期ID
        Map<Long, List<UserShiftConfigDO>> nullDayIdResult = userShiftConfigManage.getShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter(userIds, classIds, null);
        System.out.println("null日期ID结果: " + (nullDayIdResult.isEmpty() ? "空Map" : "非空Map"));
    }
}
