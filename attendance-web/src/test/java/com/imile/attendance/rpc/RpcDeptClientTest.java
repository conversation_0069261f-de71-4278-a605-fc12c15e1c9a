package com.imile.attendance.rpc;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.hrms.RpcDeptClient;
import com.imile.hrms.api.organization.dto.DeptDTO;
import com.imile.hrms.api.organization.query.DeptConditionParam;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description
 */
public class RpcDeptClientTest extends BaseTest {

    @Resource
    private RpcDeptClient rpcDeptClient;


    @Test
    public void listDeptByCondition(){
        DeptConditionParam deptConditionParam = new DeptConditionParam();
        deptConditionParam.setIdList(Arrays.asList(1133L,1134L,1031050L,1097580729203688059L));
        List<DeptDTO> deptDTOS = rpcDeptClient.listDeptByCondition(deptConditionParam);
        System.out.println(deptDTOS);
    }
}
