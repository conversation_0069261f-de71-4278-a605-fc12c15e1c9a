package com.imile.attendance.infrastructure.logRecord;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.common.enums.StatusEnum;
import com.imile.idwork.IdWorkerUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
@Slf4j
public class LogRecordServiceTest extends BaseTest {

    @Resource
    private LogRecordService logRecordService;

    @After
    public void delay() {
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDiffObj() {
        CalendarConfigDO oldObj = new CalendarConfigDO();
        oldObj.setId(1L);
        oldObj.setIsLatest(1);
        oldObj.setDeptIds("2,3");
        oldObj.setAttendanceConfigName("测试日历-1-update2");

        CalendarConfigDO newObj = new CalendarConfigDO();
        newObj.setId(IdWorkerUtil.getId());
        newObj.setIsLatest(0);
        newObj.setDeptIds("2,3,5");
        newObj.setAttendanceConfigName("测试日历-1-update3");

        logRecordService.recordObjectChange(newObj, oldObj,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.UPDATE)
                        .operationType(OperationTypeEnum.CALENDAR_UPDATE.getCode())
                        .country("CHN")
                        .bizName(oldObj.getAttendanceConfigName())
                        .build()
        );
    }

    @Test
    public void testDiffObj2() {
        CalendarConfigDO oldObj = new CalendarConfigDO();
        oldObj.setId(2L);
        oldObj.setAttendanceConfigName("测试日历-2");

        CalendarConfigDO newObj = new CalendarConfigDO();
        newObj.setId(2L);
        newObj.setAttendanceConfigName("测试日历-2-update");

        logRecordService.recordObjectChange(
                newObj, oldObj,
                LogRecordOptions.builder()
                        .operationType(OperationTypeEnum.CALENDAR_UPDATE.getCode())
                        .country("CHN")
                        .bizName(oldObj.getAttendanceConfigName())
                        .build()
        );
    }

    @Test
    public void tesEnable() {
        CalendarConfigDO model = new CalendarConfigDO();
        model.setId(5L);
        model.setStatus(StatusEnum.ACTIVE.getCode());
        model.setCountry("CHN");
        model.setAttendanceConfigName("测试日历-5");

        logRecordService.recordOperation(
                model,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ACTIVE)
                        .operationType(OperationTypeEnum.CALENDAR_ENABLE.getCode())
                        .country(model.getCountry())
                        .bizName(model.getAttendanceConfigName())
                        .build()
        );
    }

    @Test
    public void tesDisable() {
        CalendarConfigDO model = new CalendarConfigDO();
        model.setId(7L);
        model.setStatus(StatusEnum.DISABLED.getCode());
        model.setCountry("CHN");
        model.setAttendanceConfigName("测试日历-7");

        logRecordService.recordOperation(
                model,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.DISABLE)
                        .operationType(OperationTypeEnum.CALENDAR_DISABLE.getCode())
                        .country(model.getCountry())
                        .bizName(model.getAttendanceConfigName())
                        .build()
        );
    }

    @Test
    public void testaddLog() {
        CalendarConfigDO newObj = new CalendarConfigDO();
        newObj.setId(8L);
        newObj.setCountry("CHN");
        newObj.setAttendanceConfigName("测试日历-8");

        logRecordService.recordOperation(
                newObj,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ADD)
                        .operationType(OperationTypeEnum.CALENDAR_ADD.getCode())
                        .country(newObj.getCountry())
                        .bizName(newObj.getAttendanceConfigName())
                        .build()
        );
    }

}
