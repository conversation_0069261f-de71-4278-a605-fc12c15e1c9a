package com.imile.attendance.infrastructure.repository.rule;

import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigDao;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.OverTimeConfigQuery;
import com.imile.attendance.base.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 测试 OverTimeConfigDaoImpl 的 listByQuery 方法，重点验证 deptIds 查询逻辑
 */
public class OverTimeConfigDaoImplTest extends BaseTest {

    @Resource
    private OverTimeConfigDao overTimeConfigDao;

    @Test
    public void testListByQueryWithDeptIds() {
        OverTimeConfigQuery query = new OverTimeConfigQuery();
        // 假设数据库中存在 dept_ids 字段包含 1001, 1002 的数据
//        query.setDeptIds(Arrays.asList(1001L, 1002L));
        query.setDeptId(1097580729203688518L);
        List<OverTimeConfigDO> result = overTimeConfigDao.listByQuery(query);
        System.out.println("查询结果：" + result);
    }
}