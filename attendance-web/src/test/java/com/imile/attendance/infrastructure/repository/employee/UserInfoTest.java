package com.imile.attendance.infrastructure.repository.employee;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/27 
 * @Description
 */
public class UserInfoTest extends BaseTest {

    @Resource
    private UserInfoDao userInfoDao;

    @Test
    public void test() {
        List<UserInfoDO> userInfoDOS = userInfoDao.listByPage(1, 10);
        System.out.println(userInfoDOS);
    }
}
