package com.imile.attendance.common;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.base.dao.MqFailRecordDao;
import com.imile.attendance.infrastructure.repository.base.model.MqFailRecordDO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> chen
 * @Date 2025/4/29 
 * @Description
 */
public class MqFailRecordTest extends BaseTest {

    @Resource
    private MqFailRecordDao mqFailRecordDao;

    @Test
    public void testMqFailRecord() {
        List<MqFailRecordDO> list = mqFailRecordDao.list();
        Optional.ofNullable(list).ifPresent(System.out::println);
    }
}
