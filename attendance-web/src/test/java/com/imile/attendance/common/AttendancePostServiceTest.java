package com.imile.attendance.common;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> chen
 * @Date 2025/4/23 
 * @Description
 */
public class AttendancePostServiceTest extends BaseTest {

    @Resource
    private AttendancePostService postService;

    @Test
    public void listByPostId(){
        List<AttendancePost> attendancePosts = postService.listByPostList(Arrays.asList(1028L, 1029L));
        Optional.ofNullable(attendancePosts).ifPresent(System.out::println);
    }

    @Test
    public void getByPostId(){
        System.out.println(postService.getByPostId(1133L));
    }
}
