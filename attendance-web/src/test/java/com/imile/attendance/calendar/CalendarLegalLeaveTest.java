package com.imile.attendance.calendar;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarLegalLeaveConfigDao;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/4/23 
 * @Description
 */
public class CalendarLegalLeaveTest extends BaseTest {

    @Resource
    private CalendarLegalLeaveConfigDao calendarLegalLeaveConfigDao;

    @Test
    public void testgetByCalendarConfigIdAndDayId(){
        System.out.println(calendarLegalLeaveConfigDao.getByCalendarConfigIdAndDayId(1319018537230155776L, 20250402L));
        System.out.println(calendarLegalLeaveConfigDao.getByCalendarConfigIdAndDayId(1319018537230155776L, 20250303L));
        System.out.println(calendarLegalLeaveConfigDao.getByCalendarConfigIdAndDayId(1319018537230155776L, 20250305L));
    }
}
