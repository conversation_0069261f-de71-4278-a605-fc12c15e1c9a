package com.imile.attendance.util;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19 
 * @Description
 */
public class HotSwapTest {

    public static void main(String[] args) {
        while (true){
            test();
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private static void test(){
        System.out.println("test hello");
    }
}
