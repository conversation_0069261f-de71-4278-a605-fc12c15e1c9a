package com.imile.attendance.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.imile.attendance.base.BaseTest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.Test;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * JsonUtils工具类的单元测试
 * <AUTHOR> Assistant
 * @Date 2025/4/4
 * @Description
 */
public class JsonUtilsTest extends BaseTest {

    @Test
    public void testReadWithTypeReference() {
        // 测试读取List<String>
        String jsonArray = "[\"value1\", \"value2\", \"value3\"]";
        List<String> stringList = JsonUtils.read(jsonArray, new TypeReference<List<String>>() {});
        assertNotNull(stringList);
        assertEquals(3, stringList.size());
        assertEquals("value1", stringList.get(0));
        assertEquals("value2", stringList.get(1));
        assertEquals("value3", stringList.get(2));

        // 测试读取Map
        String jsonMap = "{\"key1\": \"value1\", \"key2\": \"value2\"}";
        Map<String, String> map = JsonUtils.read(jsonMap, new TypeReference<Map<String, String>>() {});
        assertNotNull(map);
        assertEquals(2, map.size());
        assertEquals("value1", map.get("key1"));
        assertEquals("value2", map.get("key2"));

        // 测试空字符串
        assertNull(JsonUtils.read("", new TypeReference<List<String>>() {}));
        assertNull(JsonUtils.read(null, new TypeReference<List<String>>() {}));
    }

    @Test
    public void testReadWithClass() {
        // 测试读取简单对象
        String json = "{\"name\":\"Test User\",\"age\":30}";
        TestUser user = JsonUtils.read(json, TestUser.class);
        assertNotNull(user);
        assertEquals("Test User", user.getName());
        assertEquals(30, user.getAge());

        // 测试空字符串
        assertNull(JsonUtils.read("", TestUser.class));
        assertNull(JsonUtils.read(null, TestUser.class));
    }

    @Test
    public void testWrite() {
        // 测试写入简单对象
        TestUser user = new TestUser("Test User", 30);
        String json = JsonUtils.write(user);
        assertNotNull(json);
        assertTrue(json.contains("\"name\":\"Test User\""));
        assertTrue(json.contains("\"age\":30"));

        // 测试写入List
        List<String> stringList = Arrays.asList("value1", "value2", "value3");
        String jsonArray = JsonUtils.write(stringList);
        assertNotNull(jsonArray);
        assertEquals("[\"value1\",\"value2\",\"value3\"]", jsonArray);

        // 测试null值
        assertNull(JsonUtils.write(null));
    }

    @Test
    public void testWriteToString() {
        // 测试对象转字符串
        TestUser user = new TestUser("Test User", 30);
        String result = JsonUtils.writeToString(user);
        assertNotNull(result);
        assertTrue(result.contains("name=Test User"));
        assertTrue(result.contains("age=30"));

        // 测试null值
        assertEquals("", JsonUtils.writeToString(null));
    }

    @Test
    public void testWriteBytes() {
        // 测试对象转字节数组
        TestUser user = new TestUser("Test User", 30);
        byte[] bytes = JsonUtils.writeBytes(user);
        assertNotNull(bytes);
        assertTrue(bytes.length > 0);

        // 测试null值
        byte[] emptyBytes = JsonUtils.writeBytes(null);
        assertNotNull(emptyBytes);
        assertEquals(0, emptyBytes.length);
    }

    @Test
    public void testConvertWithTypeReference() {
        // 测试对象转换
        TestUser user = new TestUser("Test User", 30);
        Map<String, Object> map = JsonUtils.convert(user, new TypeReference<Map<String, Object>>() {});
        assertNotNull(map);
        assertEquals("Test User", map.get("name"));
        assertEquals(30, map.get("age"));
    }

    @Test
    public void testConvertWithClass() {
        // 创建一个Map并转换为TestUser对象
        HashMap<String, Object> map = new HashMap<>();
        map.put("name", "Test User");
        map.put("age", 30);
        TestUser user = JsonUtils.convert(map, TestUser.class);
        assertNotNull(user);
        assertEquals("Test User", user.getName());
        assertEquals(30, user.getAge());
    }

    @Test
    public void testDateTimeSerialization() {
        // 测试LocalDateTime序列化
        TestDateTimeObject dateTimeObj = new TestDateTimeObject();
        dateTimeObj.setDateTime(LocalDateTime.of(2023, 5, 15, 10, 30, 0));
        String json = JsonUtils.write(dateTimeObj);
        assertNotNull(json);
        assertTrue(json.contains("\"dateTime\":\"2023-05-15 10:30:00\""));

        // 测试LocalTime序列化
        TestTimeObject timeObj = new TestTimeObject();
        timeObj.setTime(LocalTime.of(10, 30, 0));
        json = JsonUtils.write(timeObj);
        assertNotNull(json);
        assertTrue(json.contains("\"time\":\"10:30:00\""));

        // 测试ZonedDateTime序列化
        TestZonedDateTimeObject zonedDateTimeObj = new TestZonedDateTimeObject();
        zonedDateTimeObj.setZonedDateTime(ZonedDateTime.of(2023, 5, 15, 10, 30, 0, 0, ZoneId.of("UTC")));
        json = JsonUtils.write(zonedDateTimeObj);
        assertNotNull(json);
        assertTrue(json.contains("\"zonedDateTime\":\"2023-05-15T10:30:00Z\""));
    }

    @Test
    public void testDateTimeDeserialization() {
        // 测试LocalDateTime反序列化
        String json = "{\"dateTime\":\"2023-05-15 10:30:00\"}";
        TestDateTimeObject dateTimeObj = JsonUtils.read(json, TestDateTimeObject.class);
        assertNotNull(dateTimeObj);
        assertNotNull(dateTimeObj.getDateTime());
        assertEquals(2023, dateTimeObj.getDateTime().getYear());
        assertEquals(5, dateTimeObj.getDateTime().getMonthValue());
        assertEquals(15, dateTimeObj.getDateTime().getDayOfMonth());
        assertEquals(10, dateTimeObj.getDateTime().getHour());
        assertEquals(30, dateTimeObj.getDateTime().getMinute());

        // 测试LocalDateTime反序列化 - 简化格式
        json = "{\"dateTime\":\"2023-05-15\"}";
        dateTimeObj = JsonUtils.read(json, TestDateTimeObject.class);
        assertNotNull(dateTimeObj);
        assertNotNull(dateTimeObj.getDateTime());
        assertEquals(2023, dateTimeObj.getDateTime().getYear());
        assertEquals(5, dateTimeObj.getDateTime().getMonthValue());
        assertEquals(15, dateTimeObj.getDateTime().getDayOfMonth());
        assertEquals(0, dateTimeObj.getDateTime().getHour());
        assertEquals(0, dateTimeObj.getDateTime().getMinute());

        // 测试LocalTime反序列化
        json = "{\"time\":\"10:30:00\"}";
        TestTimeObject timeObj = JsonUtils.read(json, TestTimeObject.class);
        assertNotNull(timeObj);
        assertNotNull(timeObj.getTime());
        assertEquals(10, timeObj.getTime().getHour());
        assertEquals(30, timeObj.getTime().getMinute());
        assertEquals(0, timeObj.getTime().getSecond());
    }

    @Test
    public void testFlexibleListDeserializer() {
        // 测试字符串列表
        String json = "{\"stringList\":\"value1,value2,value3\"}";
        TestListObject listObj = JsonUtils.read(json, TestListObject.class);
        assertNotNull(listObj);
        assertNotNull(listObj.getStringList());
        assertEquals(3, listObj.getStringList().size());
        assertEquals("value1", listObj.getStringList().get(0));
        assertEquals("value2", listObj.getStringList().get(1));
        assertEquals("value3", listObj.getStringList().get(2));

        // 测试整数列表
        json = "{\"integerList\":\"1,2,3\"}";
        listObj = JsonUtils.read(json, TestListObject.class);
        assertNotNull(listObj);
        assertNotNull(listObj.getIntegerList());
        assertEquals(3, listObj.getIntegerList().size());
        assertEquals(Integer.valueOf(1), listObj.getIntegerList().get(0));
        assertEquals(Integer.valueOf(2), listObj.getIntegerList().get(1));
        assertEquals(Integer.valueOf(3), listObj.getIntegerList().get(2));
    }

    // 测试用的内部类
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TestUser {
        private String name;
        private int age;
    }

    @Data
    public static class TestDateTimeObject {
        private LocalDateTime dateTime;
    }

    @Data
    public static class TestTimeObject {
        private LocalTime time;
    }

    @Data
    public static class TestZonedDateTimeObject {
        private ZonedDateTime zonedDateTime;
    }

    @Data
    public static class TestListObject {
        private List<String> stringList;
        private List<Integer> integerList;
    }
}
