package com.imile.attendance.rule;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> chen
 * @Date 2025/5/22 
 * @Description
 */
public class OverTimeConfigTest extends BaseTest {

    @Resource
    private OverTimeConfigDao overTimeConfigDao;
    @Resource
    private OverTimeConfigRangeDao overTimeConfigRangeDao;

    @Test
    public void testListOnJobNoDriverUsersExcludeConfigured(){
        System.out.println("=============single country==================");

        RuleRangeUserQuery singleCountryQuery = RuleRangeUserQuery.builder()
                .country("ITA")
                .build();
        List<UserInfoDO> userInfoDOS = overTimeConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(singleCountryQuery);
        Optional.ofNullable(userInfoDOS)
                .ifPresent(i->System.out.println(i.size()));

    }
}
